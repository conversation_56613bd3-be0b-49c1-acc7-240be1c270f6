#include <jni.h>
#include <string>
#include <android/native_window.h>
#include <android/native_window_jni.h>
#include "include/libyuv.h"
#include "libyuv_utils.h"
#include "log.h"

#define PREVIEW_RGBA_SIZE 640 * 480 * 4

char preview_bg_rgba[PREVIEW_RGBA_SIZE] = {0};
char preview_front_rgba[PREVIEW_RGBA_SIZE] = {0};

extern "C"
JNIEXPORT jlong JNICALL
Java_com_imilab_yuv_MediaYUV_initSurface(JNIEnv *env, jobject clazz, jobject surface) {
    LOGD("Java_com_imilab_yuv_MediaYUV_initSurface");
    ANativeWindow *nwin = ANativeWindow_fromSurface(env, surface);
    return (jlong) nwin;
}

extern "C"
JNIEXPORT void JNICALL
Java_com_imilab_yuv_MediaYUV_releaseSurface(JNIEnv *env, jobject clazz, jlong surface) {
    LOGD("Java_com_imilab_yuv_MediaYUV_releaseSurface");
    auto handle_info = (ANativeWindow*) surface;
    ANativeWindow_release(handle_info);
}

extern "C"
JNIEXPORT void JNICALL
Java_com_imilab_yuv_MediaYUV_showBgSurfaceYuv(JNIEnv *env, jobject clazz, jlong surface,
                                            jbyteArray yuv_data, jint width, jint height) {
//    LOGD("surface: %lld width: %d height: %d", surface, width, height);
    //获取原始yuv数组
    jbyte *src_yuv_data = env->GetByteArrayElements(yuv_data, NULL);

    libyuvNV21ToRGBA(reinterpret_cast<unsigned char *>(src_yuv_data),
                     reinterpret_cast<unsigned char *>(preview_bg_rgba), width, height);

    //显示窗口初始化
    auto nwin = (ANativeWindow*) surface;
    ANativeWindow_setBuffersGeometry(nwin, width, height, WINDOW_FORMAT_RGBA_8888);
    ANativeWindow_Buffer wbuf;

    // 绘制
    if (ANativeWindow_lock(nwin, &wbuf, 0)) {
        LOGE("ANativeWindow_lock error");
        env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
        return;
    }

    uint8_t *dst = (uint8_t *) wbuf.bits;
    memcpy(dst, preview_bg_rgba, width * height * 4);
    ANativeWindow_unlockAndPost(nwin);

    // 释放资源
    env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_imilab_yuv_MediaYUV_showFrontSurfaceYuv(JNIEnv *env, jobject thiz, jlong surface,
                                                 jbyteArray yuv_data, jint width, jint height) {
    //    LOGD("surface: %lld width: %d height: %d", surface, width, height);
    //获取原始yuv数组
    jbyte *src_yuv_data = env->GetByteArrayElements(yuv_data, NULL);

    libyuvNV21ToRGBA(reinterpret_cast<unsigned char *>(src_yuv_data),
                     reinterpret_cast<unsigned char *>(preview_front_rgba), width, height);

    //显示窗口初始化
    auto nwin = (ANativeWindow*) surface;
    ANativeWindow_setBuffersGeometry(nwin, width, height, WINDOW_FORMAT_RGBA_8888);
    ANativeWindow_Buffer wbuf;

    // 绘制
    if (ANativeWindow_lock(nwin, &wbuf, 0)) {
        LOGE("ANativeWindow_lock error");
        env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
        return;
    }

    uint8_t *dst = (uint8_t *) wbuf.bits;
    memcpy(dst, preview_front_rgba, width * height * 4);
    ANativeWindow_unlockAndPost(nwin);

    // 释放资源
    env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
}

void NV12ToNV21(const uint8_t *src_nv12, uint8_t *dst_nv21, int width, int height) {
    int y_size = width * height;
    int uv_size = y_size / 2;

    // 复制 Y 平面
    memcpy(dst_nv21, src_nv12, y_size);

    // 交换 UV 平面
    for (int i = 0; i < uv_size; i += 2) {
        dst_nv21[y_size + i] = src_nv12[y_size + i + 1];
        dst_nv21[y_size + i + 1] = src_nv12[y_size + i];
    }
}

// 或者直接使用 libyuv 的转换函数（推荐）
void NV12ToI420WithLibYUV(const uint8_t* src_nv21, uint8_t* dst_i420, int width, int height) {
    const int y_size = width * height;
    libyuv::NV12ToI420(
            src_nv21, width,         // Y 分量
            src_nv21 + y_size, width,// VU 分量（NV21 格式）
            dst_i420, width,         // Y 平面
            dst_i420 + y_size, width / 2,  // U 平面
            dst_i420 + y_size * 5 / 4, width / 2,  // V 平面
            width, height
    );
}

extern "C"
JNIEXPORT jbyteArray JNICALL
Java_com_imilab_yuv_MediaYUV_NV12ToNV21(JNIEnv *env, jobject thiz, jbyteArray nv12, jint width,
                                        jint height) {
     // 获取原始 NV12 数据
    jbyte *nv12_data = env->GetByteArrayElements(nv12, NULL);

    // 计算输出数据的大小
    int y_size = width * height;
    int uv_size = y_size / 2;
    int total_size = y_size + uv_size;

    // 创建新的 NV21 数据数组
    jbyteArray nv21 = env->NewByteArray(total_size);
    jbyte *nv21_data = env->GetByteArrayElements(nv21, NULL);

    // 调用转换函数
    NV12ToNV21(reinterpret_cast<const uint8_t *>(nv12_data), reinterpret_cast<uint8_t *>(nv21_data), width, height);

    // 释放资源
    env->ReleaseByteArrayElements(nv12, nv12_data, 0);
    env->ReleaseByteArrayElements(nv21, nv21_data, 0);
    return nv21;
}

extern "C"
JNIEXPORT jbyteArray JNICALL
Java_com_imilab_yuv_MediaYUV_NV12To420P(JNIEnv *env, jobject thiz, jbyteArray nv12, jint width,
                                        jint height) {
    // 获取原始 NV12 数据
    jbyte *nv12_data = env->GetByteArrayElements(nv12, NULL);

    // 计算输出数据的大小
    int y_size = width * height;
    int uv_size = y_size / 2;
    int total_size = y_size + uv_size;

    // 创建新的 NV21 数据数组
    jbyteArray nv21 = env->NewByteArray(total_size);
    jbyte *nv21_data = env->GetByteArrayElements(nv21, NULL);

    // 调用转换函数
    NV12ToI420WithLibYUV(reinterpret_cast<const uint8_t *>(nv12_data), reinterpret_cast<uint8_t *>(nv21_data), width, height);

    // 释放资源
    env->ReleaseByteArrayElements(nv12, nv12_data, 0);
    env->ReleaseByteArrayElements(nv21, nv21_data, 0);
    return nv21;
}
extern "C"
JNIEXPORT void JNICALL
Java_com_imilab_yuv_MediaYUV_showFrontYuv(JNIEnv *env, jobject thiz, jobject surface,
                                          jbyteArray yuv_data, jint width, jint height) {
    //获取原始yuv数组
    jbyte *src_yuv_data = env->GetByteArrayElements(yuv_data, NULL);

    libyuvNV12ToRGBA(reinterpret_cast<unsigned char *>(src_yuv_data),
                     reinterpret_cast<unsigned char *>(preview_front_rgba), width, height);

    //显示窗口初始化
    ANativeWindow *nwin = ANativeWindow_fromSurface(env, surface);
    ANativeWindow_setBuffersGeometry(nwin, width, height, WINDOW_FORMAT_RGBA_8888);
    ANativeWindow_Buffer wbuf;

    // 绘制
    if (ANativeWindow_lock(nwin, &wbuf, 0)) {
        LOGE("ANativeWindow_lock error");
        env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
        return;
    }

    uint8_t *dst = (uint8_t *) wbuf.bits;
    memcpy(dst, preview_front_rgba, width * height * 4);
    ANativeWindow_unlockAndPost(nwin);

    // 释放资源
    env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
    ANativeWindow_release(nwin);
}
extern "C"
JNIEXPORT void JNICALL
Java_com_imilab_yuv_MediaYUV_showBgYuv(JNIEnv *env, jobject thiz, jobject surface,
                                       jbyteArray yuv_data, jint width, jint height) {
    //获取原始yuv数组
    jbyte *src_yuv_data = env->GetByteArrayElements(yuv_data, NULL);

    libyuvNV12ToRGBA(reinterpret_cast<unsigned char *>(src_yuv_data),
                     reinterpret_cast<unsigned char *>(preview_bg_rgba), width, height);

    //显示窗口初始化
    ANativeWindow *nwin = ANativeWindow_fromSurface(env, surface);
    ANativeWindow_setBuffersGeometry(nwin, width, height, WINDOW_FORMAT_RGBA_8888);
    ANativeWindow_Buffer wbuf;

    // 绘制
    if (ANativeWindow_lock(nwin, &wbuf, 0)) {
        LOGE("ANativeWindow_lock error");
        env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
        return;
    }

    uint8_t *dst = (uint8_t *) wbuf.bits;
    memcpy(dst, preview_bg_rgba, width * height * 4);
    ANativeWindow_unlockAndPost(nwin);

    // 释放资源
    env->ReleaseByteArrayElements(yuv_data, src_yuv_data, 0);
    ANativeWindow_release(nwin);
}