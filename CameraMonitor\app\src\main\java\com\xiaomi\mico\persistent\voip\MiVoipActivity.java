package com.xiaomi.mico.persistent.voip;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.graphics.SurfaceTexture;
import android.graphics.drawable.AnimationDrawable;
import android.media.AudioManager;
import android.media.MediaFormat;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import android.os.PowerManager;
import android.text.TextUtils;
import android.view.Surface;
import android.view.TextureView;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.DialogFragment;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.DecodeAVC;
import com.xiaomi.camera.monitoring.MediaPlayerManager;
import com.xiaomi.camera.monitoring.entity.VideoFrameData;
import com.xiaomi.camera.monitoring.miss.MissConstants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.VoipEntry;
import com.xiaomi.mico.persistent.monitor.CameraMonitorManager;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;
import com.xiaomi.mico.persistent.monitor.R;
import com.xiaomi.mico.persistent.utils.MicoCommonDialog;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import miuix.animation.Folme;
import miuix.animation.ITouchStyle;
import miuix.animation.base.AnimConfig;
import miuix.animation.utils.EaseManager;

public class MiVoipActivity extends AppCompatActivity {
    private static final String TAG = "MiVoipActivity";

    ExecutorService mExecutor =  Executors.newSingleThreadExecutor(); // 创建线程池
    private final int VOIP_YUV_WIDTH = Constants.CAMERA_VOIP_WIDTH;
    private final int VOIP_YUV_HEIGHT = Constants.CAMERA_VOIP_HEIGHT;
    private TextureView mCameraView;
    private TextureView mRemoteView;
    private Surface mSurfaceCamera;
    private Surface mSurfaceRemote;

    private RelativeLayout mCallInfoContainer;
    private ImageView mCallLoading;
    private AnimationDrawable mCallLoadingAnim;
    private LinearLayout mCallControlContainer;
    private TextView mBtnMute;
    private TextView mBtnHangUp;
    private TextView mBtnCall;
    private TextView mBtnSwitchToVoice;
    private TextView mBtnCloseCam;
    private TextView mTvCloseCamSmall;
    private TextView mTvCloseCamLarge;
    private TextView mTvCallTime;
    private long mCallStartTime = 0;
    private boolean mCamIsOpen = true; // 本地相机是否打开
    private boolean mRemoteCamIsOpen = true; // 远程相机是否打开
    private boolean mLoadingHided = false;

    private boolean mCameraShowFont = false; // 默认小窗显示相机预览页面
    private boolean mIsDecoding = false;
    private DecodeAVC mDecodeAVC;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private VoipEntry mVoipEntry;
    private AudioManager mAudioManager;
    private boolean mMicrophoneMute; // 麦克风是否静音
    private boolean mCameraClosed; // 相机是否关闭
    private PowerManager mPowerManager;

    private final int HIDE_CALL_CONTAINER_DELAY = 5000;
    private final int CALL_TIME_DELAY = 1000;
    private final int CALL_TIME_OUT_HANGUP = 60 * 1000;
    private final int MSG_PREVIEW_REMOTE_CREATED = 0x01;
    private final int MSG_PREVIEW_CAMERA_CREATED = 0x02;
    private final int MSG_HIDE_CALL_CONTAINER = 0x03;
    private final int MSG_CALL_TIME_UPDATE = 0x04;
    private final int MSG_CALL_TIME_OUT_HANGUP = 0x05;
    private Handler mHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case MSG_PREVIEW_REMOTE_CREATED:
                    if (mSurfaceRemote == null) {
                        mSurfaceRemote = new Surface(mRemoteView.getSurfaceTexture());
                        mDecodeAVC = new DecodeAVC(mSurfaceRemote);
                        mDecodeAVC.init(MediaFormat.MIMETYPE_VIDEO_AVC, VOIP_YUV_WIDTH, VOIP_YUV_HEIGHT);
                        mDecodeAVC.startDecoder();
                        // 创建解码线程
                        mHandlerThread = new HandlerThread("VoipBackground");
                        mHandlerThread.start();
                        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
                        mIsDecoding = true;
                        // 开始解码
                        mBackgroundHandler.post(decodeRunnable);
                        mRemoteView.setVisibility(View.GONE);
                    }
                    break;
                case MSG_PREVIEW_CAMERA_CREATED:
                    if (mSurfaceCamera == null) {
                        mSurfaceCamera = new Surface(mCameraView.getSurfaceTexture());
                        CameraVideoPreview.getInstance().openCamera(getApplicationContext(), mSurfaceCamera);
                        updateCameraStatus();
                    }
                    break;
                case MSG_HIDE_CALL_CONTAINER:
                    mCallControlContainer.setVisibility(View.GONE);
                    break;
                case MSG_CALL_TIME_UPDATE:
                    long calTime = System.currentTimeMillis() - mCallStartTime;
                    mTvCallTime.setText(getString(R.string.call_time, MiVoipUtil.getCallTime(calTime)));
                    mHandler.sendEmptyMessageDelayed(MSG_CALL_TIME_UPDATE, CALL_TIME_DELAY);
                    break;
                case MSG_CALL_TIME_OUT_HANGUP:
                    mBtnHangUp.performClick();
                    break;
            }
            return false;
        }
    });

    private BroadcastReceiver cmdReceiver = new BroadcastReceiver() {

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            if (MiVoipUtil.VOIP_COMMAND_ACTION.equals(action)) {
                int cmd = intent.getIntExtra(MiVoipUtil.VOIP_COMMAND_ARGS, -1);
                if (cmd == MissConstants.MISS_CMD_OPEN_CAMERA_REMOTE) { // 远程相机打开
                    mRemoteCamIsOpen = true;
                } else if (cmd == MissConstants.MISS_CMD_CLOSE_CAMERA_REMOTE) { // 远程相机关闭
                    mRemoteCamIsOpen = false;
                }
                updateCamTipStatus();
            } else if (MiVoipUtil.VOIP_FINISH_ACTION.equals(action)) {
                finish();
            }
        }
    };

    BroadcastReceiver mMicrophoneMuteReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(AudioManager.ACTION_MICROPHONE_MUTE_CHANGED)) {
                // 更新麦克风状态
                updateMuteView(MiVoipUtil.isMicrophoneMute(mAudioManager));
            }
        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_mi_voip);
        initData();
        initView();
        initMicrophone();
    }

    private void initData() {
        MiVoipUtil.hangupByself = false;
        // 关于视觉算法检测
        CommonApiUtils.setVoipRunning(this, getPackageName());
        MiVoipUtil.sendVoipBroadcast(this, MiVoipUtil.MSG_VOIP_CLIENT_VOIP_START, null);
        // 播放来电音频
        mExecutor.submit(() -> MediaPlayerManager.getInstance().startPlay(getApplicationContext(), R.raw.voip_ringing));
        // 一分钟无应答挂断
        mHandler.sendEmptyMessageDelayed(MSG_CALL_TIME_OUT_HANGUP, CALL_TIME_OUT_HANGUP);
        mAudioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        mPowerManager = (PowerManager) getSystemService(Context.POWER_SERVICE);
        Intent intent = getIntent();
        String param = intent.getStringExtra(MiVoipUtil.MI_VOIP_START_PARAM);
        mVoipEntry = new Gson().fromJson(param, VoipEntry.class);
        L.monitor.v("%s VoipEntry: %s", TAG, mVoipEntry);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(MiVoipUtil.VOIP_FINISH_ACTION);
        intentFilter.addAction(MiVoipUtil.VOIP_COMMAND_ACTION);
        LocalBroadcastManager.getInstance(this).registerReceiver(cmdReceiver, intentFilter);
        // 静音状态变化监听
        IntentFilter filter = new IntentFilter(AudioManager.ACTION_MICROPHONE_MUTE_CHANGED);
        registerReceiver(mMicrophoneMuteReceiver, filter);
        //线程异常退出，调用VOIP_STOP通知Launcher重置状态
        Thread.setDefaultUncaughtExceptionHandler((t, e) -> {
            L.monitor.v("%s VoipActivity uncaughtException: %s", TAG, e.getMessage());
            MiVoipUtil.sendVoipBroadcast(this, MiVoipUtil.MSG_VOIP_CLIENT_VOIP_STOP, null);
        });
    }

    private void initView() {
        mCameraView = findViewById(R.id.preview_front);
        mCameraView.setSurfaceTextureListener(mPreviewCameraLis);
        mCameraView.setOutlineProvider(new VideoViewOutlineProvider(
                getResources().getInteger(R.integer.voip_front_round_corner)));
        mCameraView.setClipToOutline(true);
//        mCameraView.setScaleX(-1f);

        mRemoteView = findViewById(R.id.preview_bg);
        mRemoteView.setSurfaceTextureListener(mPreviewRemoteLis);
        mRemoteView.setOutlineProvider(new VideoViewOutlineProvider(
                getResources().getInteger(R.integer.voip_front_round_corner)));
        mRemoteView.setClipToOutline(true);

        mCallInfoContainer = findViewById(R.id.calling_info);
        // 加载头像信息
        if (!TextUtils.isEmpty(mVoipEntry.getIcon())) {
            ImageView callImage = findViewById(R.id.calling_photo);
            Glide.with(this)
                    .load(mVoipEntry.getIcon())
//                    .placeholder(R.mipmap.icon_avatar)
                    .apply(RequestOptions.circleCropTransform())
                    .into(callImage);
        } else {
            TextView callNameFirst = findViewById(R.id.calling_name_first);
            callNameFirst.setVisibility(View.VISIBLE);
            if (TextUtils.isEmpty(mVoipEntry.getName())) {
                callNameFirst.setText(mVoipEntry.getUid().substring(0,1));
            } else {
                callNameFirst.setText(mVoipEntry.getName().substring(0,1));
            }
        }

        mCallLoading = findViewById(R.id.calling_loading);
        mCallLoading.setBackgroundResource(R.drawable.basic_loading_animation);
        mCallLoadingAnim = (AnimationDrawable) mCallLoading.getBackground();
        mCallLoadingAnim.start();
        // 加载名称信息
        TextView callName = findViewById(R.id.calling_name);
        if (TextUtils.isEmpty(mVoipEntry.getName())) {
            callName.setText(mVoipEntry.getUid());
        } else {
            callName.setText(mVoipEntry.getName());
        }

        AnimConfig animConfig = new AnimConfig();
        animConfig.setEase(new EaseManager.InterpolateEaseStyle(EaseManager.EaseStyleDef.DECELERATE).setDuration(150L));

        mCallControlContainer = findViewById(R.id.control_panel);
        mBtnMute = findViewById(R.id.btn_mute);
        mBtnMute.setOnClickListener(mBtnMuteClickLis);
        voipBtnAnim(mBtnMute, animConfig);
        mBtnHangUp = findViewById(R.id.calling_hang_up);
        mBtnHangUp.setOnClickListener(mBtnHangUpClickLis);
        voipBtnAnim(mBtnHangUp, animConfig);
        mBtnCall = findViewById(R.id.btn_call);
        mBtnCall.setOnClickListener(mBtnCallClickLis);
        voipBtnAnim(mBtnCall, animConfig);
        mBtnSwitchToVoice = findViewById(R.id.calling_switch_to_voice);
        voipBtnAnim(mBtnSwitchToVoice, animConfig);
        mBtnCloseCam = findViewById(R.id.btn_camera_status);
        mBtnCloseCam.setOnClickListener(mBtnCloseCamClickLis);
        voipBtnAnim(mBtnCloseCam, animConfig);

        mTvCloseCamSmall = findViewById(R.id.close_cam_small);
        mTvCloseCamSmall.setOnClickListener(mCameraClickLis);
        mTvCloseCamLarge = findViewById(R.id.close_cam_large);
        mTvCloseCamLarge.setOnClickListener(mRemoteClickLis);
        RelativeLayout mainContainer = findViewById(R.id.main);
        mainContainer.setOnClickListener(mRemoteClickLis);
        mTvCallTime = findViewById(R.id.call_time);

        updatePreviewLayout();
    }

    private OnClickListener mBtnMuteClickLis =  new OnClickListener() {
        @Override
        public void onClick(View v) {
            L.monitor.d("%s BtnMuteOnclickLis onClick", TAG);
            boolean mMicIsMute = MiVoipUtil.isMicrophoneMute(mAudioManager);
            updateMuteView(!mMicIsMute);
            MiVoipUtil.setMicrophoneMute(mAudioManager, !mMicIsMute);
            resetHideCallContainer();
        }
    };

    private void updateMuteView(boolean micIsMute) {
        if (micIsMute) {
            mBtnMute.setText(R.string.voip_unmute);
            mBtnMute.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.btn_mute, 0, 0);
        } else {
            mBtnMute.setText(R.string.voip_mute);
            mBtnMute.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.btn_un_mute, 0, 0);
        }
    }

    private OnClickListener mBtnHangUpClickLis = v -> {
        L.monitor.d("%s BtnHangUpOnclickLis onClick", TAG);
        MiVoipUtil.hangupByself = true;
        JsonObject hangupObj = new JsonObject();
        hangupObj.addProperty("type", "hang_up");
        hangupObj.addProperty("code", 0);
        String hangupParam = hangupObj.toString();
        CameraMonitorManager.getInstance().mijiaHangUp(hangupParam);
        finish();
    };

    private OnClickListener mBtnCallClickLis =  new OnClickListener() {
        @Override
        public void onClick(View v) {
            L.monitor.d("%s BtnCallOnclickLis onClick", TAG);
            // 移除挂断超时
            if (mHandler.hasMessages(MSG_CALL_TIME_OUT_HANGUP)) {
                mHandler.removeMessages(MSG_CALL_TIME_OUT_HANGUP);
            }
            CameraMonitorManager.getInstance().callStart();
            // 播放接通音频
            mExecutor.submit(() -> MediaPlayerManager.getInstance().startPlay(getApplicationContext(), R.raw.voip_on));
            MiVoipUtil.isInCalling = true; // 通话中
            mCameraShowFont = true;
            mRemoteView.setVisibility(View.VISIBLE);
            //接通后预览显示在front
            updatePreviewLayout();
            updateCamTipStatus();
            // 隐藏呼叫按钮和呼叫信息
            mBtnCall.setVisibility(View.GONE);
            mCallInfoContainer.setVisibility(View.GONE);
            // 显示静音和关闭镜头
            mBtnMute.setVisibility(View.VISIBLE);
            mBtnCloseCam.setVisibility(View.VISIBLE);
            // 更新麦克风状态
            updateMuteView(MiVoipUtil.isMicrophoneMute(mAudioManager));
            // 通话计时
            mCallStartTime = System.currentTimeMillis();
            mTvCallTime.setText(getString(R.string.call_time, "00:00"));
            mTvCallTime.setVisibility(View.VISIBLE);
            mHandler.sendEmptyMessageDelayed(MSG_CALL_TIME_UPDATE, CALL_TIME_DELAY);
            // 5s后隐藏接听状态信息
            mHandler.sendEmptyMessageDelayed(MSG_HIDE_CALL_CONTAINER, HIDE_CALL_CONTAINER_DELAY);
        }
    };

    private OnClickListener mBtnCloseCamClickLis =  new OnClickListener() {
        @Override
        public void onClick(View v) {
            L.monitor.d("%s BtnCloseCamOnclickLis onClick", TAG);
            mCamIsOpen = !mCamIsOpen;
            if (mCamIsOpen) {
                mBtnCloseCam.setText(R.string.voip_close_camera);
                mBtnCloseCam.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.btn_camera_enable, 0, 0);
            } else {
                mBtnCloseCam.setText(R.string.voip_open_camera);
                mBtnCloseCam.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.btn_camera_disable, 0, 0);
            }
            CommonApiUtils.setCameraEnable(MiVoipActivity.this, mCamIsOpen);
            CameraMonitorManager.getInstance().mijiaCameraStatus(mCamIsOpen);
            updateCamTipStatus();
            resetHideCallContainer();
        }
    };

    // 更新相机提示状态
    private void updateCamTipStatus() {
        if (mCameraShowFont) {
            if (mCamIsOpen) {
                mTvCloseCamSmall.setVisibility(View.GONE);
            } else {
                mTvCloseCamSmall.setText(R.string.video_call_local_camera_closed);
                mTvCloseCamSmall.setVisibility(View.VISIBLE);
            }
            if (mRemoteCamIsOpen) {
                mTvCloseCamLarge.setVisibility(View.GONE);
            } else {
                mTvCloseCamLarge.setText(R.string.video_call_remote_camera_closed);
                mTvCloseCamLarge.setVisibility(View.VISIBLE);
            }
        } else {
            if (mCamIsOpen) {
                mTvCloseCamLarge.setVisibility(View.GONE);
            } else {
                mTvCloseCamLarge.setText(R.string.video_call_local_camera_closed);
                mTvCloseCamLarge.setVisibility(View.VISIBLE);
            }
            if (mRemoteCamIsOpen) {
                mTvCloseCamSmall.setVisibility(View.GONE);
            } else {
                mTvCloseCamSmall.setText(R.string.video_call_remote_camera_closed);
                mTvCloseCamSmall.setVisibility(View.VISIBLE);
            }
        }
        if (mCamIsOpen) {
            mCameraView.setVisibility(View.VISIBLE);
        } else {
            mCameraView.setVisibility(View.GONE);
        }
        if (mRemoteCamIsOpen) {
            mRemoteView.setVisibility(View.VISIBLE);
        } else {
            mRemoteView.setVisibility(View.GONE);
        }
        mTvCloseCamSmall.bringToFront();
        mTvCloseCamLarge.bringToFront();
    }

    private OnClickListener mRemoteClickLis = v -> {
        L.monitor.d("%s RemoteClickLis onClick", TAG);
        resetHideCallContainer();
    };

    private OnClickListener mCameraClickLis = v -> {
        L.monitor.d("%s CameraClickLis onClick", TAG);
        mCameraShowFont = !mCameraShowFont;
        updatePreviewLayout();
        updateCamTipStatus();
        resetHideCallContainer();
    };

    private void hideCallLoading() {
        if (mLoadingHided) {
            return;
        }
        mLoadingHided = true;
        mCallLoading.setVisibility(View.GONE);
        mCallLoadingAnim.stop();
    }

    Runnable decodeRunnable = () -> {
        while (mIsDecoding) {
            VideoFrameData data = CameraMonitorManager.getInstance().getVideoQueue().poll();
            if (mDecodeAVC != null && data != null) {
                mDecodeAVC.decode(data.dataBytes, data.length);
            }
        }
    };

    // 重新设置接听挂断状态容器
    private void resetHideCallContainer() {
        if (mHandler.hasMessages(MSG_HIDE_CALL_CONTAINER)) {
            mHandler.removeMessages(MSG_HIDE_CALL_CONTAINER);
        }
        mCallControlContainer.setVisibility(View.VISIBLE);
        mHandler.sendEmptyMessageDelayed(MSG_HIDE_CALL_CONTAINER, HIDE_CALL_CONTAINER_DELAY);
    }

    // 接听挂断等按钮动画
    public void voipBtnAnim(View view, AnimConfig animConfig) {
        Folme.clean(view);
        Folme.useAt(view)
                .touch()
                .setAlpha(0.7f, ITouchStyle.TouchType.DOWN)
                .setAlpha(1.0f, ITouchStyle.TouchType.UP)
                .handleTouchOf(view, false, animConfig);
    }

    final private TextureView.SurfaceTextureListener mPreviewRemoteLis = new TextureView.SurfaceTextureListener() {
        @Override
        public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
            L.monitor.i("%s Preview remote surfaceCreated", TAG);
            mHandler.sendEmptyMessage(MSG_PREVIEW_REMOTE_CREATED);
            setTransform(mRemoteView, VOIP_YUV_WIDTH, VOIP_YUV_HEIGHT);
        }

        @Override
        public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
        }

        @Override
        public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
            return false;
        }

        @Override
        public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {
        }
    };

    final private TextureView.SurfaceTextureListener mPreviewCameraLis = new TextureView.SurfaceTextureListener() {
        @Override
        public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surface, int width, int height) {
            L.monitor.i("%s Preview camera surfaceCreated", TAG);
            mHandler.sendEmptyMessage(MSG_PREVIEW_CAMERA_CREATED);
            setTransform(mCameraView, Constants.CAMERA_PREVIEW_WIDTH, Constants.CAMERA_PREVIEW_HEIGHT);
        }

        @Override
        public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surface, int width, int height) {
        }

        @Override
        public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surface) {
            return false;
        }

        @Override
        public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surface) {
            hideCallLoading();
        }
    };

    // 根据view大小，显示View内容不给拉升压缩
    private void setTransform(TextureView textureView, int previewWidth, int preViewHeight) {
        int viewWidth = textureView.getWidth();
        int viewHeight = textureView.getHeight();

        Matrix matrix = new Matrix();
        RectF viewRect = new RectF(0, 0, viewWidth, viewHeight);
        RectF bufferRect = new RectF(0, 0, previewWidth, preViewHeight);
        float centerX = viewRect.centerX();
        float centerY = viewRect.centerY();
        bufferRect.offset(centerX - bufferRect.centerX(), centerY - bufferRect.centerY());
        matrix.setRectToRect(viewRect, bufferRect, Matrix.ScaleToFit.FILL);
        float scale = Math.max(
                (float) viewWidth / previewWidth,
                (float) viewHeight / preViewHeight);
        matrix.postScale(scale, scale, centerX, centerY);
        textureView.setTransform(matrix);
    }

    // 切换相机预览页面和远程图像页面布局大小
    private void updatePreviewLayout() {
        L.monitor.d("%s updatePreviewLayout CameraShowFont: %s", TAG, mCameraShowFont);
        mRemoteView.setOnClickListener(null);
        mCameraView.setOnClickListener(null);
        if (mCameraShowFont) {
            setLargeParams(mRemoteView);
            setSmallParams(mCameraView);
            mRemoteView.setOnClickListener(mRemoteClickLis);
            mCameraView.setOnClickListener(mCameraClickLis);
        } else {
            setLargeParams(mCameraView);
            setSmallParams(mRemoteView);
            mRemoteView.setOnClickListener(mCameraClickLis);
            mCameraView.setOnClickListener(mRemoteClickLis);
        }
        mTvCallTime.bringToFront();
        mCallControlContainer.bringToFront();
    }

    private void setLargeParams(View view) {
        RelativeLayout.LayoutParams paramLarge= (RelativeLayout.LayoutParams) view.getLayoutParams();
        paramLarge.width = RelativeLayout.LayoutParams.MATCH_PARENT;  // 设置新的宽度
        paramLarge.height = RelativeLayout.LayoutParams.MATCH_PARENT; // 设置新的高度
        paramLarge.leftMargin = 0;
        paramLarge.topMargin = 0;
        view.setLayoutParams(paramLarge);
    }

    private void setSmallParams(View view) {
        RelativeLayout.LayoutParams paramsSmall = (RelativeLayout.LayoutParams) view.getLayoutParams();
        paramsSmall.width = getResources().getDimensionPixelSize(R.dimen.call_small_width);  // 设置新的宽度
        paramsSmall.height = getResources().getDimensionPixelSize(R.dimen.call_small_height); // 设置新的高度
        paramsSmall.leftMargin = getResources().getDimensionPixelSize(R.dimen.call_small_left); // 设置左边距
        paramsSmall.topMargin = getResources().getDimensionPixelSize(R.dimen.call_small_top); // 设置上边距
        view.setLayoutParams(paramsSmall);
        view.bringToFront();
    }

    private void updateCameraStatus() {
        mCameraClosed = CommonApiUtils.getCameraStatus(this);
        if (mCameraClosed) {
            mCamIsOpen = false;
            mBtnCloseCam.setText(R.string.voip_open_camera);
            mBtnCloseCam.setCompoundDrawablesWithIntrinsicBounds(0, R.drawable.btn_camera_disable, 0, 0);
            updateCamTipStatus();
        }
    }

    // 初始化麦克风状态
    private void initMicrophone() {
        mMicrophoneMute = MiVoipUtil.isMicrophoneMute(mAudioManager);
        if (mMicrophoneMute) {
            MicoCommonDialog dialog = new MicoCommonDialog.Builder()
                    .setCancelable(false)
                    .setTitle(getString(R.string.ims_dialog_open_mic_content))
                    .setContent(getString(R.string.ims_dialog_open_mic_title))
                    .setPositiveButton(getString(R.string.ims_dialog_enter))
                    .setNegativeButton(getString(R.string.ims_dialog_cancel))
                    .build();
            dialog.show(getSupportFragmentManager(), "MicoCommonDialog");
            dialog.setCallBack(new MicoCommonDialog.CallBack() {
                @Override
                public void onFirBtnClick(DialogFragment dialog) {
                    L.monitor.d("%s Mute dialog click ok", TAG);
                    MiVoipUtil.setMicrophoneMute(mAudioManager, false);
                }

                @Override
                public void onSecBtnClick(DialogFragment dialog) {
                    L.monitor.d("%s Mute dialog click cancel", TAG);
                }
            });
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        L.monitor.d("%s onPause", TAG);
        // 恢复接通前相机状态
        mIsDecoding = false;
        boolean camStatus = CommonApiUtils.getCameraStatus(this);
        if (mCameraClosed != camStatus) {
            L.monitor.d("%s Reset Camera status: %s", TAG, mCameraClosed);
            CommonApiUtils.setCameraEnable(MiVoipActivity.this, !mCameraClosed);
        }
        // 恢复接通前麦克风状态
        boolean micStatus = MiVoipUtil.isMicrophoneMute(mAudioManager);
        if (mMicrophoneMute != micStatus) {
            L.monitor.d("%s Reset Microphone status: %s", TAG, mMicrophoneMute);
            MiVoipUtil.setMicrophoneMute(mAudioManager, mMicrophoneMute);
        }
        mExecutor.submit(() -> MediaPlayerManager.getInstance().startPlay(getApplicationContext(), R.raw.voip_phone_hangup));
        MiVoipUtil.isInCalling = false;
        mBtnHangUp.performClick();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        L.monitor.d("%s onDestroy", TAG);
        LocalBroadcastManager.getInstance(this).unregisterReceiver(cmdReceiver);
        unregisterReceiver(mMicrophoneMuteReceiver);
        CommonApiUtils.setVoipRunning(getApplicationContext(), "");
        MiVoipUtil.sendVoipBroadcast(this, MiVoipUtil.MSG_VOIP_CLIENT_VOIP_STOP, null);
        CameraVideoPreview.getInstance().stopPreview();
        // 移除挂断超时
        if (mHandler.hasMessages(MSG_CALL_TIME_OUT_HANGUP)) {
            mHandler.removeMessages(MSG_CALL_TIME_OUT_HANGUP);
        }
        if (mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
        }
        try {
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mHandlerThread = null;
            mBackgroundHandler = null;
        }
        if (mDecodeAVC != null) {
            mDecodeAVC.stopDecoder();
        }
        mExecutor.shutdown();
    }
}