package com.xiaomi.mico.persistent.utils;

import org.apache.commons.codec.binary.Base64;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.ByteArrayOutputStream;
import java.io.Closeable;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.concurrent.TimeUnit;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import okhttp3.OkHttpClient;

public class MiUploadUtils {

    public static String convertStringToHex(String str) {
        char[] chars = str.toCharArray();
        StringBuffer hex = new StringBuffer();
        for (int i = 0; i < chars.length; i++) {
            hex.append(Integer.toHexString((int) chars[i]));
        }
        return hex.toString();
    }

    public static IvParameterSpec generateIvParameterSpec() {
        byte[] iv = new byte[16];
        SecureRandom random = new SecureRandom();
        random.nextBytes(iv);
        return new IvParameterSpec(iv);
    }

    public static String decryptData(String encryptString, String securityKey){
        String decryptString = null;
        try {
            JSONObject encryptObject = new JSONObject(encryptString);
            String encryptData = encryptObject.getString("data");
            String encryptIv = encryptObject.getString("iv");
            decryptString = MiUploadUtils.decrypt(encryptData, securityKey, encryptIv);
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return decryptString;
    }

    //解密返回数据： 验证签名通过后要使⽤如下函数解密数据
    //将返回中的字段"data"+返回的字段iv作为输⼊，⽣成新的signature, 并与返回的“sign”⽐较，⽐较通过
    //后，使⽤之前的服务器下发的secretKey和返回的“iv"，解密字段"data"
    public static String decrypt(String encryptData, String secretKey, String iv) {
        IvParameterSpec ivParameterSpec = new
                IvParameterSpec(Base64.decodeBase64(iv));
        SecretKeySpec secretKeySpec = new
                SecretKeySpec(Base64.decodeBase64(secretKey), "AES");
        byte[] byteData = Base64.decodeBase64(encryptData);

        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            byte[] raw = cipher.doFinal(byteData);
            return new String(raw, "UTF-8");
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidAlgorithmParameterException
                | InvalidKeyException | BadPaddingException | IllegalBlockSizeException | UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return null;
    }

    //加密https请求中携带的数据
    //加密请求参数,根据上⼀步⽣成iv和下发的secuirty进⾏参数加密,加密参数中不包括iv字段
    public static byte[] encrypt(byte[] raw, String secretKey, String
            iv) {
        IvParameterSpec ivParameterSpec = new
                IvParameterSpec(Base64.decodeBase64(iv));
        SecretKeySpec secretKeySpec = new
                SecretKeySpec(Base64.decodeBase64(secretKey), "AES");
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            return cipher.doFinal(raw);
        } catch (NoSuchAlgorithmException | NoSuchPaddingException | InvalidAlgorithmParameterException
                | InvalidKeyException | BadPaddingException | IllegalBlockSizeException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String generateSign(byte[] signByte){

        byte[] sign = new byte[0];
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(signByte);
            sign = messageDigest.digest();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return Base64.encodeBase64URLSafeString(sign);
    }

    /**
     * 对文件进行AES加密
     *
     * @param sourceFile 待加密文件
     * @param secretKey  密钥
     * @return 加密后的文件
     */
    public static EntryData encryptFile(File sourceFile, String secretKey, String iv) {
        try {
            // 使用 ByteArrayOutputStream 来存储加密后的字节
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 初始化 Cipher
            IvParameterSpec ivParameterSpec = new IvParameterSpec(Base64.decodeBase64(iv));
            SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.decodeBase64(secretKey), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            // 以加密流写入文件
            CipherInputStream cipherInputStream = new CipherInputStream(new FileInputStream(sourceFile), cipher);
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            // 创建缓存字节数组
            byte[] buffer = new byte[1024 * 2];
            // 读取
            int len;
            // 读取加密并写入文件
            while ((len = cipherInputStream.read(buffer)) != -1) {
                digest.update(buffer, 0, len);
                outputStream.write(buffer, 0, len);
            }
            cipherInputStream.close();
            closeStream(outputStream);

            // 关闭加密输入流
            byte[] md5sum = digest.digest();
            EntryData data = new EntryData();
            data.encryptFile = outputStream.toByteArray();
            data.encryptSign = Base64.encodeBase64String(md5sum);
            return data;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 关闭流
     *
     * @param closeable 实现Closeable接口
     */
    private static void closeStream(Closeable closeable) {
        try {
            if (closeable != null) closeable.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static class EntryData {
        public byte[] encryptFile;
        public String encryptSign;
    }

    public static OkHttpClient getOkHttpClient() {
        OkHttpClient client = new OkHttpClient.Builder()
//                .addInterceptor(new HttpLogInterceptor())
                .addInterceptor(new RetryInterceptor(3))
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(10, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build();
        return client;
    }
}
