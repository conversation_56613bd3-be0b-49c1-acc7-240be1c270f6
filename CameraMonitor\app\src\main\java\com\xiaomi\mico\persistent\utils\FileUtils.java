package com.xiaomi.mico.persistent.utils;

import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.os.Environment;
import android.text.TextUtils;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;

import java.io.BufferedOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 文件夹工具
 *
 * <AUTHOR>
 * @date 2020/1/2 14:25
 */
public class FileUtils {
    private static final String TAG = "FileUtils";

    public static final String SEPARATOR = "/";

    /**
     * 重命名文件
     */
    public static File renameFile(String oldPath, String newPath) {
        if (TextUtils.isEmpty(oldPath)) {
            return null;
        }

        if (TextUtils.isEmpty(newPath)) {
            return null;
        }

        File file = new File(oldPath);
        file.renameTo(new File(newPath));
        return file;
    }

    /**
     * 获取所有子文件的名字（非递归，只取当前子文件，不会递归去取孙子文件）
     */
    public static List<String> getAllChildFileName(String dirFilePath) {
        List<String> fileList = new ArrayList<>();

        File file = new File(dirFilePath);
        File[] files = file.listFiles();
        if (files != null) {
            for (File childFile : files) {
                fileList.add(childFile.getName());
            }
        }
        return fileList;
    }

    /**
     * 递归获取当前目录下所有叶节点文件（会递归去取孙子文件）
     */
    public static List<File> getAllLeafNodeFile(String dirFilePath) {
        List<File> fileList = new ArrayList<>();

        File file = new File(dirFilePath);

        File[] files = file.listFiles();
        if (files != null) {
            if (files.length == 0) {//getExternalFilesDir会直接创建文件夹，如果是空文件夹需要删除。以免相应回看会当作有数据
                file.delete();
                return fileList;
            }
            //根据文件的修改日期进行排序
            Arrays.sort(files, new Comparator<File>() {
                public int compare(File f1, File f2) {
                    long diff = f1.lastModified() - f2.lastModified();
                    if (diff > 0)
                        return 1;
                    else if (diff == 0)
                        return 0;
                    else
                        return -1;//如果 if 中修改为 返回-1 同时此处修改为返回 1  排序就会是递减
                }

                public boolean equals(Object obj) {
                    return true;
                }
            });

            for (File childFile : files) {
                if (childFile.isDirectory()) {//如果是目录，继续
                    fileList.addAll(getAllLeafNodeFile(childFile.getAbsolutePath()));
                } else {
                    fileList.add(childFile);
                }
            }
        }
        return fileList;
    }

    /**
     * 删除文件
     */
    public static void delFile(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
        }
    }

    //删除文件夹和文件夹里面的文件
    public static void deleteDir(final String pPath) {
        File dir = new File(pPath);
        deleteDirWihtFile(dir);
    }

    public static void deleteDirWihtFile(File dir) {
        if (dir == null || !dir.exists() || !dir.isDirectory())
            return;
        for (File file : dir.listFiles()) {
            if (file.isFile())
                file.delete(); // 删除所有文件
            else if (file.isDirectory())
                deleteDirWihtFile(file); // 递规的方式删除文件夹
        }
        dir.delete();// 删除目录本身
    }

    /**
     * 将文件转byte[]
     */
    public static byte[] file2Bytes(File file) {
        int byte_size = 1024;
        byte[] b = new byte[byte_size];
        try {
            FileInputStream fileInputStream = new FileInputStream(file);
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream(byte_size);
            for (int length; (length = fileInputStream.read(b)) != -1; ) {
                outputStream.write(b, 0, length);
            }
            fileInputStream.close();
            outputStream.close();
            return outputStream.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 根据名字找出当前目录下的文件
     */
    public static File getFileByName(String parentPath, String tag) {
        File parent = new File(parentPath);
        File[] files = parent.listFiles();
        if (files == null) {
            return null;
        }
        for (File file : files) {
            if (file.getName().contains(tag)) {
                return file;
            }
        }
        return null;
    }

    /**
     * 文件的复制操作方法
     * @param fromFile 准备复制的文件
     * @param toFile 要复制的文件的目录
     */
    public static void copyFile(File fromFile, File toFile){
        if(!fromFile.exists()){
            return;
        }
        if(!fromFile.isFile()){
            return;
        }
        if(!fromFile.canRead()){
            return;
        }
        if(!toFile.getParentFile().exists()){
            toFile.getParentFile().mkdirs();
        }
        if(toFile.exists()){
            toFile.delete();
        }
        try {
            FileInputStream fosfrom = new FileInputStream(fromFile);
            FileOutputStream fosto = new FileOutputStream(toFile);
            byte[] bt = new byte[1024];
            int c;
            while((c=fosfrom.read(bt)) > 0){
                fosto.write(bt,0,c);
            }
            //关闭输入、输出流
            fosfrom.close();
            fosto.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void splitByFileReader(String resFile, String targetFileDir, String targetFileName, int targetFileSize) throws IOException {
        File fromFile = new File(resFile);
        if(!fromFile.exists()){
            return;
        }
        if(!fromFile.isFile()){
            return;
        }
        if(!fromFile.canRead()){
            return;
        }
        File toFile = new File(targetFileDir);
        if(!toFile.exists()){
            toFile.mkdirs();
        }
        // 读取文件
        FileReader fileReader = new FileReader(resFile);
        FileWriter fileWriter = new FileWriter(targetFileDir + "/" + targetFileName + ".txt");
        // 一次读1024个字符
        char [] buf = new char[1024];
        int count = 0;
        int id = 1;
        while (fileReader.read(buf) != -1) {
            // 读取targetFileSize次之后，开始向第二个txt注入
            if (count != 0 && count % targetFileSize == 0) {
                // 创建新的输出流时，将前一个关闭。
                fileWriter.flush();
                fileWriter.close();
                fileWriter = new FileWriter(targetFileDir + "/" + targetFileName +  id + ".txt");
                id ++;
            }
            fileWriter.write(buf);
            count ++;
        }
        fileReader.close();
        fileWriter.close();
    }

    /**
     * 获取文件签名嘻嘻
     * @param file 文件路径
     * @param algorithm "SHA-256" "MD5"
     */
    public static String getFileSign(File file, String algorithm) {
        MessageDigest messageDigest;
        RandomAccessFile randomAccessFile = null;
        try {
            messageDigest = MessageDigest.getInstance(algorithm);
            if (file == null) {
                return "";
            }
            if (!file.exists()) {
                return "";
            }
            randomAccessFile = new RandomAccessFile(file,"r");
            byte[] bytes = new byte[1024];
            int len=0;
            while ((len=randomAccessFile.read(bytes))!=-1){
                messageDigest.update(bytes,0, len);
            }
            BigInteger bigInt = new BigInteger(1, messageDigest.digest());
            String md5 = bigInt.toString(16);
            while (md5.length() < 32) {
                md5 = "0" + md5;
            }
            return md5;
        } catch (NoSuchAlgorithmException | IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (randomAccessFile != null) {
                    randomAccessFile.close();
                    randomAccessFile = null;
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }

    public static String getFileMd5Byte(byte[] fileData) {
        MessageDigest messageDigest;
        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(fileData,0, fileData.length);
            BigInteger bigInt = new BigInteger(1, messageDigest.digest());
            String md5 = bigInt.toString(16);
            while (md5.length() < 32) {
                md5 = "0" + md5;
            }
            return md5;
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    private static FileOutputStream fileOutputStream;
    private static BufferedOutputStream bufferedOutputStream;
    public static void createFileToByte(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
        }
        try {
            fileOutputStream = new FileOutputStream(file);
            bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void writeByteToFile(byte[] data) {
        if (bufferedOutputStream != null) {
            try {
                bufferedOutputStream.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void closeFileToByte() {
        if (bufferedOutputStream != null) {
            try {
                bufferedOutputStream.flush();
                bufferedOutputStream.close();
                bufferedOutputStream = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static final String CLOUD_UPLOAD_PATH = "CloudRecord";
    public static String getCloudRecordPath() {
        String sdPath = Environment.getExternalStorageDirectory().getPath();
        File file = new File( sdPath + File.separator + CLOUD_UPLOAD_PATH);
        if (!file.exists()) {
            file.mkdirs();
        }
        return file.getPath();
    }

    public static boolean saveImage(byte[] yuvData, String path) {
        if (yuvData == null) {
            L.monitor.e("%s !!!!!========saveImage:yuvData == null", TAG);
            return false;
        }

        File pictureFile = new File(path);
        if (pictureFile.exists()) {
            pictureFile.delete();
        }
        try {
            pictureFile.createNewFile();
            FileOutputStream fileCon = new FileOutputStream(pictureFile);
            YuvImage image = new YuvImage(yuvData, ImageFormat.NV21, Constants.CLOUD_IMAGE_WIDTH,
                    Constants.CLOUD_IMAGE_HEIGHT, null);   //将NV21 data保存成YuvImage
            //图像压缩
            image.compressToJpeg(new Rect(0, 0, image.getWidth(), image.getHeight()), Constants.CLOUD_IMAGE_QUALITY, fileCon);   // 将NV21格式图片，并得到JPEG数据流
            L.monitor.i("%s Save %s done", TAG, pictureFile.getName());
        } catch (IOException e) {
            L.monitor.e("%s saveImage error: %s", TAG, e.getMessage());
            return false;
        }
        return true;
    }

    public static byte[] createBlackYUV(int width, int height) {
        byte[] outputYUV = new byte[width * height * 3 / 2];

        // y: up_y , middle_y:old_yuv, down_y
        Arrays.fill(outputYUV, 0, width * height, (byte) 16);

        // uv : up_black, middle_old_color, down_black
        Arrays.fill(outputYUV, width * height, width * height + (width * height / 2), (byte) 128);
        return outputYUV;
    }

}
