package com.xiaomi.mico.persistent.func;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.provider.Settings;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.FamilyCareEntry;
import com.xiaomi.mico.persistent.spec.SpecEventSend;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * https://wayawbott0.f.mioffice.cn/docx/doxk4jtrl1DFrTF2S7BFxdafISf
 * {
        "enable": false, 开关是否开启
        "clock_idx": 0,  app设置的一个时钟下发的clock_idx = 1。
        "start": "07:00", 开始时间
        "end": "09:00", 结束时间
        "repeat": 127, 重复时间127: [111 1111] [六，五，四，三，二，一，日], 0:只执行一次
        "name": "早晨无人出现" 名称
    }
 */

public class FamilyCareManager {
    private static final String TAG = "FamilyCareManager";

    private final String DEFAULT_FAMILY_CARE = "{\"clock\":[{\"enable\":false,\"clock_idx\":0,\"start\":\"07:00\",\"end\":\"09:00\",\"repeat\":127,\"name\":\"早晨无人出现\"},{\"enable\":false,\"clock_idx\":1,\"start\":\"11:00\",\"end\":\"13:00\",\"repeat\":127,\"name\":\"午餐无人出现\"},{\"enable\":false,\"clock_idx\":2,\"start\":\"06:00\",\"end\":\"20:00\",\"repeat\":127,\"name\":\"白天无人出现\"}]}";

    private final int MAX_ALARM_CLOCK_NUM = 10;
    private final int MODE_NO_NEED_WORK = -1;
    private final int MODE_OUT_WORK_TIME = 0;
    private final int MODE_NEED_WORK = 1;

    private volatile static FamilyCareManager mInstance;
    private Context mContext;
    private List<FamilyCareEntry> mFamilyCareList = new ArrayList<>(MAX_ALARM_CLOCK_NUM);
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;

    public static FamilyCareManager getInstance() {
        if (mInstance == null) {
            synchronized (FamilyCareManager.class) {
                if (mInstance == null) {
                    mInstance = new FamilyCareManager();
                }
            }
        }
        return mInstance;
    }

    private FamilyCareManager() {}

    public void initFamilyCare(Context context) {
        mContext = context;
        // 启动 HandlerThread
        mHandlerThread = new HandlerThread("FamilyCareThread");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        mBackgroundHandler.post(this::updateFamilyCareConfig);
        scheduleCheckReportEvent();
    }

    private void scheduleCheckReportEvent() {
        Calendar now = Calendar.getInstance();
        int currentSecond = now.get(Calendar.SECOND);
        int delay = (60 - currentSecond) * 1000; // 计算到下一次0秒的时间间隔
        mBackgroundHandler.postDelayed(() -> {
            checkReportEvent();
            scheduleCheckReportEvent(); // 递归调用以每分钟的0秒执行一次
        }, delay);
    }

    private synchronized void checkReportEvent() {
        for (FamilyCareEntry entry : mFamilyCareList) {
            // 判断是否需要开始看护
            if (!entry.doCare) {
                entry.doCare = shouldDoCare(entry);
            }
            // 如果开始看护
            if (entry.doCare) {
                int mode = ifSmartCareClockNeedWork(entry);
                L.monitor.d("%s checkReportEvent mode: %d", TAG, mode);
                // 超过看护时间，并且没有人形，则上报事件
                if (mode == MODE_OUT_WORK_TIME) {
                    if (!entry.hasHuman) {
                        reportEvent(entry.clock_idx);
                    } else {
                        entry.hasHuman = false;
                    }
                    entry.doCare = false;
                    // 如果是一次性看护，修改看护开关为false
                    if (entry.repeat == 0) {
                        updateFamilyCare(entry.clock_idx);
                    }
                }
            }
        }
    }

    // 触发人形
    public synchronized void peopleEventOccurred() {
        for (FamilyCareEntry entry : mFamilyCareList) {
            if (entry.hasHuman || !entry.doCare) {
                continue;
            }
            // 看护时间段内出现人形，设置 hasHuman = true;
            int mode = ifSmartCareClockNeedWork(entry);
            if (mode == MODE_NEED_WORK) {
                entry.hasHuman = true;
            }
        }
    }

    // 判断当前是否为看护开始时间
    private synchronized boolean shouldDoCare(FamilyCareEntry alarm) {
        Calendar now = Calendar.getInstance();
        int hour = now.get(Calendar.HOUR_OF_DAY);
        int min = now.get(Calendar.MINUTE);
        String[] startTime = alarm.start.split(":");
        if (Integer.parseInt(startTime[0]) == hour && Integer.parseInt(startTime[1]) == min) {
            return true;
        }
        return false;
    }

    // 判断是否在看护时间范围内
    private synchronized int ifSmartCareClockNeedWork(FamilyCareEntry alarm) {
        int ret = 0;
        Calendar now = Calendar.getInstance();

        if ((alarm.repeat != 0x00 && (alarm.repeat & (1 << now.get(Calendar.DAY_OF_WEEK) - 1)) == 0) || !alarm.enable) {
            return MODE_NO_NEED_WORK;
        }

        String[] startTime = alarm.start.split(":");
        String[] endTime = alarm.end.split(":");
        int smin = Integer.parseInt(startTime[0]) * 60 + Integer.parseInt(startTime[1]);
        int emin = Integer.parseInt(endTime[0]) * 60 + Integer.parseInt(endTime[1]);
        int cmin = now.get(Calendar.HOUR_OF_DAY) * 60 + now.get(Calendar.MINUTE);
        int diff = emin - smin;

        if (diff > 0 && (cmin >= smin && cmin < emin)) {
            ret =MODE_NEED_WORK;
        } else if (diff < 0 && (cmin < emin || cmin >= smin)) {
            ret = MODE_NEED_WORK;
        } else if (diff == 0 && cmin == smin) {
            ret = MODE_NEED_WORK;
        } else {
            ret = MODE_OUT_WORK_TIME;
        }

        return ret;
    }

    // 更新家人看护配置
    public synchronized void updateFamilyCareConfig() {
        String familyCareConfig = Settings.Global.getString(mContext.getContentResolver(), Constants.KEY_FAMILY_GUARD_CONFIG);
        L.monitor.d("%s config: %s", TAG, familyCareConfig);
        if (TextUtils.isEmpty(familyCareConfig)) {
            familyCareConfig = DEFAULT_FAMILY_CARE;
        }
        JsonObject clockJsonObj;
        try {
            clockJsonObj = new Gson().fromJson(familyCareConfig, JsonObject.class);
        } catch (Exception e) {
            clockJsonObj = new Gson().fromJson(DEFAULT_FAMILY_CARE, JsonObject.class);
        }
        JsonArray clockJsonArray = clockJsonObj.getAsJsonArray("clock");
        Type listType = new TypeToken<List<FamilyCareEntry>>() {}.getType();
        List<FamilyCareEntry> entryList = new Gson().fromJson(clockJsonArray, listType);
        if (entryList.isEmpty()) {
            L.monitor.v("%s updateFamilyCareConfig mFamilyCareList clear", TAG);
            mFamilyCareList.clear();
        } else {
            // 以当前最新的列表为基础，进行匹配
            int originSize = mFamilyCareList.size();
            int newSize = entryList.size();
            for (int i = 0; i < newSize; i ++) {
                FamilyCareEntry newEntry = entryList.get(i);
                if (originSize -1 >= i) {
                    FamilyCareEntry originEntry = mFamilyCareList.get(i);
                    if (!newEntry.equals(originEntry)) {
                        mFamilyCareList.set(i, newEntry);
                    }
                } else {
                    mFamilyCareList.add(newEntry);
                }
            }
            if (originSize > newSize) {
                mFamilyCareList.subList(newSize, originSize).clear();
            }
        }
//        for (FamilyCareEntry entry : mFamilyCareList) {
//            L.monitor.v("%s updateFamilyCareConfig entry: %s", TAG, entry);
//        }
    }

    // 上报无人出现事件
    private void reportEvent(int idx) {
        SpecEventSend.eventFamilyCare(idx);
    }

    private synchronized void updateFamilyCare(int clock_idx) {
        JsonObject clockObj = new JsonObject();
        JsonArray clockArray = new JsonArray();
        for (FamilyCareEntry entry : mFamilyCareList) {
            JsonObject itemObj = new JsonObject();
            itemObj.addProperty("enable", clock_idx != entry.clock_idx && entry.enable);
            itemObj.addProperty("clock_idx", entry.clock_idx);
            itemObj.addProperty("start", entry.start);
            itemObj.addProperty("end", entry.end);
            itemObj.addProperty("repeat", entry.repeat);
            itemObj.addProperty("name", entry.name);
            clockArray.add(itemObj);
        }
        clockObj.add("clock", clockArray);
        Settings.Global.putString(mContext.getContentResolver(), Constants.KEY_FAMILY_GUARD_CONFIG, clockObj.toString());
    }

    public void release() {
        if (mBackgroundHandler != null) {
            mBackgroundHandler.removeCallbacksAndMessages(null);
        }
        if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
            mHandlerThread.quit();
            mHandlerThread.interrupt();
        }
    }
}
