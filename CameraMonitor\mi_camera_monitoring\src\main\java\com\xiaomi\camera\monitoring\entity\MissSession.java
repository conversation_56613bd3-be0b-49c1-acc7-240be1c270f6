package com.xiaomi.camera.monitoring.entity;

import com.xiaomi.camera.monitoring.UVCCamera0VideoManager;
import com.xiaomi.camera.monitoring.UVCCamera1VideoManager;

import org.json.JSONException;
import org.json.JSONObject;

public class MissSession {

    public static final int VIDEO_QUALITY_AUTO = 0;
    public static final int VIDEO_QUALITY_LOW = 1;
    public static final int VIDEO_QUALITY_HEIGHT = 6;

    private int session;
    private boolean runVideo;
    private boolean runAudio;
    private int videoQuality = VIDEO_QUALITY_AUTO;

    public MissSession(int session) {
        this.session = session;
    }

    public int getSession() {
        return session;
    }

    public void startVideo(boolean startVideo) {
        runVideo = startVideo;
    }

    public void startAudio(boolean startAudio) {
        runAudio = startAudio;
    }

    public boolean isRunVideo() {
        return runVideo;
    }

    public boolean isRunAudio() {
        return runAudio;
    }

    public void setVideoQuality(int quality) {
        videoQuality = quality;
        // 有视频请求，获取IDR帧
        if (videoQuality == VIDEO_QUALITY_HEIGHT) {
            UVCCamera0VideoManager.getInstance().requestIDR();
        } else {
            UVCCamera1VideoManager.getInstance().requestIDR();
        }
    }

    public void setVideoQuality(String quality) {
        try {
            JSONObject qualityJson = new JSONObject(quality);
            videoQuality = qualityJson.getInt("videoquality");
            // 有视频请求，获取IDR帧
            if (videoQuality == VIDEO_QUALITY_HEIGHT || videoQuality == VIDEO_QUALITY_AUTO) {
                UVCCamera0VideoManager.getInstance().requestIDR();
            } else {
                UVCCamera1VideoManager.getInstance().requestIDR();
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    public int getVideoQuality() {
        return videoQuality;
    }
}
