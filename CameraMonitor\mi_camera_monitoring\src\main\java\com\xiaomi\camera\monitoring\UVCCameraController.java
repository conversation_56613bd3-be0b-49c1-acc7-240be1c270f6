package com.xiaomi.camera.monitoring;

import android.hardware.ipcamera.V1_0.IUVCCameraController;
import android.os.Process;
import android.os.RemoteException;

import com.xiaomi.camera.monitoring.utils.L;

/**
 * 通过UVC获取Camera0数据
 */
public class UVCCameraController {

    private final String TAG = "UVCCameraController";

    private volatile static UVCCameraController mInstance;

    private IUVCCameraController mUVCameraController;

    public static UVCCameraController getInstance() {
        if (mInstance == null) {
            synchronized (UVCCameraController.class) {
                if (mInstance == null) {
                    mInstance = new UVCCameraController();
                }
            }
        }
        return mInstance;
    }

    private UVCCameraController() {
    }

    public void init() {
//        L.monitor.d("%s =====init=====", TAG);
        try {
            mUVCameraController = IUVCCameraController.getService(Constants.IPCAMERA_SERVICE_NAME);
            linkToDeath();
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    // 监听IPCamera服务进程状态
    private void linkToDeath() {
        try {
            if (mUVCameraController != null) {
                mUVCameraController.linkToDeath(l -> {
                    L.monitor.e("IPCamera Service died !!!");
                    Process.killProcess(Process.myPid());
                }, 0);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    // 设置水印开关
    public void setWaterMarkEnable(boolean enable) {
        if (mUVCameraController != null) {
            try {
                mUVCameraController.setWaterMarkEnable(enable ? 1 : 0);
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }
    }

    //更新时间水印时间
    public void updateWaterMarkTS() {
        if (mUVCameraController != null) {
            try {
                mUVCameraController.updateWaterMarkTS();
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }
    }

    // 设置畸变矫正开关
    public void setDistortionCorrectEnable(boolean enable) {
        if (mUVCameraController != null) {
            try {
                mUVCameraController.setDistortionCorrectEnable(enable ? 1 : 0);
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }
    }

    // 设置算法开关
    public void setAlgoSwitch(byte algoType, boolean isOpen) {
        if (mUVCameraController != null) {
            try {
                mUVCameraController.setAlgoSwitch(algoType, isOpen ? 1 : 0);
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }
    }

    // 设置算法灵敏度
    public void SetAlgoSensitivity(byte algoType, int level) {
        if (mUVCameraController != null) {
            try {
                mUVCameraController.SetAlgoSensitivity(algoType, level);
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }
    }

    public String getVersion() {
        if (mUVCameraController != null) {
            try {
                return mUVCameraController.getVersion();
            } catch (RemoteException e) {
                L.monitor.e("%s getVersion error: %s", TAG, e.getMessage());
            }
        }
        return "9999";
    }
}
