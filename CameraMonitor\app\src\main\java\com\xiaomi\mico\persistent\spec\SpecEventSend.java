package com.xiaomi.mico.persistent.spec;

import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.EventConfig;
import com.xiaomi.mico.persistent.utils.MiotManager;

import org.json.JSONException;

public class SpecEventSend {
    private static final String TAG = "SpecEventSend";

    // 家人看护
    private static final int FAMILY_CARE_SIID = 13;
    private static final int FAMILY_CARE_EIID = 1;
    private static final int FAMILY_CARE_PIID = 2;
    private static final String FAMILY_CARE_VALUE = "prop.s_chuangmi_clocks";

    // AI事件
    private static final int AI_DETECTION_SIID = 10;
    private static final int AI_SOMEONE_APPEAR_EIID = 1;
    private static final int AI_BABY_CRY_EIID = 2;
    private static final int AI_OBJECT_MOTION_EIID = 3;

    // 摄像机控制
    private static final int CAMERA_CONTROL_SIID = 11;
    private static final int IR_TURNED_ON_EIID = 1;
    private static final int IR_TURNED_OFF_EIID = 2;


    /**
     * 家人守护事件上报
     * @param idx 事件名称索引
     */
    public static void eventFamilyCare(int idx) {
        String eventValue = FAMILY_CARE_VALUE + idx;
        EventConfig eventConfig = new EventConfig(FAMILY_CARE_SIID, FAMILY_CARE_EIID, FAMILY_CARE_PIID,
                eventValue);
        eventSend(eventConfig);
    }

    public static void eventAIReport(String eventType) {
        EventConfig eventConfig = new EventConfig();
        eventConfig.setSiid(AI_DETECTION_SIID);
        if (SpecConstants.EVENT_TYPE_PEOPLE_MOTION.equals(eventType)) {
            eventConfig.setEiid(AI_SOMEONE_APPEAR_EIID);
        } else if (SpecConstants.EVENT_TYPE_BABY_CRY.equals(eventType)) {
            eventConfig.setEiid(AI_BABY_CRY_EIID);
        } else if (SpecConstants.EVENT_TYPE_OBJECT_MOTION.equals(eventType)) {
            eventConfig.setEiid(AI_OBJECT_MOTION_EIID);
        }
        eventSend(eventConfig);
    }

    public static void eventIRCurtReport(boolean isOn) {
        EventConfig eventConfig = new EventConfig();
        eventConfig.setSiid(CAMERA_CONTROL_SIID);
        if (isOn) {
            eventConfig.setEiid(IR_TURNED_ON_EIID);
        } else {
            eventConfig.setEiid(IR_TURNED_OFF_EIID);
        }
        eventSend(eventConfig);
    }

    private static void eventSend(EventConfig eventConfig) {
        L.monitor.v("%s reportEvent: %s", TAG, eventConfig);
        try {
            MiotManager.getInstance().miEVentSend(eventConfig);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
