#pragma once

#include "../common/imimedia_include.h"

typedef struct imimp4v2_info_s *imimp4v2_info_t;

#ifdef __cplusplus
extern "C"
{
#endif

int imimp4v2_create_file(const char *path,
	timestamp_correct_id correct,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	unsigned long long duration,
	/*out*/imimp4v2_info_t *handle);

int imimp4v2_write_video_frame(imimp4v2_info_t handle,
	unsigned char *data,
	unsigned int data_len,
	unsigned long long timestamp);

int imimp4v2_write_audio_frame(imimp4v2_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long timestamp);

int imimp4v2_open_file(const char* path,
	video_codec_id *video,
	audio_codec_id *audio,
	unsigned int *vfps,
	unsigned int *vwidth,
	unsigned int *vheight,
	unsigned int *achannel,
	unsigned int *asamplerate,
	unsigned long long *duration,
	/*out*/imimp4v2_info_t *handle);

int imimp4v2_get_frame(imimp4v2_info_t handle,
	unsigned char *data,
	unsigned int size,
	unsigned int *data_len,
	unsigned long long *timestamp,
	frame_type_id *frametype);

int imimp4v2_seek_file(imimp4v2_info_t handle,
	unsigned long long timestamp);

int imimp4v2_close_file(imimp4v2_info_t handle);

#ifdef __cplusplus
}
#endif