package com.xiaomi.camera.monitoring.miss;

import com.xiaomi.camera.monitoring.utils.BuildModelUtils;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.camera.monitoring.utils.SystemIdUtils;

public class MissDeviceInfo {
    public String device_id = "365575396";
    public String device_model = "chuangmi.camera.026c02";
    public String device_key = "x08e";
    public String miio_token;

    public MissDeviceInfo() {
        device_id = SystemIdUtils.getMiDid();
        if (BuildModelUtils.isX10A()) {
            device_model = "xiaomi.wifispeaker.x10a";
            device_key = "x10a";
        } else if (BuildModelUtils.isX6A()) {
            device_model = "xiaomi.wifispeaker.x6a";
            device_key = "x6a";
        } else if (BuildModelUtils.isX8F()) {
            device_model = "xiaomi.wifispeaker.x8f";
            device_key = "x8f";
        } else if (BuildModelUtils.isOH8()) {
            device_model = "xiaomi.wifispeaker.oh8";
            device_key = "oh8";
        }
        L.monitor.d("device model is %s", device_model);
    }
}
