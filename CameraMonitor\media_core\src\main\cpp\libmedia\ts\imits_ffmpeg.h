#pragma once

#include "imimedia_include.h"

typedef struct imits_ffmpeg_info_s *imits_ffmpeg_info_t;

void imits_init_ffmpeg();

int imits_create_file(const char* path,
	container_format_id container,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	/*out*/imits_ffmpeg_info_t* handle);

int imits_write_video_frame(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype);

int imits_write_audio_frame(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp);

int imits_close_file_for_create(imits_ffmpeg_info_t handle);

int imits_open_file(const char* path,
	container_format_id* container,
	video_codec_id* video,
	audio_codec_id* audio,
	unsigned int* vfps,
	unsigned int* vwidth,
	unsigned int* vheight,
	unsigned int* achannel,
	unsigned int* asamplerate,
	unsigned int* abitrate,
	unsigned long long* duration,
	/*out*/imits_ffmpeg_info_t* handle);

int imits_get_frame(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype);

int imits_seek_file(imits_ffmpeg_info_t handle,
	unsigned long long timestamp);

int imits_close_file_for_open(imits_ffmpeg_info_t handle);