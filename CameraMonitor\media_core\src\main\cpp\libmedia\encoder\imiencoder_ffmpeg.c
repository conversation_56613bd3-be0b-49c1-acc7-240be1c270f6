#include <stdlib.h>
#include <string.h>
#ifndef WIN32
#include <pthread.h>
#else
#include "imimedia_win2linux.h"
#endif

#include "imiffmpeg_common.h"
#include "imiparser_aac.h"
#include "imiencoder_ffmpeg.h"

#if defined(WIN32) && !defined(__cplusplus)
#define inline __inline
#endif

#ifdef __cplusplus
extern "C"{
#endif
#include <libavcodec/avcodec.h>
#include <libavutil/opt.h>
#include <libswscale/swscale.h>
#include <libswresample/swresample.h>
#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#pragma comment(lib, "swresample.lib")
#endif

typedef struct imiencoder_ffmpeg_info_s {
#ifndef WIN32
	pthread_mutex_t _lock_mutex;
#endif
	AVCodecContext* _avcodec;
	AVFrame* _avframe;
	unsigned int _index;
	unsigned char *_filldata;
	unsigned int _fillindex;
	unsigned char *_remaindata;
	unsigned int _remainlen;
} imiencoder_ffmpeg_info_s, *imiencoder_ffmpeg_info_t;

void imiencoder_init_ffmpeg()
{
	printf("imiencoder_init_ffmpeg\n");
	avcodec_register_all();
}

AVCodec* _encoder_get_video_codec(video_codec_id codec) {
	AVCodec* avcodec = NULL;
	switch (codec)
	{
	case video_codec_h264:
		avcodec = avcodec_find_encoder(AV_CODEC_ID_H264);
		break;
	case video_codec_h265:
		avcodec = avcodec_find_encoder(AV_CODEC_ID_H265);
		break;
	case video_codec_mjpeg:
		avcodec = avcodec_find_encoder(AV_CODEC_ID_MJPEG);
		break;
	default:
		break;
	}
	return avcodec;
}

int _encoder_video_init(imiencoder_ffmpeg_info_t handle,
	video_codec_id codec,
	pixel_format_id format,
	int width,
	int height,
	int rate,
	int gop,
	int bit_rate,
	int max_b)
{
	int ret = IMIMEDIA_OK;
	AVCodec* avcodec;
	avcodec = _encoder_get_video_codec(codec);
	if (avcodec == NULL) {
		printf("avcodec_find_encoder video codec error %d\n", codec);
		return IMIMEDIA_UNINSTALL_ERROR;
	}
	handle->_avcodec = avcodec_alloc_context3(avcodec);
	if (handle->_avcodec == NULL) {
		printf("avcodec_alloc_context3 video error\n");
		return IMIMEDIA_UNINSTALL_ERROR;
	}
	handle->_avcodec->codec_type = AVMEDIA_TYPE_VIDEO;
	if (handle->_avcodec->codec_id == AV_CODEC_ID_MJPEG) {
		handle->_avcodec->pix_fmt = AV_PIX_FMT_YUVJ420P;
	} else {
		handle->_avcodec->pix_fmt = pixel_format2ffmpeg(format);
		handle->_avcodec->thread_count = 4;
		handle->_avcodec->gop_size = gop + 1;
		handle->_avcodec->max_b_frames = max_b;
		handle->_avcodec->bit_rate = bit_rate;
		handle->_avcodec->qmin = 10;
		handle->_avcodec->qmax = 30;
		av_opt_set(handle->_avcodec->priv_data, "preset", "ultrafast", 0);
	}
	handle->_avcodec->time_base.num = 1;
	handle->_avcodec->time_base.den = rate;
	handle->_avcodec->width = width;
	handle->_avcodec->height = height;
	ret = avcodec_open2(handle->_avcodec, avcodec, NULL);
	if (ret == 0) {
		handle->_avframe = av_frame_alloc();
		handle->_avframe->pict_type = AV_PICTURE_TYPE_NONE;
		handle->_avframe->width = handle->_avcodec->width;
		handle->_avframe->height = handle->_avcodec->height;
		handle->_avframe->format = handle->_avcodec->pix_fmt;
	} else {
		ffmpeg_err2str("imiencoder_open avcodec_open2 video", ret);
		av_free(handle->_avcodec);
		handle->_avcodec =  NULL;
		return ret;  
	}
	return IMIMEDIA_OK;
}

AVCodec* _encoder_get_audio_codec(audio_codec_id codec) {
	AVCodec* avcodec = NULL;
	switch (codec)
	{
	case audio_codec_aac:
		avcodec = avcodec_find_encoder(AV_CODEC_ID_AAC);
		break;
	case audio_codec_g711a:
		avcodec = avcodec_find_encoder(AV_CODEC_ID_PCM_ALAW);
		break;
	case audio_codec_g711u:
		avcodec = avcodec_find_encoder(AV_CODEC_ID_PCM_MULAW);
		break;
	default:
		break;
	}
	return avcodec;
}

int _encoder_audio_init(imiencoder_ffmpeg_info_t handle,
	audio_codec_id codec,
	sample_format_id format,
	int sample_rate,
	int channels)
{
	int ret = IMIMEDIA_OK;
	AVCodec* avcodec;
	avcodec = _encoder_get_audio_codec(codec);
	if (avcodec == NULL) {
		printf("avcodec_find_encoder audio codec error %d\n", codec);
		return IMIMEDIA_UNINSTALL_ERROR;
	}
	handle->_avcodec = avcodec_alloc_context3(avcodec);
	if (handle->_avcodec == NULL) {
		printf("avcodec_alloc_context3 audio error\n");
		return IMIMEDIA_UNINSTALL_ERROR;
	}
	handle->_avcodec->codec_type = AVMEDIA_TYPE_AUDIO;
	handle->_avcodec->sample_rate = sample_rate;
	handle->_avcodec->channels = channels;
	handle->_avcodec->channel_layout = av_get_default_channel_layout(channels);
	handle->_avcodec->bit_rate = 64000;
	switch (handle->_avcodec->codec_id)
	{
	case AV_CODEC_ID_AAC:
		{
			handle->_avcodec->sample_fmt = AV_SAMPLE_FMT_FLTP;
			handle->_avcodec->profile = FF_PROFILE_AAC_LOW;
			handle->_avcodec->level = 0x02;
		}
		break;
	default:
		{
			handle->_avcodec->sample_fmt = sample_format2ffmpeg(format);
		}
		break;
	}
	ret = avcodec_open2(handle->_avcodec, avcodec, NULL);
	if (ret == 0) {
		handle->_avframe = av_frame_alloc();
		handle->_avframe->sample_rate = sample_rate;
		handle->_avframe->channels = channels;
		handle->_avframe->channel_layout = av_get_default_channel_layout(channels);
		handle->_avframe->format = handle->_avcodec->sample_fmt;
	} else {
		ffmpeg_err2str("imiencoder_open avcodec_open2 audio", ret);
		av_free(handle->_avcodec);
		handle->_avcodec =  NULL;
		return ret;  
	}
	return IMIMEDIA_OK;
}

int _encoder_open_inner(imiencoder_ffmpeg_info_t handle,
	imiencoder_codec_info *info)
{
	int ret = IMIMEDIA_OK;
	int codec = info->codec;
	if (codec < AUDIO_CODEC_INDEX) {
		ret = _encoder_video_init(handle,
			(video_codec_id)codec,
			(pixel_format_id)info->format,
			(int)info->width,
			(int)info->height,
			(int)info->rate,
			(int)info->gop,
			(int)info->bit_rate,
			(int)info->max_b);
	} else {
		ret = _encoder_audio_init(handle,
			(audio_codec_id)codec,
			(sample_format_id)info->format,
			(int)info->sample_rate,
			(int)info->channels);
	}
	return ret;
}

int imiencoder_open(imiencoder_codec_info *info,
	/*out*/imiencoder_ffmpeg_info_t *handle)
{
	int ret = IMIMEDIA_OK;
	imiencoder_ffmpeg_info_t handle_impl = NULL;
	handle_impl = (imiencoder_ffmpeg_info_t)malloc(sizeof(imiencoder_ffmpeg_info_s));
	if (handle_impl == NULL) {
		printf("imiencoder_open malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imiencoder_ffmpeg_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _encoder_open_inner(handle_impl, info);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imiencoder_open handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

unsigned char *_fill_in_data_with_aac(imiencoder_ffmpeg_info_t handle, unsigned char* data, unsigned int len, unsigned int smp_len)
{
	if (handle->_filldata == NULL) {
		handle->_filldata = (unsigned char *)malloc(smp_len);
		memset(handle->_filldata, 0, smp_len);
	}
	if (len == smp_len) {
		memcpy(handle->_filldata, data, len);
		return handle->_filldata;
	} else {
		unsigned int resv = smp_len - handle->_fillindex;
		if (handle->_remaindata) {
			memcpy(handle->_filldata, handle->_remaindata, handle->_remainlen);
			handle->_fillindex += handle->_remainlen;
			free(handle->_remaindata);
			handle->_remaindata = NULL;
			handle->_remainlen = 0;
			resv = smp_len - handle->_fillindex;
		}
		if (len > resv) {
			memcpy(handle->_filldata + handle->_fillindex, data, resv);
			handle->_fillindex = 0;
			handle->_remainlen = len - resv;
			handle->_remaindata = (unsigned char *)malloc(handle->_remainlen);
			memcpy(handle->_remaindata, data + resv, handle->_remainlen);
			return handle->_filldata;
		} else {
			memcpy(handle->_filldata + handle->_fillindex, data, len);
			handle->_fillindex += len;
		}
	}
	return NULL;
}

int _encoder_frame(imiencoder_ffmpeg_info_t handle,
	endecoder_frame_info *frame)
{
	int len, got_frame_ptr = 0;
	AVPacket avpacket = { 0 };
	AVFrame* avframe = NULL;
	if (handle->_avcodec == NULL || handle->_avframe == NULL) {
		printf("imiencoder_one_frame null error\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	avframe = handle->_avframe;
	av_init_packet(&avpacket);
	if (handle->_avcodec->codec_type == AVMEDIA_TYPE_VIDEO) {
		AVPicture dst_picture = { 0 };
		int ret = avpicture_alloc(&dst_picture, handle->_avcodec->pix_fmt, handle->_avcodec->width, handle->_avcodec->height);
		if (ret != 0) {
			ffmpeg_err2str("avpicture_alloc", ret);
			return IMIMEDIA_CAPABILITY_ERROR;
		}
		switch (handle->_avcodec->pix_fmt)
		{
		case AV_PIX_FMT_YUVJ420P:
		case AV_PIX_FMT_YUV420P:
			{
				unsigned char *y = frame->raw_data;
				unsigned int ylen = handle->_avcodec->width * handle->_avcodec->height;
				unsigned char *u = frame->raw_data + ylen;
				unsigned int ulen = ((handle->_avcodec->width/2) * (handle->_avcodec->height/2));
				unsigned char *v = frame->raw_data + ylen + ulen;
				unsigned int vlen = ulen;
				memcpy(dst_picture.data[0], y, ylen);
				memcpy(dst_picture.data[2], u, ulen);
				memcpy(dst_picture.data[1], v, vlen);
			}
			break;
		case AV_PIX_FMT_NV12:
			{
				unsigned char *y = frame->raw_data;
				unsigned int ylen = handle->_avcodec->width * handle->_avcodec->height;
				unsigned char *uv = frame->raw_data + ylen;
				unsigned int uvlen = handle->_avcodec->width * handle->_avcodec->height/2;
				memcpy(dst_picture.data[0], y, ylen);
				memcpy(dst_picture.data[1], uv, uvlen);
			}
			break;
		case AV_PIX_FMT_RGB24:
		case AV_PIX_FMT_RGB32:
			{
				unsigned char *rgb = frame->raw_data;
				unsigned int rgblen = frame->raw_data_len;
				memcpy(dst_picture.data[0], rgb, rgblen);
			}
			break;
		default:
			return IMIMEDIA_PARAMS_ERROR;
		}
		*((AVPicture*)avframe) = dst_picture;
		avframe->pts = av_rescale(handle->_index++,
			AV_TIME_BASE * (int64_t)handle->_avcodec->time_base.num,
			handle->_avcodec->time_base.den);
		len = avcodec_encode_video2(handle->_avcodec,
			&avpacket,
			avframe,
			&got_frame_ptr);
		if (got_frame_ptr == 0 || len < 0) {
			avpicture_free(&dst_picture);
			ffmpeg_err2str("imiencoder_one_frame avcodec_encode", len);
			return IMIMEDIA_ENCODE_ERROR;
		}
		memcpy(frame->enc_data, avpacket.data, avpacket.size);
		frame->enc_data_len = avpacket.size;
		if (avpacket.flags & AV_PKT_FLAG_KEY) {
			frame->frame_type = frame_type_i;
		} else {
			frame->frame_type = frame_type_p;
		}
		av_packet_unref(&avpacket);
		avpicture_free(&dst_picture);
	} else if (handle->_avcodec->codec_type == AVMEDIA_TYPE_AUDIO) {
		unsigned int templen = 0;
		unsigned char *tempbuff = NULL;
		if (frame->raw_data && frame->raw_data_len) {
			switch (handle->_avcodec->codec_id)
			{
			case AV_CODEC_ID_AAC:
				{
					int ret = IMIMEDIA_OK;
					SwrContext* swrcontext = NULL;
					unsigned char *filldata = NULL;
					avframe->nb_samples = handle->_avcodec->frame_size;
					templen = av_samples_get_buffer_size(NULL, avframe->channels, avframe->nb_samples, AV_SAMPLE_FMT_FLTP, 1);
					filldata = _fill_in_data_with_aac(handle, frame->raw_data, frame->raw_data_len, templen/2);
					if (filldata == NULL)
						return IMIMEDIA_NOT_ENOUGH_LEN;
					swrcontext = swr_alloc_set_opts(swrcontext,
						avframe->channel_layout, AV_SAMPLE_FMT_FLTP, avframe->sample_rate,
						avframe->channel_layout, sample_format2ffmpeg(frame->sample), avframe->sample_rate,
						0, NULL);
					if (swrcontext == NULL) {
						printf("imiencoder_one_frame swr_alloc_set_opts error\n");
						return IMIMEDIA_PARAMS_ERROR;
					}
					ret = swr_init(swrcontext);
					if (ret < 0) {
						swr_free(&swrcontext);
						printf("imiencoder_one_frame swr_init error\n");
						return IMIMEDIA_CAPABILITY_ERROR;
					}
					tempbuff = (unsigned char *)malloc(templen);
					memset(tempbuff, 0, templen);
					len = swr_convert(swrcontext, &tempbuff, templen, (const uint8_t**)&filldata, avframe->nb_samples);
					swr_free(&swrcontext);
					if (len <= 0){
						ffmpeg_err2str("imiencoder_one_frame swr_convert audio", len);
						return IMIMEDIA_FORMAT_ERROR;
					}
					avcodec_fill_audio_frame(avframe,
						avframe->channels,
						AV_SAMPLE_FMT_FLTP,
						tempbuff,
						templen,
						1);
				}
				break;
			case AV_CODEC_ID_PCM_ALAW:
			case AV_CODEC_ID_PCM_MULAW:
				{
					avframe->nb_samples = frame->raw_data_len/2;
					avcodec_fill_audio_frame(avframe,
						avframe->channels,
						sample_format2ffmpeg(frame->sample),
						frame->raw_data,
						frame->raw_data_len,
						1);
				}
				break;
			}
		} else {
			avframe = NULL;
		}
		len = avcodec_encode_audio2(handle->_avcodec,
			&avpacket,
			avframe,
			&got_frame_ptr);
		if (tempbuff)
			free(tempbuff);
		if (got_frame_ptr == 0 || len < 0) {
			ffmpeg_err2str("imiencoder_one_frame avcodec_encode", len);
			return IMIMEDIA_ENCODE_ERROR;
		}
		switch (handle->_avcodec->codec_id)
		{
		case AV_CODEC_ID_AAC:
			{
				int headerlen = AAC_ADTS_HEADER;
				unsigned char* header = imi_make_aac_header_net(handle->_avcodec->sample_rate,
					handle->_avcodec->channels,
					avpacket.size + headerlen);
				memcpy(frame->enc_data, header, headerlen);
				memcpy(frame->enc_data + headerlen, avpacket.data, avpacket.size);
				frame->enc_data_len = avpacket.size + headerlen;
				free(header);
			}
			break;
		default:
			{
				memcpy(frame->enc_data, avpacket.data, avpacket.size);
				frame->enc_data_len = avpacket.size;
			}
			break;
		}
		frame->frame_type = frame_type_audio;
		av_packet_unref(&avpacket);
	} else {
		printf("imiencoder_one_frame codec_type error %d\n", handle->_avcodec->codec_type);
		return IMIMEDIA_UNKNOWN_ERROR;
	}
	return IMIMEDIA_OK;
}

int imiencoder_one_frame(imiencoder_ffmpeg_info_t handle,
	endecoder_frame_info *frame)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _encoder_frame(handle, frame);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

void _release_fill_data(imiencoder_ffmpeg_info_t handle) {
	if (handle->_filldata) {
		free(handle->_filldata);
		handle->_filldata = NULL;
	}
	handle->_fillindex = 0;
	if (handle->_remaindata) {
		free(handle->_remaindata);
		handle->_remaindata = NULL;
	}
	handle->_remainlen = 0;
}

int _encoder_close_inner(imiencoder_ffmpeg_info_t handle) {
	_release_fill_data(handle);
	if (handle->_avcodec) {
		avcodec_close(handle->_avcodec);
		av_free(handle->_avcodec);
		handle->_avcodec =  NULL;
	}
	if (handle->_avframe) {
		av_free(handle->_avframe);
		handle->_avframe = NULL;
	}
	return IMIMEDIA_OK;
}

int imiencoder_close(imiencoder_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	printf("imiencoder_close handle = %x\n", handle);
	ret = _encoder_close_inner(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	pthread_mutex_destroy(&handle->_lock_mutex);
	free(handle);
	return ret;
}
