package com.xiaomi.mico.persistent.entry;

public class CloudUploadEntry {
    private byte[] videoByte;
    private String videoName;
    private String eventType;
    private boolean isEnd = false;
    private int offset = 0;
    private boolean ignoreEvent = false;
    private boolean nextFieldId = false; // 下一个fieldId上传
    private String videoSign;
    private PreUploadEntry preUploadEntry;

    public CloudUploadEntry(String videoName, boolean ignoreEvent) {
        this.videoName = videoName;
        this.ignoreEvent = ignoreEvent;
    }

    public CloudUploadEntry(byte[] videoByte, String videoSign, String eventType, boolean isEnd, int offset, boolean ignoreEvent) {
        this.videoByte = videoByte;
        this.videoSign = videoSign;
        this.eventType = eventType;
        this.isEnd = isEnd;
        this.offset = offset;
        this.ignoreEvent = ignoreEvent;
    }

    public byte[] getVideoByte() {
        return videoByte;
    }

    public void setVideoByte(byte[] videoByte) {
        this.videoByte = videoByte;
    }

    public String getVideoName() {
        return videoName;
    }

    public void setVideoName(String videoName) {
        this.videoName = videoName;
    }

    public String getVideoSign() {
        return videoSign;
    }

    public void setVideoSign(String videoSign) {
        this.videoSign = videoSign;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public boolean isEnd() {
        return isEnd;
    }

    public void setEnd(boolean end) {
        isEnd = end;
    }

    public int getOffset() {
        return offset;
    }

    public void setOffset(int offset) {
        this.offset = offset;
    }

    public boolean isIgnoreEvent() {
        return ignoreEvent;
    }

    public void setIgnoreEvent(boolean ignoreEvent) {
        this.ignoreEvent = ignoreEvent;
    }

    public boolean isNextFieldId() {
        return nextFieldId;
    }

    public void setNextFieldId(boolean nextFieldId) {
        this.nextFieldId = nextFieldId;
    }

    public PreUploadEntry getPreUploadEntry() {
        return preUploadEntry;
    }

    public void setPreUploadEntry(PreUploadEntry preUploadEntry) {
        this.preUploadEntry = preUploadEntry;
    }
}
