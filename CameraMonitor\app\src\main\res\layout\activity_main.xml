<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <Button
            android:id="@+id/btn_miss"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="注册miss" />

        <Button
            android:id="@+id/btn_cloud_upload"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="CLOUD UPLOAD" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <Button
            android:id="@+id/btn_mp4"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="media_core 测试" />

        <Button
            android:id="@+id/btn_decoder"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="MediaCodec Decoder" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <Button
            android:id="@+id/btn_audio"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="Audio Test" />

        <Button
            android:id="@+id/btn_uvc"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="UVC Camera" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <Button
            android:id="@+id/btn_event_start"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="Event start" />

        <Button
            android:id="@+id/btn_event_stop"
            android:layout_width="match_parent"
            android:layout_height="80dp"
            android:layout_weight="1"
            android:text="Event stop" />
    </LinearLayout>

    <SurfaceView
        android:id="@+id/decode_surface"
        android:layout_width="300dip"
        android:layout_height="300dip"/>

</LinearLayout>