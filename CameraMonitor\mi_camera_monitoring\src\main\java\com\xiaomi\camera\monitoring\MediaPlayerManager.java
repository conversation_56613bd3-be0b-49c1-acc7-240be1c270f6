package com.xiaomi.camera.monitoring;

import android.content.Context;
import android.media.AudioAttributes;
import android.media.AudioManager;
import android.media.MediaPlayer;

import com.xiaomi.camera.monitoring.utils.L;

/**
 * MediaPlayer播放音频
 */
public class MediaPlayerManager {

    private final String TAG = "MediaPlayerManager";

    private volatile static MediaPlayerManager mInstance;

    private MediaPlayer mMediaPlayer;
    private AudioAttributes mAudioAttributes;
    private AudioManager mAudioManager;

    public static MediaPlayerManager getInstance() {
        if (mInstance == null) {
            synchronized (MediaPlayerManager.class) {
                if (mInstance == null) {
                    mInstance = new MediaPlayerManager();
                }
            }
        }
        return mInstance;
    }

    private MediaPlayerManager() {
        mAudioAttributes = new AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_MEDIA)
                .setContentType(AudioAttributes.CONTENT_TYPE_MUSIC)
                .build();
    }

    public synchronized void startPlay(Context context, int id) {
        L.monitor.d("%s =====startPlay=====", TAG);
        stopPlay();
        if (mAudioManager == null) {
            mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        }
        mAudioManager.requestAudioFocus(null, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
        mMediaPlayer = MediaPlayer.create(context, id);
        mMediaPlayer.setAudioAttributes(mAudioAttributes);
        mMediaPlayer.setLooping(false);
        mMediaPlayer.setOnCompletionListener(mp -> stopPlay());
        mMediaPlayer.start();
    }

    public synchronized void stopPlay() {
        L.monitor.d("%s =====stopPlay=====", TAG);
        try {
            if (mAudioManager != null) {
                mAudioManager.abandonAudioFocus(null);
            }
            if (mMediaPlayer != null) {
                mMediaPlayer.stop();
                mMediaPlayer.release();
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mMediaPlayer = null;
        }
    }
}
