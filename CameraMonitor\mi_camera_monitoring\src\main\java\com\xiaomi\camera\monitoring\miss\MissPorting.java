package com.xiaomi.camera.monitoring.miss;

/**
 *  miss porting 回调
 **/
public interface MissPorting {

    //  int miss_ack_send(const char *buf, int length);
    int cb_miss_ack_send(byte[] buf, int length);


    //  int miss_rpc_send(void *rpc_id, const char *method, const char *params);
    int cb_miss_rpc_send(int rpc_id, String method,String params);


    //  int miss_statistics(miss_session_t *session, void *data, int length);
    int cb_miss_statistics(final int rpc_id, String method, String params);


    //  int miss_on_connect(miss_session_t *session, void **user_data);
    int cb_miss_on_connect(int session);


    //  int miss_on_disconnect(miss_session_t *session, miss_error_e error, void *user_data);
    int cb_miss_on_disconnect(int session);


    //  int miss_on_error(miss_session_t *session, miss_error_e error, void *user_data);
    int cb_miss_on_error(int session, int error, byte[] user_data);


    //  void miss_on_video_data(miss_session_t *session, miss_frame_header_t *frame_header, void *data, void *user_data);
    void cb_miss_on_video_data(byte[] data, int len, int isKeyFrame);

    //  void miss_on_audio_data(miss_session_t *session,miss_frame_header_t *frame_header, void *data, void *user_data);
    void cb_miss_on_audio_data(byte[] data, int len, int sample, int data_bits, int code_id);


    //  void miss_on_rdt_data(miss_session_t *session, void *rdt_data, uint32_t length, void *user_data);
    void cb_miss_on_rdt_data(int session, byte[] rdt_data, int length, byte[] user_data);


    //  void miss_on_cmd(miss_session_t *session, miss_cmd_e cmd,void *params, unsigned int length, void *user_data);
    void cb_miss_on_cmd(int session, int cmd, String params, int length, byte[] user_data);


    //  int miss_on_server_ready();
    void cb_miss_on_server_ready();


    // void miss_encrypt_data (miss_encrypt_param_t *encrypt_param);
    void cb_miss_encrypt_data(byte[] encrypt_param);


    //  void miss_decrypt_data  (miss_encrypt_param_t *decrypt_param);
    void cb_miss_decrypt_data(byte[] decrypt_param);
}
