package com.xiaomi.mico.persistent.func;

import android.app.Notification;
import android.app.Service;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.IBinder;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;

import androidx.annotation.Nullable;

import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.monitor.NotificationChannelUtils;
import com.xiaomi.mico.persistent.monitor.R;

public class FloatSoundBoxService extends Service {
    private static final String TAG = "FloatSoundBoxService";
    public static final int FOREGROUND_NOTIFY_ID_START_FLOAT = 3;

    private WindowManager windowManager;
    private View floatingView;

    @Override
    public void onCreate() {
        super.onCreate();
        L.monitor.d("%s onCreate", TAG);
        Notification notification = NotificationChannelUtils.createServiceNotification(this, "");
        startForeground(FOREGROUND_NOTIFY_ID_START_FLOAT, notification);

        // 初始化WindowManager
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);

        // 加载悬浮窗口布局
        floatingView = LayoutInflater.from(this).inflate(R.layout.floating_window, null);

        // 设置悬浮窗口参数
        WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.WRAP_CONTENT,
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
                WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
                PixelFormat.TRANSLUCENT);

        // 设置悬浮窗口位置
        params.gravity = Gravity.TOP | Gravity.END;
        params.x = 20;
        params.y = 20;

        // 添加悬浮窗口
        windowManager.addView(floatingView, params);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        L.monitor.d("%s onDestroy", TAG);
        if (floatingView != null) {
            windowManager.removeView(floatingView);
        }
        floatingView = null;
        windowManager = null;
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
}
