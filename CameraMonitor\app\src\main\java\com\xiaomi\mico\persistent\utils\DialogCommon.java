package com.xiaomi.mico.persistent.utils;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.xiaomi.mico.persistent.monitor.R;

public class DialogCommon {

    private Activity activity;
    private ViewGroup contentView;
    private View view;
    public DialogCommon(Activity activity) {
        this.activity=activity;
        contentView = activity.findViewById(android.R.id.content);
        view= LayoutInflater.from(activity).inflate(R.layout.layout_common_dialog,null);
        view.findViewById(R.id.cancel_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
        view.findViewById(R.id.ok_btn).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
            }
        });
    }

    /**
     * 显示dialog(包含动画)
     */
    public void show(){
        contentView.addView(view);
    }

    /**
     * 移除dialog(包含动画)
     */
    public void dismiss(){
        contentView.removeView(view);
    }
}
