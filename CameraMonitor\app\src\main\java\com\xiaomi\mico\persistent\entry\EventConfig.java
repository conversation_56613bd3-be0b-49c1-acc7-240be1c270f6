package com.xiaomi.mico.persistent.entry;

public class EventConfig {
    private int siid;
    private int eiid;
    private int piid;
    private Object property_value;

    public EventConfig(){}

    public EventConfig(int siid, int eiid) {
        this.siid = siid;
        this.eiid = eiid;
    }

    public EventConfig(int siid, int eiid, int piid, Object property_value) {
        this.siid = siid;
        this.eiid = eiid;
        this.piid = piid;
        this.property_value = property_value;
    }

    public int getSiid() {
        return siid;
    }

    public void setSiid(int siid) {
        this.siid = siid;
    }

    public int getEiid() {
        return eiid;
    }

    public void setEiid(int eiid) {
        this.eiid = eiid;
    }

    public int getPiid() {
        return piid;
    }

    public void setPiid(int piid) {
        this.piid = piid;
    }

    public Object getProperty_value() {
        return property_value;
    }

    public void setProperty_value(Object property_value) {
        this.property_value = property_value;
    }

    @Override
    public String toString() {
        return "EventConfig{" +
                "siid=" + siid +
                ", eiid=" + eiid +
                ", piid=" + piid +
                ", property_value=" + property_value +
                '}';
    }
}
