package com.xiaomi.camera.monitoring.utils;

import android.text.TextUtils;

public class SystemIdUtils {

    private static String miDid;

    public static String getMiDid() {
        if (TextUtils.isEmpty(miDid)) {
            try {
                miDid =
                        (String)
                                ReflectUtils.forName("android.os.SystemProperties")
                                        .getMethod("get", String.class)
                                        .invoke(null, "mi.did");
            } catch (Exception e) {
                L.monitor.e("SystemIdUtils invoke get system properties throw exception: %s", e.getMessage());
            }
        }
        return miDid;
    }
}
