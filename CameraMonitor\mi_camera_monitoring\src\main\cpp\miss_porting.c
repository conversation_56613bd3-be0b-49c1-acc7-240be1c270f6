#include <jni.h>
#include <string.h>
#include <android/log.h>
#include <stdlib.h>
#include <unistd.h>

#include "miss/miss.h"
#include "miss/miss_porting.h"

#define LOGD(...)  __android_log_print(ANDROID_LOG_DEBUG, "CameraMonitoring JNI ---->> ", __VA_ARGS__)
#define LOGE(...)  __android_log_print(ANDROID_LOG_DEBUG, "CameraMonitoring JNI---->> ", __VA_ARGS__)

extern JavaVM *gJavaVM;
extern jobject gJavaObj;

#define RET_SUCC                (0)
#define RET_FAIL                (-1)

#if 1
#define MAX_RECV_G711A_LEN      (640)
#define MAX_PCM_DATA_LEN        (1280)

#define MAX_CLIENT_NUMBER       2

char g_encord_g711a_data[640] = {0};
char g_decord_pcm_data[2048] = {0};

void *p_rpc_id;
miss_session_t *sessions[MAX_CLIENT_NUMBER] = {NULL};

static short aLawDecompressTable[] = {-5504, -5248,
                                      -6016, -5760, -4480, -4224, -4992, -4736, -7552, -7296, -8064,
                                      -7808, -6528, -6272, -7040, -6784, -2752, -2624, -3008, -2880,
                                      -2240, -2112, -2496, -2368, -3776, -3648, -4032, -3904, -3264,
                                      -3136, -3520, -3392, -22016, -20992, -24064, -23040, -17920,
                                      -16896, -19968, -18944, -30208, -29184, -32256, -31232,
                                      -26112,
                                      -25088, -28160, -27136, -11008, -10496, -12032, -11520, -8960,
                                      -8448, -9984, -9472, -15104, -14592, -16128, -15616, -13056,
                                      -12544, -14080, -13568, -344, -328, -376, -360, -280, -264,
                                      -312,
                                      -296, -472, -456, -504, -488, -408, -392, -440, -424, -88,
                                      -72,
                                      -120, -104, -24, -8, -56, -40, -216, -200, -248, -232, -152,
                                      -136,
                                      -184, -168, -1376, -1312, -1504, -1440, -1120, -1056, -1248,
                                      -1184,
                                      -1888, -1824, -2016, -1952, -1632, -1568, -1760, -1696, -688,
                                      -656,
                                      -752, -720, -560, -528, -624, -592, -944, -912, -1008, -976,
                                      -816,
                                      -784, -880, -848, 5504, 5248, 6016, 5760, 4480, 4224, 4992,
                                      4736,
                                      7552, 7296, 8064, 7808, 6528, 6272, 7040, 6784, 2752, 2624,
                                      3008,
                                      2880, 2240, 2112, 2496, 2368, 3776, 3648, 4032, 3904, 3264,
                                      3136,
                                      3520, 3392, 22016, 20992, 24064, 23040, 17920, 16896, 19968,
                                      18944,
                                      30208, 29184, 32256, 31232, 26112, 25088, 28160, 27136, 11008,
                                      10496, 12032, 11520, 8960, 8448, 9984, 9472, 15104, 14592,
                                      16128,
                                      15616, 13056, 12544, 14080, 13568, 344, 328, 376, 360, 280,
                                      264,
                                      312, 296, 472, 456, 504, 488, 408, 392, 440, 424, 88, 72, 120,
                                      104,
                                      24, 8, 56, 40, 216, 200, 248, 232, 152, 136, 184, 168, 1376,
                                      1312,
                                      1504, 1440, 1120, 1056, 1248, 1184, 1888, 1824, 2016, 1952,
                                      1632,
                                      1568, 1760, 1696, 688, 656, 752, 720, 560, 528, 624, 592, 944,
                                      912,
                                      1008, 976, 816, 784, 880, 848};

static int cClip = 32635;
static char aLawCompressTable[] = {1, 1, 2, 2, 3, 3, 3,
                                   3, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
                                   5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
                                   6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7,
                                   7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
                                   7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
                                   7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7};

static char g711a_linearToALawSample(short sample) {
    int sign;
    int exponent;
    int mantissa;
    int s;
    sign = ((~sample) >> 8) & 0x80;
    if (!(sign == 0x80)) {
        sample = (short) -sample;
    }
    if (sample > cClip) {
        sample = cClip;
    }
    if (sample >= 256) {
        exponent = (int) aLawCompressTable[(sample >> 8) & 0x7F];
        mantissa = (sample >> (exponent + 3)) & 0x0F;
        s = (exponent << 4) | mantissa;
    } else {
        s = sample >> 4;
    }
    s ^= (sign ^ 0x55);
    return (char) s;
}

static int g711a_Encode(char *src, int offset, int len, char *res) {
    int j = offset;
    int count = len / 2;
    short sample = 0;
    int i = 0;
    for (i = 0; i < count; i++) {
        sample = (short) (((src[j++] & 0xff) | (src[j++]) << 8));
        res[i] = g711a_linearToALawSample(sample);
    }
    return count;
}

static int g711a_decode(char *src, int offset, int len, char *res) {
    int j = 0;
    int tmp_offset = 0;
    int i = 0;
    for (i = 0; i < len; i++) {
        short s = aLawDecompressTable[src[i + tmp_offset] & 0xff];
        res[j++] = (char) s;
        res[j++] = (char) (s >> 8);
    }
    return j;
}

#endif

/*------------------------------------------*/

int miss_ack_send(const char *buf, int length) {
    LOGE("[%s][%d], =====", __func__, __LINE__);
    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_ack_send", "([BI)I");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }

    char buffer[64] = "hello world !!!";
    int len = 64;
    (*env)->CallVoidMethod(env, gJavaObj, javaCallback, buffer, len);

    return RET_SUCC;
}

jstring charTojstring(JNIEnv *env, const char *pat) {
    //定义java String类 strClass
    jclass strClass = (*env)->FindClass(env, "java/lang/String");
    //获取String(byte[],String)的构造器,用于将本地byte[]数组转换为一个新String
    jmethodID ctorID = (*env)->GetMethodID(env, strClass, "<init>", "([BLjava/lang/String;)V");
    //建立byte数组
    jbyteArray bytes = (*env)->NewByteArray(env, strlen(pat));
    //将char* 转换为byte数组
    (*env)->SetByteArrayRegion(env, bytes, 0, strlen(pat), (jbyte *) pat);
    // 设置String, 保存语言类型,用于byte数组转换至String时的参数
    jstring encoding = (*env)->NewStringUTF(env, "UTF-8");
    //将byte数组转换为java String,并输出
    jstring result = (jstring) (*env)->NewObject(env, strClass, ctorID, bytes, encoding);
    return result;
}

jstring charTojstringExc(JNIEnv *env, const void *pat, int len) {
    //定义java String类 strClass
    jclass strClass = (*env)->FindClass(env, "java/lang/String");

    //获取String(byte[],String)的构造器,用于将本地byte[]数组转换为一个新String
    jmethodID ctorID = (*env)->GetMethodID(env, strClass, "<init>", "([BLjava/lang/String;)V");

    //建立byte数组
    jbyteArray bytes = (*env)->NewByteArray(env, len);

    //将char* 转换为byte数组
    (*env)->SetByteArrayRegion(env, bytes, 0, len, (jbyte *) pat);

    // 设置String, 保存语言类型,用于byte数组转换至String时的参数
    jstring encoding = (*env)->NewStringUTF(env, "UTF-8");

    //将byte数组转换为java String,并输出
    jstring result = (jstring) (*env)->NewObject(env, strClass, ctorID, bytes, encoding);

    return result;
}

int miss_rpc_send(void *rpc_id, const char *method, const char *params) {
    LOGE("[%s][%d], =====", __func__, __LINE__);
    LOGE("[%s][%d], rpc_id:%p, method:%s, params:%s ", __func__, __LINE__, rpc_id, method, params);
    p_rpc_id = rpc_id;

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_rpc_send",
                                                 "(ILjava/lang/String;Ljava/lang/String;)I");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }

    int rpcid = (int) rpc_id;
    jstring methodStr = charTojstring(env, method);
    jstring paramStr = charTojstring(env, params);
    (*env)->CallIntMethod(env, gJavaObj, javaCallback, rpcid, methodStr, paramStr);

    return RET_SUCC;
}


int miss_statistics(miss_session_t *session, void *data, int length) {
    LOGD("[%s][%d], =====", __func__, __LINE__);
    LOGD("msg_id:%d, strLen:%d , len:%d ,data:|%s|", rand(), strlen((char *) data), length,
         (char *) data);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_statistics",
                                                 "(ILjava/lang/String;Ljava/lang/String;)I");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }

    int rpcid = (int) rand();
    jstring methodStr = charTojstring(env, "_sync.camera_perf_data");
    jstring paramStr = charTojstringExc(env, (char *) data, length);

    (*env)->CallIntMethod(env, gJavaObj, javaCallback, rpcid, methodStr, paramStr);

    return 0;
}

int miss_on_connect(miss_session_t *session, void **user_data) {
    LOGD("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_connect", "(I)I");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }

    int miss_session = (int) session;
    // cb_miss_on_connect 返回当前已在线用户数(不计算当前请求的session)
    int online_sessionnum = (*env)->CallIntMethod(env, gJavaObj, javaCallback, miss_session);
    int *data = malloc(sizeof(int));
    *data = MAX_CLIENT_NUMBER - online_sessionnum;
    *user_data = data;

    // 添加session到集合
    LOGD("online session num is [%d]", online_sessionnum);
    sessions[online_sessionnum - 1] = session;

    return 0;
}


int miss_on_disconnect(miss_session_t *session, miss_error_e error, void *user_data) {
    LOGE("[%s][%d], =====", __func__, __LINE__);

    if (NULL == user_data) {
        miss_server_session_close(session);
        LOGE("[%s][%d], =====", __func__, __LINE__);
        return 0;
    }

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_disconnect", "(I)I");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }

    int sessionnum = *(int *) (user_data);
    if (sessionnum < 0) {
        LOGE("get sessionnum failed. sessionnum = %d\n", sessionnum);
        return -1;
    } else {
        LOGD("get sessionnum succeed. sessionnum = %d, session = %p\n", sessionnum, session);
    }

    int miss_session = (int) session;
    (*env)->CallIntMethod(env, gJavaObj, javaCallback, miss_session);

    miss_server_session_close(session);
    usleep(200);
    if (user_data) {
        free(user_data);
        user_data = NULL;
    }

    // 把session从集合移除
    int i = 0;
    for (; i < MAX_CLIENT_NUMBER; i++) {
        LOGD("remove session, sessionnum = %d, session = %p\n", (int) sessions[i], sessions[i]);
        if (NULL != sessions[i] && miss_session == (int) sessions[i]) {
            if (i >= MAX_CLIENT_NUMBER - 1) {
                sessions[i] = NULL;
            } else {
                int j = i;
                for (; j < MAX_CLIENT_NUMBER - 1; j++) {
                    sessions[j] = sessions[j + 1];
                }
                sessions[MAX_CLIENT_NUMBER - 1] = NULL;
            }
            break;
        }
    }

    return 0;
}


int miss_on_error(miss_session_t *session, miss_error_e error, void *user_data) {
    LOGE("[%s][%d], =====, ret:%d", __func__, __LINE__, error);

    if (error == MISS_ERR_TIMOUT || error == MISS_ERR_ABORTED) {
        LOGD("miss_server_finish() by errorcode:%d\n", error);
    }

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_error", "(II[B)I");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }

    return 0;
}

int is_key_frame(miss_frame_header_t *frame_header) {
    // 提取 FrameType 字段
    flag_frame_type_e frameType = (flag_frame_type_e)(frame_header->flags & 0x7);
    // 比较 FrameType 是否为 FLAG_FRAME_TYPE_IFRAME
    return frameType == FLAG_FRAME_TYPE_IFRAME;
}

void miss_on_video_data(miss_session_t *session,
                        miss_frame_header_t *frame_header, void *data, void *user_data) {
//    LOGE("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_video_data",
                                                 "([BII)V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return;
    }

    int isKeyFrame = (frame_header->flags & FLAG_FRAME_TYPE_IFRAME) != 0;
    int length = frame_header->length;
    jbyteArray array = (*env)->NewByteArray(env, length);

    (*env)->SetByteArrayRegion(env, array, 0, length, data);
    (*env)->CallVoidMethod(env, gJavaObj, javaCallback, array, length, isKeyFrame);

    (*env)->DeleteLocalRef(env, array);

    return;
}

void miss_on_audio_data(miss_session_t *session,
                        miss_frame_header_t *frame_header, void *data, void *user_data) {
//    LOGE("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_audio_data",
                                                 "([BIIII)V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return;
    }

    int audio_sample = FLAG_AUDIO_SAMPLE_16K;
    int audio_codec_id = frame_header->codec_id;
    int audio_databits = FLAG_AUDIO_DATABITS_16;

    int length = frame_header->length;
    jbyteArray array = (*env)->NewByteArray(env, length);

    (*env)->SetByteArrayRegion(env, array, 0, length, data);
    (*env)->CallVoidMethod(env, gJavaObj, javaCallback, array, length, audio_sample,
                           audio_databits, audio_codec_id);

    (*env)->DeleteLocalRef(env, array);
    return;
}

void miss_on_rdt_data(miss_session_t *session, void *rdt_data, uint32_t length, void *user_data) {
    LOGE("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_rdt_data",
                                                 "(I[BI[B)V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return;
    }

    return;
}


void miss_on_cmd(miss_session_t *session, miss_cmd_e cmd,
                 void *params, unsigned int length, void *user_data) {
    LOGE("[%s][%d], =====, cmd:%d ", __func__, __LINE__, cmd);

//    int sessionnum = *(int *)user_data;
    int miss_session = (int) session;

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_cmd",
                                                 "(IILjava/lang/String;I[B)V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return;
    }

    jstring paramStr = charTojstringExc(env, (char *) params, length);

    /*param使用string，长度使用str.length获取，length参数传参为0*/
    (*env)->CallVoidMethod(env, gJavaObj, javaCallback, miss_session, (int) cmd, paramStr, 0, NULL);
}


int miss_on_server_ready() {
    LOGE("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return 0;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_on_server_ready", "()V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return RET_FAIL;
    }
    (*env)->CallVoidMethod(env, gJavaObj, javaCallback);

    return 0;
}


void miss_encrypt_data(miss_encrypt_param_t *encrypt_param) {
    LOGE("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_encrypt_data", "([B)V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return;
    }

    return;
}

void miss_decrypt_data(miss_encrypt_param_t *decrypt_param) {
    LOGE("[%s][%d], =====", __func__, __LINE__);

    JNIEnv *env;
    /* 从全局的JavaVM中获取到环境变量 */
    (*gJavaVM)->AttachCurrentThread(gJavaVM, &env, NULL);

    /* 获取Java层对应的类 */
    jclass javaClass = (*env)->GetObjectClass(env, gJavaObj);
    if (NULL == javaClass) {
        LOGD("Fail to find javaClass");
        return;
    }

    /* 获取Java层被回调的函数 */
    jmethodID javaCallback = (*env)->GetMethodID(env, javaClass, "cb_miss_decrypt_data", "([B)V");
    if (NULL == javaCallback) {
        LOGE("[%s][%d], get callback fail", __func__, __LINE__);
        return;
    }

    return;
}

void *getP_Rpc_id(int rpc_id) {
    return p_rpc_id;
}

void *getP_Session(int session) {
    int i = 0;
    for (; i < MAX_CLIENT_NUMBER; i++) {
        if (NULL != sessions[i] && session == (int) sessions[i]) {
            return sessions[i];
        }
    }
    LOGE("get session return NULL !!!!!!");
    return NULL;
}

void clearP_Session() {
    int i = 0;
    for (; i < MAX_CLIENT_NUMBER; i++) {
        sessions[i] = NULL;
    }
}