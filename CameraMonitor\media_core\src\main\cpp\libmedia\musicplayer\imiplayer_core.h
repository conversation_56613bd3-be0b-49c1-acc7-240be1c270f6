#pragma once

typedef enum
{
	imifiles_event_begin_of_file = 0,
	imifiles_event_end_of_file,
	imifiles_event_open_fail,
	imifiles_event_data_fail
}imifiles_event;

typedef enum
{
	imifiles_sequence_onebyone = 0,
	imifiles_sequence_other
}imifiles_sequence;

typedef struct _imifiles_list {
	char* path;
}imifiles_list;

typedef int (*imiplayer_callback_event)(const char* file, unsigned int file_index, imifiles_event event, unsigned int id, void* userdata);

void imiplayer_init();

int imiplayer_openfiles(const imifiles_list* files, unsigned int files_count, imifiles_sequence files_seq);

int imiplayer_closefiles();

int imiplayer_addfile(char* path);

int imiplayer_removefile(char* path);

int imiplayer_play(unsigned int assign_resample, float assign_volume, imiplayer_callback_event fproc, unsigned int id, void* userdata);

int imiplayer_stop();

int imiplayer_pause();

int imiplayer_resume();

int imiplayer_seek(unsigned int timestamp);

int imiplayer_get_duration();

int imiplayer_get_postion();

int imiplayer_next_file();

int imiplayer_previous_file();

int imiplayer_volume(float volume);