package com.xiaomi.camera.monitoring;

import com.xiaomi.camera.monitoring.utils.L;

/**
 * 通过UVC获取Camera1数据
 */
public class UVCCameraManager {

    private final String TAG = "UVCCameraManager";

    private volatile static UVCCameraManager mInstance;
    private boolean isCameraRunning = false;

    public static UVCCameraManager getInstance() {
        if (mInstance == null) {
            synchronized (UVCCameraManager.class) {
                if (mInstance == null) {
                    mInstance = new UVCCameraManager();
                }
            }
        }
        return mInstance;
    }

    private UVCCameraManager() {}

    public synchronized boolean isCameraRunning() {
        return isCameraRunning;
    }

    public synchronized boolean startCamera() {
        L.monitor.i("%s =====startCamera===== isCameraRunning: %s", TAG, isCameraRunning);
        if (isCameraRunning) {
            return true;
        }
        boolean startUVC0 = UVCCamera0VideoManager.getInstance().startCamera();
        boolean startUVC1 = UVCCamera1VideoManager.getInstance().startCamera();
        isCameraRunning = startUVC0 && startUVC1;
        if (!isCameraRunning) {
            L.monitor.e("%s Start UVC camera failed.", TAG);
            forceStopCamera();
        }
        return  isCameraRunning;
    }

    public synchronized void stopCamera() {
        L.monitor.i("%s =====stopCamera===== isCameraRunning: %s", TAG, isCameraRunning);
        if (!isCameraRunning) {
            return;
        }
        isCameraRunning = false;
        UVCCamera0VideoManager.getInstance().stopCamera();
        UVCCamera1VideoManager.getInstance().stopCamera();
    }

    private synchronized void forceStopCamera() {
        L.monitor.i("%s =====forceStopCamera=====", TAG);
        UVCCamera0VideoManager.getInstance().stopCamera();
        UVCCamera1VideoManager.getInstance().stopCamera();
    }
}
