package com.xiaomi.mico.persistent.func;

import android.content.Context;
import android.os.Handler;
import android.os.HandlerThread;
import android.provider.Settings;
import android.text.TextUtils;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.UVCCameraController;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;


public class CameraUvcVersion {
    private static final String TAG = "CameraUvcVersion";

    private Context mContext;
    private volatile static CameraUvcVersion mInstance;
    private static final long THREAD_SLEEP_TIME = 5 * 60 * 1000;
    private boolean isUpdating = false;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;

    public static CameraUvcVersion getInstance() {
        if (mInstance == null) {
            synchronized (CameraUvcVersion.class) {
                if (mInstance == null) {
                    mInstance = new CameraUvcVersion();
                }
            }
        }
        return mInstance;
    }

    public void init(Context context) {
        mContext = context;
        String version = UVCCameraController.getInstance().getVersion();
        L.monitor.i("%s Init UVC version: %s", TAG, version);
        Settings.Global.putString(context.getContentResolver(), Constants.KEY_UVC_VERSION, version);
        String uvcUpdated = CommonApiUtils.getSpfConfig(context, Constants.KEY_UVC_VERSION_UPDATE);
        L.monitor.i("%s Init UVC version update: %s", TAG, uvcUpdated);
        if (!TextUtils.isEmpty(uvcUpdated) && uvcUpdated.equals("0")) {
            updateUvcVersion();
        }
    }

    public void updateUvcVersion() {
        if (isUpdating) {
            L.monitor.i("%s Update UVC Version is already running.", TAG);
            return; // 如果正在更新，直接返回
        }
        L.monitor.i("%s Update UVC Version.", TAG);
        isUpdating = true; // 标记为正在更新
        mHandlerThread = new HandlerThread("UVCVersionBg");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        mBackgroundHandler.postDelayed(() -> {
            String version = UVCCameraController.getInstance().getVersion();
            L.monitor.i("%s Update UVC version: %s", TAG, version);
            Settings.Global.putString(mContext.getContentResolver(), Constants.KEY_UVC_VERSION, version);
            CommonApiUtils.setSpfConfig(mContext, Constants.KEY_UVC_VERSION_UPDATE, "1");
            mHandlerThread.quit();
            isUpdating = false; // 标记为更新完成
        }, THREAD_SLEEP_TIME);
    }
}
