#ifndef JNIDEMO_MISSJNI_H
#define JNIDEMO_MISSJNI_H

#define TAG     "missJNI"

#ifdef __cplusplus
extern "C" {
#endif

#include "miss/miss.h"

#ifdef __cplusplus
}
#endif

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissInit
(JNIEnv *env, jclass object, jobject callBack);

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissSessionQuery
(JNIEnv *env, jclass object, jint MissQueryCmd);

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissSetLogPath
(JNIEnv *env, jclass object);

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCmdSend
(JNIEnv *env, jclass object, jint session, jint cmd, jintArray params, jint length, jint code);

JNIEXPORT jint JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissServerInit
(JNIEnv *env, jclass object, jobject device_info, jint videoWidth, jint videoHeight);

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissRpcProcess
(JNIEnv *env, jclass object, jint rpc_id, jobject msg, jint length);

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissVideoSend
(JNIEnv *Env, jclass object, jint session, jbyteArray data, jint length, jboolean isKeyframe, jlong seq, jlong timestamp, jint codecId, jint videoChn);

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissAudioSend
(JNIEnv *Env, jclass object, jint session, jbyteArray data, jint length, jlong seq, jlong timestamp);

JNIEXPORT void JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCloseServerSession
(JNIEnv *Env, jclass object, jint session);

JNIEXPORT void JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissFinishServer
        (JNIEnv *Env, jclass object);

/*-----test-----*/
JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_test(JNIEnv *env, jclass object,jbyteArray b1,jbyteArray b2);

#endif //JNIDEMO_MISSJNI_H

