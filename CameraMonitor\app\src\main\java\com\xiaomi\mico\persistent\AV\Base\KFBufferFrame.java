package com.xiaomi.mico.persistent.AV.Base;

import static com.xiaomi.mico.persistent.AV.Base.KFFrame.KFFrameType.KFFrameBuffer;

import android.media.MediaCodec;
import android.os.Build;

import androidx.annotation.RequiresApi;

import java.nio.ByteBuffer;

//
//  KFBufferFrame
//  KFAVDemo
//  微信搜索『gzjkeyframe』关注公众号『关键帧Keyframe』获得最新音视频技术文章和进群交流。
//  Created by [公众号：关键帧Keyframe] on 2021/12/28.
//
public class KFBufferFrame extends KFFrame {
    public ByteBuffer buffer;
    public MediaCodec.BufferInfo bufferInfo;

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public KFBufferFrame() {
        super(KFFrameBuffer);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public KFBufferFrame(ByteBuffer inputBuffer, MediaCodec.BufferInfo inputBufferInfo) {
        super(KFFrameBuffer);
        buffer = inputBuffer;
        bufferInfo = inputBufferInfo;
    }

    public KFFrameType frameType() {
        return KFFrameBuffer;
    }
}
