package com.xiaomi.mico.persistent.cloud;

import com.xuhao.didi.core.iocore.interfaces.ISendable;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class SendKeyIv implements ISendable {

    private byte type = 0x01;

    private byte[] key;
    private byte[] iv;
    private boolean isVip;

    public SendKeyIv(byte[] key, byte[] iv, boolean isVip) {
        this.key = key;
        this.iv = iv;
        this.isVip = isVip;
    }

    @Override
    public byte[] parse() {
        int length = 4 + 1 + 1 + key.length + 1 + iv.length + 1;
        ByteBuffer bb = ByteBuffer.allocate(length);
        bb.order(ByteOrder.BIG_ENDIAN);
        bb.putInt(length - 4);
        bb.put(type);
        bb.put((byte) key.length);
        bb.put(key);
        bb.put((byte) iv.length);
        bb.put(iv);
        bb.put(isVip ? (byte) 1 : (byte) 0);
        return bb.array();
    }
}
