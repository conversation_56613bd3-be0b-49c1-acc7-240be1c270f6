package com.xiaomi.mico.persistent.voip;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CaptureRequest;
import android.os.Handler;
import android.os.HandlerThread;
import android.view.Surface;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import com.xiaomi.camera.monitoring.utils.L;

import java.util.ArrayList;
import java.util.List;

public class CameraVideoPreview {

    private final String TAG = "CameraVideoManager";

    private volatile static CameraVideoPreview mInstance;

    private CameraDevice mCameraDevice;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private CameraCaptureSession mCameraCaptureSession;
    private Surface mSurface;

    public static CameraVideoPreview getInstance() {
        if (mInstance == null) {
            synchronized (CameraVideoPreview.class) {
                if (mInstance == null) {
                    mInstance = new CameraVideoPreview();
                }
            }
        }
        return mInstance;
    }

    private CameraVideoPreview() {
    }

    public void openCamera(Context context, Surface surface) {
        mSurface = surface;
        L.monitor.d("%s openCamera", TAG);
        mHandlerThread = new HandlerThread("CameraPreviewBackground");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        CameraManager cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
        try {
            if (ActivityCompat.checkSelfPermission(context, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                L.monitor.e("%s the permission android.permission.CAMERA denied.", TAG);
                return;
            }
            cameraManager.openCamera(Integer.toString(0), new CameraDevice.StateCallback() {
                @Override
                public void onOpened(@NonNull CameraDevice camera) {
                    L.monitor.d("%s camera onOpened.", TAG);
                    mCameraDevice = camera;
                    mBackgroundHandler.post(recordRunnable);
                }

                @Override
                public void onDisconnected(@NonNull CameraDevice camera) {
                    L.monitor.d("%s camera disconnected.", TAG);
                    stopPreview();
                }

                @Override
                public void onError(@NonNull CameraDevice camera, int error) {
                    L.monitor.e("%s open camera onError, error is %d", TAG, error);
                    stopPreview();
                }
            }, mBackgroundHandler);
        } catch (Exception e) {
            L.monitor.e("%s open camera throw exception: %s", TAG, e.getMessage());
        }
    }

    Runnable recordRunnable = new Runnable() {
        @Override
        public void run() {
            if (mCameraDevice == null) {
                L.monitor.d("%s Please init first!!!!!!", TAG);
                return;
            }
            createCaptureSession(mCameraDevice);
        }
    };

    public void stopPreview() {
        try {
            if (mCameraCaptureSession != null) {
                mCameraCaptureSession.stopRepeating();
                mCameraCaptureSession.abortCaptures();
                mCameraCaptureSession.close();
            }
            if (mCameraDevice != null) {
                mCameraDevice.close();
            }
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mHandlerThread = null;
            mBackgroundHandler = null;
            mCameraCaptureSession = null;
            mCameraDevice = null;
        }
    }

    private void createCaptureSession(CameraDevice camera) {
        List<Surface> surfaces = new ArrayList<>();
        surfaces.add(mSurface);

        try {
            final CaptureRequest.Builder captureRequest = camera.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            captureRequest.addTarget(mSurface);
            camera.createCaptureSession(surfaces, new CameraCaptureSession.StateCallback() {
                @Override
                public void onConfigured(@NonNull CameraCaptureSession session) {
                    try {
                        mCameraCaptureSession = session;
                        mCameraCaptureSession.setRepeatingRequest(captureRequest.build(), null, null);
                    } catch (Exception e) {
                        L.monitor.e("%s set repeating request throw exception: %s", TAG, e.getMessage());
                    }
                }

                @Override
                public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                    L.monitor.w("%s create capture session onConfigureFailed.", TAG);
                }
            }, mBackgroundHandler);
        } catch (Exception e) {
            L.monitor.e("%s create capture session throw exception: %s", TAG, e.getMessage());
        }
    }
}
