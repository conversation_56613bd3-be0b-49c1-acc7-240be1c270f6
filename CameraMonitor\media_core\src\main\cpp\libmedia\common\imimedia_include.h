#ifndef __IMIMEDIA_INCLUDE_H__
#define __IMIMEDIA_INCLUDE_H__

#include "imimedia_error.h"

#define ERROR_MAX_INDEX 32
#define AUDIO_CODEC_INDEX 0xA0
#define CODEC_FORMAT_INDEX 0xF0

typedef enum {
    video_codec_h264 = 0x01,
    video_codec_h265,
    video_codec_mjpeg,
    video_codec_mpeg4,
} video_codec_id;

typedef enum {
    audio_codec_aac = AUDIO_CODEC_INDEX,
    audio_codec_g711a,
    audio_codec_g711u,
    audio_codec_opus,
} audio_codec_id;

typedef enum {
    frame_type_i = 0x01,
    frame_type_p,
    frame_type_b,
    frame_type_audio,
} frame_type_id;

typedef enum {
    frame_info_motion = 0x01,
    frame_info_people = 0x02,
    frame_info_face = 0x04,
    frame_info_babycry = 0x08,
} frame_info_id;

typedef enum {
    container_format_mp4 = 0,
    container_format_fragmented_mp4,
    container_format_ts,
    container_format_ps,
    container_format_imi_cloud,
} container_format_id;

typedef enum {
    timestamp_correct_enable = 0,
    timestamp_correct_disable,
} timestamp_correct_id;

typedef enum {
    rtsp_transport_tcp = 0,
    rtsp_transport_udp,
} rtsp_transport_id;

typedef enum {
    rtsp_method_play = 0,
    rtsp_method_record,
} rtsp_method_id;

typedef enum {
    pixel_format_unknown = -1,
    pixel_format_yv12 = 0,
    pixel_format_nv12,
    pixel_format_rgb24,
    pixel_format_bgr24,
    pixel_format_rgb32,
    pixel_format_bgr32,
} pixel_format_id;

typedef enum {
    sample_format_unknown = -1,
    sample_format_u8 = CODEC_FORMAT_INDEX,
    sample_format_s16,
    sample_format_s32,
    sample_format_float,
    sample_format_double,
    sample_format_u8_p,
    sample_format_s16_p,
    sample_format_s32_p,
    sample_format_float_p,
    sample_format_double_p,
} sample_format_id;

typedef struct _endecoder_frame_info {
    unsigned char *enc_data;
    unsigned int enc_data_len;
    frame_type_id frame_type;
    unsigned char *raw_data;
    unsigned int raw_data_len;
    pixel_format_id pixel;
    sample_format_id sample;
} endecoder_frame_info;

#endif // __IMIMEDIA_INCLUDE_H__
