package com.xiaomi.camera.monitoring.utils;

import android.content.Context;

import androidx.annotation.NonNull;

import com.elvishew.xlog.LogConfiguration;
import com.elvishew.xlog.LogLevel;
import com.elvishew.xlog.XLog;
import com.elvishew.xlog.internal.SystemCompat;
import com.elvishew.xlog.printer.AndroidPrinter;
import com.elvishew.xlog.printer.Printer;
import com.elvishew.xlog.printer.file.FilePrinter;
import com.elvishew.xlog.printer.file.backup.FileSizeBackupStrategy;
import com.elvishew.xlog.printer.file.backup.FileSizeBackupStrategy2;
import com.elvishew.xlog.printer.file.naming.ChangelessFileNameGenerator;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2020-03-09 13:59
 */
public class LogSetup {
    private static final int M_5 = 5 * 1024 * 1024;
    private static final String MM_DD_HH_MM_SS_SSS = "MM-dd HH:mm:ss SSS";
    public static final String TAG = "CameraMonitor";
    private static final String LOG_FILE = "CameraMonitor.log";
    private volatile boolean isSetup = false;

    public LogSetup() {
    }

    private void setupLog(Context context) {
        LogConfiguration logConfiguration = new LogConfiguration.Builder()
                .threadFormatter(Thread::getName)
                .enableThreadInfo()
                .enableBorder()
                .borderFormatter(strings -> {
                    if (strings == null || strings.length != 3) {
                        return null;
                    }

                    String thread = strings[0];
                    String stackTrace = strings[1];
                    String msg = strings[2];
                    return (thread != null ? "[" + thread + "] " : "")
                            + (stackTrace != null ? stackTrace + SystemCompat.lineSeparator : "")
                            + msg;
                })
                .logLevel(LogLevel.ALL)
                .tag(TAG)
                .build();

        Printer androidPrinter = new AndroidPrinter(true);
        Printer filePrinter = new FilePrinter.Builder(getLogDirectory(context))
                .fileNameGenerator(new ChangelessFileNameGenerator(LOG_FILE))
                .backupStrategy(new FileSizeBackupStrategy2(M_5, 1))
                .flattener((timeMillis, logLevel, tag, message) -> new SimpleDateFormat(MM_DD_HH_MM_SS_SSS, Locale.CHINA).format(timeMillis)
                        + '|' + LogLevel.getShortLevelName(logLevel) + '|' + tag + '|' + message)
                .build();

        XLog.init(logConfiguration, androidPrinter, filePrinter);
    }

    public void setup(Context context) {
        if (isSetup) {
            return;
        }

        setupLog(context);
        isSetup = true;
    }

    private static String getLogDirectory(@NonNull Context context) {
        return getExternalStoragePath(context) + File.separator + "log";
    }

    private static String getExternalStoragePath(@NonNull Context context) {
        File path = context.getExternalFilesDir(null);

        if (path == null) {
            path = context.getFilesDir();
        }

        return path.getAbsolutePath();
    }

}