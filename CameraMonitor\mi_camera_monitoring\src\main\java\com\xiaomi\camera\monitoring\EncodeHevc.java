package com.xiaomi.camera.monitoring;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.util.Log;

import com.xiaomi.camera.monitoring.entity.FrameData;
import com.xiaomi.camera.monitoring.entity.VideoFrameData;

import java.nio.ByteBuffer;

/**
 * hevc格式编码器
 */
public class EncodeHevc implements IEncoder {

    private static final String TAG = "EncodeHevc";

    private static final int FRAME_RATE = 15;//帧率15帧，和底层固定，发送的时间戳计算也要，更重要的是合成视频处也要
    private static final int BIT_RATE_1080P = 2000_000;
    private static final int BIT_RATE_720P = 800_000;

    private MediaCodec mediaCodec;
    private MediaCodec.BufferInfo bufferInfo;
    private VideoCameraStreamProducer videoProducer;

    private byte[] configByte;
    private int width;
    private int height;
    private long generateIndex = 1;
    private MediaFormat mMediaFormat;
    private String mEncodeType = MediaFormat.MIMETYPE_VIDEO_HEVC;

    public EncodeHevc(VideoCameraStreamProducer videoProducer) {
        this.videoProducer = videoProducer;
    }

    @Override
    public void init(Object... params) {
        mEncodeType = (String) params[0];
        this.width = (int) params[1];
        this.height = (int) params[2];
        Log.v(TAG, "init EncodeHevc :" + width + "  " + height);

        mMediaFormat = MediaFormat.createVideoFormat(mEncodeType, width, height);

        mMediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible);

        mMediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE_720P);

        mMediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);//I帧间隔1
        mMediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);//帧率
        try {
            mediaCodec = MediaCodec.createEncoderByType(mEncodeType);
            mediaCodec.configure(mMediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
            mediaCodec.start();
        } catch (Exception e) {
            Log.e(TAG, "init EncodeHevc throw exception:", e);
            mediaCodec = null;
        }
    }

    @Override
    public synchronized void encode(FrameData frameData) throws IllegalStateException {
        if (mediaCodec == null) {
            return;
        }
        byte[] input = frameData.dataBytes;

        switch (frameData.formatType) {
            case FrameData.NV21:
                if (input != null) {//buffer只读情况下就不可以添加时间水印了
                    NV21ToNV12(input, width, height);
                }
                break;
            case FrameData.YUV_420_888:
                break;
        }

        try {
            final int inputBufferIndex = mediaCodec.dequeueInputBuffer(-1);
            if (inputBufferIndex >= 0) {
                final ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputBufferIndex);
                inputBuffer.clear();
                if (input != null) {
                    inputBuffer.put(input);
                    inputBuffer.limit(frameData.getDataLen());
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, input.length, computePresentationTime(generateIndex), 0);
                } else {//当buff为只读的情况下
                    inputBuffer.put(frameData.dataBytes);
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, frameData.getDataLen(), computePresentationTime(generateIndex), 0);
                }
            }

            int outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 0);
            while (outputBufferIndex >= 0) {
                ByteBuffer outputBuffer = mediaCodec.getOutputBuffer(outputBufferIndex);
                byte[] outData = new byte[bufferInfo.size];
                outputBuffer.get(outData);
                if (bufferInfo.flags == MediaCodec.BUFFER_FLAG_CODEC_CONFIG) {
                    configByte = outData;
                } else if (bufferInfo.flags == MediaCodec.BUFFER_FLAG_KEY_FRAME) {//I帧
                    byte[] keyframe = new byte[bufferInfo.size + configByte.length];
                    System.arraycopy(configByte, 0, keyframe, 0, configByte.length);
                    System.arraycopy(outData, 0, keyframe, configByte.length, outData.length);

                    VideoFrameData keyFrameData = new VideoFrameData(frameData.getTimeStamp());
                    keyFrameData.index = generateIndex;
                    keyFrameData.dataBytes = keyframe;
                    keyFrameData.setDataLen(bufferInfo.size + configByte.length);
                    keyFrameData.setIFrame(true);
                    if (Constants.DEBUG) {
                        Log.v(TAG, "生产了编码了一帧I帧：" + generateIndex);
                    }
                    offerData(keyFrameData);
                } else {//P帧
                    VideoFrameData pFrameData = new VideoFrameData(frameData.getTimeStamp());
                    pFrameData.index = generateIndex;
                    pFrameData.dataBytes = outData;
                    pFrameData.setDataLen(outData.length);
                    pFrameData.setIFrame(false);
                    if (Constants.DEBUG) {
                        Log.v(TAG, "生产了编码了一帧P帧:" + generateIndex);
                    }
                    offerData(pFrameData);
                }

                mediaCodec.releaseOutputBuffer(outputBufferIndex, false);
                outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 0);
            }
        } catch (Exception e) {
            Log.e(TAG, "EncodeHevc throw exception, then restart:", e);
            try {
                mediaCodec = MediaCodec.createEncoderByType(mEncodeType);
                mediaCodec.configure(mMediaFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
                mediaCodec.start();
            } catch (Exception ex) {
                Log.e(TAG, "restart EncodeHevc throw exception:", ex);
                mediaCodec = null;
            }
        }
    }

    private void offerData(VideoFrameData keyFrameData) {
        if (videoProducer != null) {
            videoProducer.offerData(keyFrameData);
            generateIndex += 1;
        }
    }

    @Override
    public void startEncoder() {
        if (mediaCodec == null) {
            Log.e(TAG, "Please initialize first.");
            return;
        }
        generateIndex = 1;
        bufferInfo = new MediaCodec.BufferInfo();
    }

    @Override
    public void stopEncoder() {
        if (mediaCodec != null) {
            try {
                mediaCodec.stop();
                mediaCodec.release();
            } catch (Exception e) {
                Log.e(TAG, "stop Encode throw exception:", e);
            }
        }
    }

    @Override
    public synchronized void release() {
        stopEncoder();
        if (mediaCodec != null) {
            mediaCodec.release();
            mediaCodec = null;
        }
        videoProducer = null;
    }

    private long computePresentationTime(long frameIndex) {
        return frameIndex * 1000000 / (width * height * 5);
    }

    private byte[] NV21ToNV12(byte[] data, int width, int height) {
        int len = width * height;
        byte[] buffer = new byte[len * 3 / 2];
        byte[] y = new byte[len];
        byte[] uv = new byte[len / 2];
        System.arraycopy(data, 0, y, 0, len);
        for (int i = 0; i < len / 4; i++) {
            uv[i * 2] = data[len + i * 2 + 1];
            uv[i * 2 + 1] = data[len + i * 2];
        }
        System.arraycopy(y, 0, buffer, 0, y.length);
        System.arraycopy(uv, 0, buffer, y.length, uv.length);
        return buffer;
    }
}
