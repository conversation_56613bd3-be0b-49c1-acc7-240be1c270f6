package com.xiaomi.mico.persistent.voip;

import android.content.Context;
import android.content.Intent;
import android.media.AudioManager;
import android.os.Message;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.monitor.R;

public class MiVoipUtil {
    private static final String TAG = "MiVoipUtil";

    public static boolean isInCalling = false; // 当前是否接通电话
    public static boolean hangupByself = false; // 设备端主动挂断
    public static final String MI_VOIP_START_PARAM = "mi_voip_start_param";

    private static final String VOIP_ACTION = "com.xiaomi.voip.permissions.VOIP_EVENT";
    private static final String VOIP_PERMISSION = "com.xiaomi.voip.permissions.VOIP";
    public static final int MSG_VOIP_CLIENT_VOIP_START = 0x10002;
    public static final int MSG_VOIP_CLIENT_VOIP_STOP = 0x10004;
    public static final int MSG_TO_LAUNCHER_CLIENT_CHECK_FACE_DETECT = 0x10003;

    public static final String VOIP_FINISH_ACTION = "com.xiaomi.mico.persistent.monitor.VOIP_FINISH";
    public static final String VOIP_COMMAND_ACTION = "com.xiaomi.mico.persistent.monitor.COMMAND_ACTION";
    public static final String VOIP_COMMAND_ARGS = "COMMAND";

    //https://xiaomi.f.mioffice.cn/docx/doxk42EhUWFXi9GnKrEldpHUO4e
    //音箱上有些自研算法会利用Camera做些检测，例如： "儿童距离保户"，"手势检测"，“快速进入儿童模式"等；
    //关键在于 MicoVoIP 会在 start/end 时 主动  close/open 这些算法以及对应页面；
    public static void sendVoipBroadcast(Context context, int msgWhat, Intent intentParams) {
        Intent intent = new Intent(VOIP_ACTION);
        Message msg = Message.obtain();
        msg.what = msgWhat;
        if (intentParams != null) {
            msg.obj = intentParams;
        }
        intent.putExtra("record_data", msg);
        context.sendBroadcast(intent, VOIP_PERMISSION);
    }

    public static void startVoipActivity(Context context, String params) {
        Intent intent = new Intent(context, MiVoipActivity.class);
        intent.putExtra(MI_VOIP_START_PARAM, params);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//        context.startActivity(intent);
        //启动通话actiivty前，将这个actiivty intent 通过这个广播发给launcher，launcher去和算法交互，如果校验成功则会启动你的intent
        sendVoipBroadcast(context, MSG_TO_LAUNCHER_CLIENT_CHECK_FACE_DETECT, intent);
    }

    public static void finishVoipActivity(Context context) {
        if (!MiVoipUtil.hangupByself) {
            Toast.makeText(context, context.getString(R.string.hang_up_by_remote), Toast.LENGTH_LONG).show();
        }
        Intent intent = new Intent(VOIP_FINISH_ACTION);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    public static void sendMessageToVoip(Context context, int cmd) {
        Intent intent = new Intent(VOIP_COMMAND_ACTION);
        intent.putExtra(VOIP_COMMAND_ARGS, cmd);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
    }

    public static String getCallTime(long callTime) {
        int totalSeconds = (int) (callTime / 1000);
        int hours = totalSeconds / 3600;
        int minutes = (totalSeconds % 3600) / 60;
        int seconds = totalSeconds % 60;

        String formattedTime;
        if (hours > 0) {
            formattedTime = String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            formattedTime = String.format("%02d:%02d", minutes, seconds);
        }
        return formattedTime;
    }

    /**
     * @param audioManager
     * @param isMute  true: 麦克风静音
     */
    public static void setMicrophoneMute(AudioManager audioManager, boolean isMute) {
        if (audioManager != null) {
            L.monitor.d("%s setMicrophoneMute: %s", TAG, isMute);
            try {
                audioManager.setMicrophoneMute(isMute);
            } catch (Throwable e) {
                L.monitor.e("%s setMicrophoneMute: %s", TAG, e.getMessage());
            }
        }
    }

    /**
     * 判断麦克风是否静音
     */
    public static boolean isMicrophoneMute(AudioManager audioManager) {
        if (audioManager != null) {
            try {
                return audioManager.isMicrophoneMute();
            } catch (Throwable e) {
                L.monitor.e("%s setMicrophoneMute: %s", TAG, e.getMessage());
            }
        }
        return false;
    }


}
