package com.xiaomi.mico.persistent.voip;
//
//  MainActivity
//  KFAVDemo
//  微信搜索『gzjkeyframe』关注公众号『关键帧Keyframe』获得最新音视频技术文章和进群交流。
//  Created by [公众号：关键帧Keyframe] on 2021/12/28.
//
import androidx.annotation.RequiresApi;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import android.Manifest;
import android.app.Activity;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.media.MediaCodec;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.util.Size;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.FrameLayout;

import com.xiaomi.mico.persistent.AV.Base.KFFrame;
import com.xiaomi.mico.persistent.AV.Base.KFTextureFrame;
import com.xiaomi.mico.persistent.AV.Capture.KFIVideoCapture;
import com.xiaomi.mico.persistent.AV.Capture.KFVideoCaptureConfig;
import com.xiaomi.mico.persistent.AV.Capture.KFVideoCaptureListener;
import com.xiaomi.mico.persistent.AV.Capture.KFVideoCaptureV1;
import com.xiaomi.mico.persistent.AV.Capture.KFVideoCaptureV2;
import com.xiaomi.mico.persistent.AV.Effect.KFGLContext;
import com.xiaomi.mico.persistent.AV.Render.KFRenderView;

import com.xiaomi.mico.persistent.monitor.R;

import static android.hardware.camera2.CameraMetadata.LENS_FACING_FRONT;

public class TestActivity extends AppCompatActivity {

    private KFIVideoCapture mCapture;///< 相机采集
    private KFVideoCaptureConfig mCaptureConfig;///< 相机采集配置
    private KFRenderView mRenderView;///< 渲染视图
    private KFGLContext mGLContext;///< OpenGL 上下文


    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_test);


        ///< 检测采集相关权限
        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED || ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(this, Manifest.permission.WRITE_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions((Activity) this,
                    new String[] {Manifest.permission.CAMERA,Manifest.permission.RECORD_AUDIO, Manifest.permission.READ_EXTERNAL_STORAGE, Manifest.permission.WRITE_EXTERNAL_STORAGE},
                    1);
        }

        ///< OpenGL上下文
        mGLContext = new KFGLContext(null);
        ///< 渲染视图
        mRenderView = new KFRenderView(this,mGLContext.getContext());
        WindowManager windowManager = (WindowManager)this.getSystemService(this.WINDOW_SERVICE);
        Rect outRect = new Rect();
        windowManager.getDefaultDisplay().getRectSize(outRect);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(outRect.width(), outRect.height());
        addContentView(mRenderView,params);

        ///< 采集配置 摄像头方向、分辨率、帧率
        mCaptureConfig = new KFVideoCaptureConfig();
        mCaptureConfig.cameraFacing = LENS_FACING_FRONT;
        mCaptureConfig.resolution = new Size(1280,720);
        mCaptureConfig.fps = 30;
        boolean useCamera2 = false;
        if(useCamera2){
            mCapture = new KFVideoCaptureV2();
        }else{
            mCapture = new KFVideoCaptureV1();
        }
        mCapture.setup(this,mCaptureConfig,mVideoCaptureListener,mGLContext.getContext());
        mCapture.startRunning();
    }

    private KFVideoCaptureListener mVideoCaptureListener = new KFVideoCaptureListener() {
        @Override
        ///< 相机打开回调
        public void cameraOnOpened(){}

        @Override
        ///< 相机关闭回调
        public void cameraOnClosed() {
        }

        @Override
        ///< 相机出错回调
        public void cameraOnError(int error,String errorMsg) {

        }

        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        @Override
        ///< 相机数据回调
        public void onFrameAvailable(KFFrame frame) {
            mRenderView.render((KFTextureFrame) frame);
        }
    };
}