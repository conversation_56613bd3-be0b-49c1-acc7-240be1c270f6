package com.xiaomi.mico.persistent.yuv;

import android.opengl.GLES20;
import android.opengl.GLSurfaceView;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.FloatBuffer;
import javax.microedition.khronos.egl.EGLConfig;
import javax.microedition.khronos.opengles.GL10;

public class CameraRenderer implements GLSurfaceView.Renderer {
    private int program;
    private int yTexture, uTexture, vTexture;
    private FloatBuffer vertexBuffer, textureBuffer;
    private ByteBuffer yBuffer, uBuffer, vBuffer;
    private int width, height;

    private final float[] vertexCoords = {
            -1.0f, 1.0f,  // Top-left
            -1.0f, -1.0f, // Bottom-left
            1.0f, 1.0f,   // Top-right
            1.0f, -1.0f   // Bottom-right
    };

    private final float[] textureCoords = {
            0.0f, 0.0f,  // Top-left
            0.0f, 1.0f,   // Bottom-left
            1.0f, 0.0f,   // Top-right
            1.0f, 1.0f    // Bottom-right
    };

    @Override
    public void onSurfaceCreated(GL10 gl, EGLConfig config) {
        program = ShaderUtils.createProgram(vertexShaderCode, fragmentShaderCode);
        yTexture = ShaderUtils.createTexture();
        uTexture = ShaderUtils.createTexture();
        vTexture = ShaderUtils.createTexture();

        vertexBuffer = ByteBuffer.allocateDirect(vertexCoords.length * 4)
                .order(ByteOrder.nativeOrder())
                .asFloatBuffer()
                .put(vertexCoords);
        vertexBuffer.position(0);

        textureBuffer = ByteBuffer.allocateDirect(textureCoords.length * 4)
                .order(ByteOrder.nativeOrder())
                .asFloatBuffer()
                .put(textureCoords);
        textureBuffer.position(0);
    }

    @Override
    public void onSurfaceChanged(GL10 gl, int width, int height) {
        GLES20.glViewport(0, 0, width, height);
    }

    @Override
    public void onDrawFrame(GL10 gl) {
        GLES20.glClear(GLES20.GL_COLOR_BUFFER_BIT | GLES20.GL_DEPTH_BUFFER_BIT);
        GLES20.glUseProgram(program);

        // Bind YUV textures
        ShaderUtils.bindTexture(yTexture, 0, GLES20.GL_TEXTURE0, "yTexture", program);
        ShaderUtils.bindTexture(uTexture, 1, GLES20.GL_TEXTURE1, "uTexture", program);
        ShaderUtils.bindTexture(vTexture, 2, GLES20.GL_TEXTURE2, "vTexture", program);

        // Upload YUV data
        if (yBuffer != null && uBuffer != null && vBuffer != null) {
            ShaderUtils.uploadYUVData(yTexture, yBuffer, width, height, GLES20.GL_LUMINANCE);
            ShaderUtils.uploadYUVData(uTexture, uBuffer, width / 2, height / 2, GLES20.GL_LUMINANCE);
            ShaderUtils.uploadYUVData(vTexture, vBuffer, width / 2, height / 2, GLES20.GL_LUMINANCE);
        }

        // Draw
        int positionHandle = GLES20.glGetAttribLocation(program, "aPosition");
        int textureHandle = GLES20.glGetAttribLocation(program, "aTexCoord");
        GLES20.glEnableVertexAttribArray(positionHandle);
        GLES20.glEnableVertexAttribArray(textureHandle);
        GLES20.glVertexAttribPointer(positionHandle, 2, GLES20.GL_FLOAT, false, 0, vertexBuffer);
        GLES20.glVertexAttribPointer(textureHandle, 2, GLES20.GL_FLOAT, false, 0, textureBuffer);
        GLES20.glDrawArrays(GLES20.GL_TRIANGLE_STRIP, 0, 4);
    }

    public void updateYUVData(ByteBuffer y, ByteBuffer u, ByteBuffer v, int width, int height) {
        this.yBuffer = y;
        this.uBuffer = u;
        this.vBuffer = v;
        this.width = width;
        this.height = height;
    }

    public void updateNV12Data(ByteBuffer y, ByteBuffer uv, int width, int height) {
        this.width = width;
        this.height = height;

        // 计算 UV 分量大小
        int uvSize = uv.remaining(); // UV 分量总大小
        int uSize = uvSize / 2;      // U 分量大小
        int vSize = uvSize / 2;      // V 分量大小

        // 分配足够的容量
        ByteBuffer u = ByteBuffer.allocateDirect(uSize);
        ByteBuffer v = ByteBuffer.allocateDirect(vSize);

        // 分离 UV 分量
        for (int i = 0; i < uvSize; i += 2) {
            u.put(uv.get(i));     // U 分量
            v.put(uv.get(i + 1)); // V 分量
        }
        u.position(0);
        v.position(0);

        this.yBuffer = y;
        this.uBuffer = u;
        this.vBuffer = v;
    }

    private final String vertexShaderCode =
            "attribute vec4 aPosition;\n" +
                    "attribute vec2 aTexCoord;\n" +
                    "varying vec2 vTexCoord;\n" +
                    "void main() {\n" +
                    "    gl_Position = aPosition;\n" +
                    "    vTexCoord = aTexCoord;\n" +
                    "}";

    private final String fragmentShaderCode =
            "precision mediump float;\n" +
                    "varying vec2 vTexCoord;\n" +
                    "uniform sampler2D yTexture;\n" +
                    "uniform sampler2D uTexture;\n" +
                    "uniform sampler2D vTexture;\n" +
                    "void main() {\n" +
                    "    float y = texture2D(yTexture, vTexCoord).r;\n" +
                    "    float u = texture2D(uTexture, vTexCoord).r - 0.5;\n" +
                    "    float v = texture2D(vTexture, vTexCoord).r - 0.5;\n" +
                    "    float r = y + 1.402 * v;\n" +
                    "    float g = y - 0.344 * u - 0.714 * v;\n" +
                    "    float b = y + 1.772 * u;\n" +
                    "    gl_FragColor = vec4(r, g, b, 1.0);\n" +
                    "}";
}