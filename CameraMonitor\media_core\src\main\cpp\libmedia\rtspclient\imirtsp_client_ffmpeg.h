#pragma once

#include "imimedia_include.h"

typedef struct imirtsp_client_ffmpeg_info_s *imirtsp_client_ffmpeg_info_t;

void imirtsp_client_init_ffmpeg();

void imirtsp_client_set_certificate_file(const char* ca_file,
	const char* cert_file,
	const char* key_file);

void imirtsp_client_set_latency(int latency);

int imirtsp_client_play(const char* url,
	const char* username,
	const char* password,
	rtsp_transport_id transport,
	video_codec_id *video,
	audio_codec_id *audio,
	unsigned int *vfps,
	unsigned int *vwidth,
	unsigned int *vheight,
	unsigned int *achannel,
	unsigned int *asamplerate,
	unsigned long long *duration,
	/*out*/imirtsp_client_ffmpeg_info_t* handle);

int imirtsp_read_frame(imirtsp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int len,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype);

int imirtsp_client_record(const char* url,
	const char* username,
	const char* password,
	rtsp_transport_id transport,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	unsigned long long duration,
	/*out*/imirtsp_client_ffmpeg_info_t* handle);

int imirtsp_client_write_video_frame(imirtsp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype);

int imirtsp_client_write_audio_frame(imirtsp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp);

int imirtsp_client_pause(imirtsp_client_ffmpeg_info_t handle);

int imirtsp_client_resume(imirtsp_client_ffmpeg_info_t handle);

int imirtsp_client_seek(imirtsp_client_ffmpeg_info_t handle,
	unsigned long long timestamp);

int imirtsp_client_teardown(imirtsp_client_ffmpeg_info_t handle);