#pragma once

#include "imimedia_include.h"

int imiconvert_video(unsigned int in_width,
	unsigned int in_height,
	pixel_format_id src_pix,
	unsigned char *src_data,
	unsigned int out_width,
	unsigned int out_height,
	pixel_format_id dst_pix,
	/*out*/unsigned char *dst_data);

int imiconvert_audio(unsigned int channels,
	unsigned int sample_rate,
	unsigned int nb_samples,
	sample_format_id src_smp,
	unsigned char *src_data,
	sample_format_id dst_smp,
	/*out*/unsigned char *dst_data,
	/*out*/unsigned int *dst_len);
