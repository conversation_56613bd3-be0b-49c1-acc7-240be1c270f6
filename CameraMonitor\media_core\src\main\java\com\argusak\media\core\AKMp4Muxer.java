package com.argusak.media.core;

import com.argusak.media.core.constant.AKMediaCoreConstant;
import com.argusak.media.core.constant.AKMediaCoreConstant.AudioCodecId;
import com.argusak.media.core.constant.AKMediaCoreConstant.ContainerFormatId;
import java.nio.ByteBuffer;
import java.nio.file.InvalidPathException;

/**
 * @Author: zhy
 * @Date: 2024/5/27
 * @Desc: 底层使用ffmpeg 实现mp4的相关合成 解析相关功能
 */
public final class AKMp4Muxer {

    private static final String TAG = "CameraMonitor.AKJniMp4Muxer";

    static {
        System.loadLibrary("ffmpeg-org");
        System.loadLibrary("media_core");
    }

    private static int reaLen = 0;
    private static long mHandleRead = 0;
    private static long mHandleWrite = 0;

    /**
     * 录制总时长,0为一直录，单位ms
     */
    private static final int DURATION = 0;

    public static long createFileJava(String path, int codecType, long curTime) {
        if (path == null || path.isEmpty()) {
            throw new InvalidPathException(path, "createFileJava  path can't be null");
        }
        initMp4();
        mHandleWrite = createFile(
                path,
                ContainerFormatId.CONTAINER_FORMAT_MP4,
                codecType,
                AudioCodecId.AUDIO_CODEC_OPUS,
                AKMediaCoreConstant.V_FPS,
                AKMediaCoreConstant.V_WIDTH,
                AKMediaCoreConstant.V_HEIGHT,
                AKMediaCoreConstant.A_CHANNEL,
                AKMediaCoreConstant.A_SAMPLE_RATE,
                DURATION,
                curTime
        );
        return mHandleWrite;
    }

    public static long openFileJava(String path, int codecType) {
        if (path == null || path.isEmpty()) {
            throw new InvalidPathException(path, "openFileJava  path can't be null");
        }
        mHandleRead = openFile(
                path,
                ContainerFormatId.CONTAINER_FORMAT_MP4,
                codecType,
                AudioCodecId.AUDIO_CODEC_AAC,
                AKMediaCoreConstant.V_FPS,
                AKMediaCoreConstant.V_WIDTH,
                AKMediaCoreConstant.V_HEIGHT,
                AKMediaCoreConstant.A_CHANNEL,
                AKMediaCoreConstant.A_SAMPLE_RATE,
                AKMediaCoreConstant.A_BITRATE,
                DURATION
        );
        return mHandleRead;
    }

    public static int writeVideoFrameFileJava(byte[] videoBuffer, long timestamp, long timestampUtc) {
        if (videoBuffer == null) {
            throw new NullPointerException("writeVideoFrameFileJava videoBuffer is null or empty");
        }
        return writeVideoFrameFile(
                mHandleWrite,
                videoBuffer,
                videoBuffer.length,
                timestamp,
                reaLen,
                timestampUtc
        );
    }

    /**
     * 写入音频数据 aac
     * @param audioByte ByteArray?
     * @return Int
     */
    public static int writeAudioFrameFileJava(byte[] audioByte, long timestamp) {
        if (audioByte == null) {
            throw new RuntimeException("writeAudioFrameFileJava audioByte is null or empty");
        }
        return writeAudioFrameFile(
                mHandleWrite,
                audioByte,
                audioByte.length,
                timestamp
        );
    }

    /**
     *  保存mp4文件 写入文件结束时必须调用
     * @return Int
     */
    public static int closeFileJava() {
        return closeFile(mHandleWrite);
    }

    private static native void initMp4();

    private static native void mp4Test();

    /**
     * 写入视频帧
     *
     * @param handle
     * @param videoFrame
     * @param dataLen
     * @param vTimesTamp
     * @param reaLen
     * @return
     */
    private static native int writeVideoFrameFile(long handle, byte[] videoFrame, int dataLen, long vTimesTamp, int reaLen, long timestampUtc);

    /**
     * 写入音频
     *
     * @param handle
     * @param data
     * @param dataLen
     * @param aTimesTamp 0
     * @return
     */
    private static native int writeAudioFrameFile(long handle, byte[] data, int dataLen, long aTimesTamp);

    /**
     * 保存
     *
     * @return 是否成功
     */
    private static native int closeFile(long handle);

    /***-------------------------------    //读取文件 --------------------- */
    /**
     * @param path
     * @param videoCodecID
     * @param audioCodecID
     * @param vFps
     * @param vWidth
     * @param vHeight
     * @param aChannel
     * @param aSampleRate
     * @param aBitrate
     * @param duration
     * @return
     */
    private static native long openFile(String path, int containerFormatId, int videoCodecID, int audioCodecID, int vFps, int vWidth, int vHeight, int aChannel, int aSampleRate, int aBitrate, int duration);

    /**
     *
     * @param handle
     * @param data
     * @param dataLen
     * @param vTimesTamp
     * @param frameType
     * @return
     */
    private static native int getVideoFrame(long handle, byte[] data, int dataLen, long vTimesTamp, int frameType);

    /**
     * 创建文件
     *
     * @param path         文件路径
     * @param videoCodecID 编码方式  - VIDEO_CODEC_ID_H264
     * @param audioCodecID 音频编码方式  -AUDIO_CODEC_ID_AAC
     * @param vFps         每秒传输帧数
     * @param vWidth       视频宽
     * @param vHeight      视频高
     * @param aChannel     音频通道数 tutk
     * @param aSampleRate  音频采样率
     * @return ret success =0
     */
    private static native long createFile(String path, int containerFormatId, int videoCodecID, int audioCodecID, int vFps, int vWidth, int vHeight, int aChannel, int aSampleRate, int duration, long curTime);

    /**
     * 执行ffmpeg cmd
     *
     * @param commands
     * @return
     */
    private static native int runFfmpegCmd(String[] commands);
}
