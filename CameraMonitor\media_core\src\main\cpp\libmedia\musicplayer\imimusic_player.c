#include <stdlib.h>
#include <string.h>
#include "imimusic_player.h"
#include "imiparser_mp3.h"

#ifndef inline
#define inline __inline
#endif

#ifdef __cplusplus
extern "C"{
#endif

#include <libavformat/avformat.h>
#include <libavformat/avio.h>
#include <libavutil/mem.h>
#include <libavutil/opt.h>
#include <libavfilter/avfilter.h>
#include <libavfilter/buffersrc.h>
#include <libavfilter/buffersink.h>

#ifdef __cplusplus
}
#endif

#ifdef WIN32
#define log_error(x, ...)
#define log_info(x, ...)
#else

#include "elog.h"

#endif

#ifdef WIN32
//ffmpeg
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#pragma comment(lib, "avfilter.lib")
#endif

#define MODULE_IMIMUSIC_PLAYER "imimusicplayer"

AVFormatContext *_format_context = NULL;
AVCodecContext *_avcodec = NULL;
AVStream *_avstream = NULL;
AVFrame *_avframe = NULL;
int _is_interrupt = 0;
time_t _interrupt_time = 0;
AVCodecContext *_mp3_avcodec = NULL;
AVFrame *_mp3_avframe = NULL;
unsigned char *_mp3_frame = NULL;
unsigned int _mp3_len = 0;
ReSampleContext *_pcm_resample = NULL;
AVFilterGraph *_pcm_filter_graph = NULL;
AVFilterContext *_pcm_filter_ctx_buffer = NULL;
AVFilterContext *_pcm_filter_ctx_volume = NULL;
AVFilterContext *_pcm_filter_ctx_buffersink = NULL;
float _current_volume = 1.0f;

int _init_avfilter(int channel_layout, int samplerate, int sample_fmt, int timebase_num,
                   int timebase_den, float volume) {
    int ret = 0;
    char args_in[128] = {0};
    char volume_in[16] = {0};
    AVFilter *pcm_filter_buffer = NULL;
    AVFilter *pcm_filter_volume = NULL;
    AVFilter *pcm_filter_buffersink = NULL;
    if (_pcm_filter_graph) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter pcm_filter_graph is exist");
        return -1;
    }
    avfilter_register_all();
    _pcm_filter_graph = avfilter_graph_alloc();
    if (_pcm_filter_graph == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_graph_alloc error");
        return -1;
    }
    pcm_filter_buffer = avfilter_get_by_name("abuffer");
    if (pcm_filter_buffer == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_get_by_name abuffer error");
        goto error;
    }
    sprintf_s(args_in, sizeof(args_in),
              "sample_fmt=%d:channel_layout=%d:sample_rate=%d:time_base=%d/%d", sample_fmt,
              channel_layout, samplerate, timebase_num, timebase_den);
    ret = avfilter_graph_create_filter(&_pcm_filter_ctx_buffer, pcm_filter_buffer, "IN", args_in,
                                       NULL, _pcm_filter_graph);
    if (ret != 0) {
        log_error(MODULE_IMIMUSIC_PLAYER,
                  "init_avfilter avfilter_graph_create_filter IN error = %d", ret);
        goto error;
    }
    pcm_filter_volume = avfilter_get_by_name("volume");
    if (pcm_filter_volume == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_get_by_name volume error");
        goto error;
    }
    sprintf_s(volume_in, sizeof(volume_in), "volume=%f", volume);
    ret = avfilter_graph_create_filter(&_pcm_filter_ctx_volume, pcm_filter_volume, "VOL", volume_in,
                                       NULL, _pcm_filter_graph);
    if (ret != 0) {
        log_error(MODULE_IMIMUSIC_PLAYER,
                  "init_avfilter avfilter_graph_create_filter VOL error = %d", ret);
        goto error;
    }
    pcm_filter_buffersink = avfilter_get_by_name("abuffersink");
    if (pcm_filter_buffersink == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_get_by_name abuffersink error");
        goto error;
    }
    ret = avfilter_graph_create_filter(&_pcm_filter_ctx_buffersink, pcm_filter_buffersink, "OUT",
                                       "", NULL, _pcm_filter_graph);
    if (ret != 0) {
        log_error(MODULE_IMIMUSIC_PLAYER,
                  "init_avfilter avfilter_graph_create_filter OUT error = %d", ret);
        goto error;
    }
    ret = av_opt_set_bin(_pcm_filter_ctx_buffersink, "sample_fmts", (uint8_t * ) & sample_fmt,
                         sizeof(sample_fmt), AV_OPT_SEARCH_CHILDREN);
    if (ret < 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter av_opt_set_bin sample_fmts error = %d",
                  ret);
        goto error;
    }
    ret = avfilter_link(_pcm_filter_ctx_buffer, 0, _pcm_filter_ctx_volume, 0);
    if (ret != 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_link buffer error = %d", ret);
        goto error;
    }
    ret = avfilter_link(_pcm_filter_ctx_volume, 0, _pcm_filter_ctx_buffersink, 0);
    if (ret != 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_link buffersink error = %d", ret);
        goto error;
    }
    ret = avfilter_graph_config(_pcm_filter_graph, NULL);
    if (ret < 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "init_avfilter avfilter_graph_config error = %d", ret);
        goto error;
    }
    log_info(MODULE_IMIMUSIC_PLAYER, "init_avfilter success");
    return 0;

    error:
    if (_pcm_filter_ctx_buffer) {
        avfilter_free(_pcm_filter_ctx_buffer);
        _pcm_filter_ctx_buffer = NULL;
    }
    if (_pcm_filter_ctx_volume) {
        avfilter_free(_pcm_filter_ctx_volume);
        _pcm_filter_ctx_volume = NULL;
    }
    if (_pcm_filter_ctx_buffersink) {
        avfilter_free(_pcm_filter_ctx_buffersink);
        _pcm_filter_ctx_buffersink = NULL;
    }
    if (_pcm_filter_graph) {
        avfilter_graph_free(&_pcm_filter_graph);
        _pcm_filter_graph = NULL;
    }
    avfilter_uninit();
    return -1;
}

int _fini_avfilter() {
    if (_pcm_filter_ctx_buffer) {
        avfilter_free(_pcm_filter_ctx_buffer);
        _pcm_filter_ctx_buffer = NULL;
    }
    if (_pcm_filter_ctx_volume) {
        avfilter_free(_pcm_filter_ctx_volume);
        _pcm_filter_ctx_volume = NULL;
    }
    if (_pcm_filter_ctx_buffersink) {
        avfilter_free(_pcm_filter_ctx_buffersink);
        _pcm_filter_ctx_buffersink = NULL;
    }
    if (_pcm_filter_graph) {
        avfilter_graph_free(&_pcm_filter_graph);
        _pcm_filter_graph = NULL;
    }
    avfilter_uninit();
    log_info(MODULE_IMIMUSIC_PLAYER, "fini_avfilter success");
    return 0;
}

void imimusic_init_ffmpeg() {
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_init_ffmpeg");
    av_register_all();
}

static int decode_interrupt_cb(void *ctx) {
    if (_interrupt_time == 0) {
        return _is_interrupt;
    } else {
        time_t now = time(NULL);
        if (now - _interrupt_time >= 1) {
            return 1;
        } else {
            return 0;
        }
    }
}

int imimusic_open_file(const char *path, unsigned int *achannel, unsigned int *asamplerate,
                       unsigned int *abitrate, unsigned int *duration) {
    int ret = 0;
    unsigned int i = 0;
    AVDictionary *options = NULL;
    if (path == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file path is null");
        return -1;
    }
    _format_context = avformat_alloc_context();
    if (_format_context == NULL) {
        log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file avformat_alloc_context error");
        return -1;
    }
    _is_interrupt = 0;
    _interrupt_time = 0;
    _format_context->interrupt_callback.callback = decode_interrupt_cb;
    _format_context->interrupt_callback.opaque = NULL;
    av_dict_set(&options, "recv_buffer_size", "204800", 0);//200k
    av_dict_set(&options, "timeout", "1000000", 0);//1s
    ret = avformat_open_input(&_format_context, path, NULL, &options);
    if (ret < 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file avformat_open_input error = %d", ret);
        goto error;
    }
    av_dict_free(&options);
    ret = avformat_find_stream_info(_format_context, NULL);
    if (ret < 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file avformat_find_stream_info error = %d",
                  ret);
        goto error;
    }
    *duration = (unsigned int) _format_context->duration;
    for (i = 0; i < _format_context->nb_streams; i++) {
        AVStream *stream = _format_context->streams[i];
        switch (stream->codec->codec_type) {
            case AVMEDIA_TYPE_AUDIO: {
                AVCodec *avcodec = avcodec_find_decoder(stream->codec->codec_id);
                if (avcodec == 0) {
                    log_error(MODULE_IMIMUSIC_PLAYER,
                              "imimusic_open_file avcodec_find_decoder error codec_id = %d",
                              stream->codec->codec_id);
                    goto error;
                }
                _avcodec = avcodec_alloc_context3(avcodec);
                _avcodec->codec_type = AVMEDIA_TYPE_AUDIO;
                ret = avcodec_open2(_avcodec, avcodec, NULL);
                if (ret >= 0) {
                    _avframe = av_frame_alloc();
                    if (avcodec->capabilities & CODEC_CAP_TRUNCATED) {
                        _avframe->flags |= CODEC_FLAG_TRUNCATED;
                    }
                } else {
                    log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file avcodec_open2 error = %d",
                              ret);
                    goto error;
                }
                *achannel = stream->codec->channels;
                *asamplerate = stream->codec->sample_rate;
                *abitrate = (unsigned int) stream->codec->bit_rate;
                _avstream = stream;
            }
                break;
            default:
                continue;
        }
    }
    if (_avframe == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file av_frame_alloc error");
        goto error;
    }
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_open_file success");
    return 0;

    error:
    if (_avcodec) {
        avcodec_close(_avcodec);
        av_free(_avcodec);
        _avcodec = NULL;
    }
    if (_avframe) {
        av_free(_avframe);
        _avframe = NULL;
    }
    if (_format_context) {
        avformat_close_input(&_format_context);
        _format_context = NULL;
    }
    return -1;
}

int imimusic_get_one_frame(unsigned char *data, unsigned int *data_len, unsigned int *timestamp,
                           unsigned int *nb_samples, float volume) {
    int ret = 0;
    int got_frame_ptr = 0;
    int len = 0;
    AVPacket pkt = {0};
    if (_format_context == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_get_one_frame format_context is null");
        return -1;
    }
    if (*timestamp != 0) {
        int64_t time = (int64_t)(((double) (*timestamp) / (double) 1000) * AV_TIME_BASE +
                                 (double) _format_context->start_time);
        int r = av_seek_frame(_format_context, -1, time, AVSEEK_FLAG_FRAME);//AVSEEK_FLAG_FRAME
        if (r < 0) {
            log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_get_one_frame av_seek_frame fail ret = %d",
                     r);
            return -2;
        }
    }
    av_init_packet(&pkt);
    _interrupt_time = time(NULL);
    ret = av_read_frame(_format_context, &pkt);
    _interrupt_time = 0;
    if (ret < 0) {
        log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_get_one_frame av_read_frame fail ret = %d", ret);
        av_free_packet(&pkt);
        return -2;
    }
    len = avcodec_decode_audio4(_avcodec, _avframe, &got_frame_ptr, &pkt);
    if (got_frame_ptr != 0) {
        if (_current_volume != volume) {
            _current_volume = volume;
            _fini_avfilter();
            _init_avfilter((int) _avcodec->channel_layout, _avcodec->sample_rate,
                           _avcodec->sample_fmt, _avstream->time_base.num, _avstream->time_base.den,
                           _current_volume);
        }
        if (_current_volume != 1.0f) {
            if (_pcm_filter_graph) {
                int raw_linesize = _avframe->linesize[0];
                ret = av_buffersrc_add_frame_flags(_pcm_filter_ctx_buffer, _avframe,
                                                   AV_BUFFERSRC_FLAG_KEEP_REF);
                if (ret != 0) {
                    log_error(MODULE_IMIMUSIC_PLAYER,
                              "imimusic_get_one_frame av_buffersrc_add_frame_flags error = %d",
                              ret);
                    av_free_packet(&pkt);
                    return -1;
                }
                ret = av_buffersink_get_frame(_pcm_filter_ctx_buffersink, _avframe);
                if (ret < 0) {
                    log_error(MODULE_IMIMUSIC_PLAYER,
                              "imimusic_get_one_frame av_buffersink_get_frame error = %d", ret);
                    av_free_packet(&pkt);
                    return -1;
                }
                *nb_samples = _avframe->nb_samples;
                *data_len = raw_linesize;
                memcpy(data, _avframe->data[0], raw_linesize);
                av_frame_unref(_avframe);
            } else {
                *nb_samples = _avframe->nb_samples;
                *data_len = _avframe->linesize[0];
                memcpy(data, _avframe->data[0], _avframe->linesize[0]);
            }
        } else {
            *nb_samples = _avframe->nb_samples;
            *data_len = _avframe->linesize[0];
            memcpy(data, _avframe->data[0], _avframe->linesize[0]);
        }
    } else {
        av_free_packet(&pkt);
        return -1;
    }
    *timestamp = (unsigned int) (((double) pkt.pts /
                                  (double) _format_context->streams[pkt.stream_index]->time_base.den) *
                                 1000);
    av_free_packet(&pkt);
    return 0;
}

int imimusic_close_file() {
    int ret = _fini_avfilter();
    if (_avcodec) {
        avcodec_close(_avcodec);
        av_free(_avcodec);
        _avcodec = NULL;
    }
    if (_avframe) {
        av_free(_avframe);
        _avframe = NULL;
    }
    _is_interrupt = 1;
    _interrupt_time = 0;
    if (_format_context) {
        avformat_close_input(&_format_context);
        _format_context = NULL;
    }
    _avstream = NULL;
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_close_file success");
    return ret;
}

int imimusic_transmp3_init() {
    AVCodec *av = NULL;
    int ret = 0;
    if (_mp3_avcodec || _mp3_avframe) {
        log_error(MODULE_IMIMUSIC_PLAYER,
                  "imimusic_transmp3_init mp3_avcodec or mp3_avframe exist");
        return -1;
    }
    av = avcodec_find_decoder(AV_CODEC_ID_MP3);
    if (av == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_transmp3_init avcodec_find_decoder error");
        goto error;
    }
    _mp3_avcodec = avcodec_alloc_context3(av);
    _mp3_avcodec->codec_type = AVMEDIA_TYPE_AUDIO;
    ret = avcodec_open2(_mp3_avcodec, av, NULL);
    if (ret >= 0) {
        _mp3_avframe = av_frame_alloc();
        if (av->capabilities & CODEC_CAP_TRUNCATED) {
            _avframe->flags |= CODEC_FLAG_TRUNCATED;
        }
    } else {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_transmp3_init avcodec_open2 error");
        goto error;
    }
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_transmp3_init success");
    return 0;

    error:
    if (_mp3_avcodec) {
        avcodec_close(_mp3_avcodec);
        av_free(_mp3_avcodec);
        _mp3_avcodec = NULL;
    }
    if (_mp3_avframe) {
        av_free(_mp3_avframe);
        _mp3_avframe = NULL;
    }
    return -1;
}

int imimusic_transmp3_fini() {
    if (_mp3_avcodec) {
        avcodec_close(_mp3_avcodec);
        av_free(_mp3_avcodec);
        _mp3_avcodec = NULL;
    }
    if (_mp3_avframe) {
        av_free(_mp3_avframe);
        _mp3_avframe = NULL;
    }
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_transmp3_fini success");
    return 0;
}

inline int _check_mp3_header(unsigned char *data) {
    //xiaomi mp3 header
    if (*(unsigned char *) (data + 0) == 0xFF &&
        *(unsigned char *) (data + 1) == 0xF3 &&
        *(unsigned char *) (data + 2) == 0x48 &&
        *(unsigned char *) (data + 3) == 0xC4) {
        return 0;
    }
    return -1;
}

int imimusic_transmp3(unsigned char *data, unsigned int data_len, imimusic_callback_play_pcm fproc,
                      unsigned int id, void *userdata) {
    if (_mp3_avcodec == NULL || _mp3_avframe == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_transmp3 mp3_avcodec or mp3_avframe unexist");
        imimusic_transmp3_init();
    }
    if (data == NULL || data_len == 0) {
        if (_mp3_frame) {
            free(_mp3_frame);
            _mp3_frame = NULL;
            _mp3_len = 0;
        }
        fproc((const char *) data, data_len, 0, 0, id, userdata);
    } else {
        unsigned int bitrate = 0, sampling_frequency = 0, mp3_frame_len = 0;
        int ret = 0;
        unsigned int frame_index = 0;
        int got_frame_ptr = 0;
        unsigned char *mp3_data = NULL;
        unsigned int mp3_len = 0;
        unsigned char *mp3_complete = NULL;
        mp3_fixed_header *mp3_header = NULL;
        int mp3_offset = 0;
        if (_mp3_frame == NULL) {
            mp3_data = data;
            mp3_len = data_len;
            mp3_complete = NULL;
        } else {
            mp3_len = _mp3_len + data_len;
            mp3_complete = (unsigned char *) malloc(mp3_len);
            memcpy(mp3_complete, _mp3_frame, _mp3_len);
            memcpy(mp3_complete + _mp3_len, data, data_len);
            mp3_data = mp3_complete;
        }
        if (_check_mp3_header(mp3_data) == 0) {
            mp3_header = imi_parser_mp3_header_net(mp3_data);
        } else {
            log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_transmp3 check_mp3_header data error");
            while ((unsigned int) mp3_offset < mp3_len - MP3_HEADER) {
                mp3_offset++;
                if (_check_mp3_header(mp3_data + mp3_offset) == 0) {
                    log_error(MODULE_IMIMUSIC_PLAYER,
                              "imimusic_transmp3 imi_parser_mp3_header_net find next header offset = %d",
                              mp3_offset);
                    mp3_header = imi_parser_mp3_header_net(mp3_data + mp3_offset);
                    mp3_data = mp3_data + mp3_offset;
                    mp3_len = mp3_len - mp3_offset;
                    break;
                }
            }
        }
        log_info(MODULE_IMIMUSIC_PLAYER,
                 "imimusic_transmp3 bitrate_index = %d sampling_frequency = %d\n",
                 mp3_header->_bitrate_index, mp3_header->_sampling_frequency);
        bitrate = imi_parser_mp3_header_bitrate_index(mp3_header->_bitrate_index);
        sampling_frequency = imi_parser_mp3_header_sampling_frequency_index(
                mp3_header->_sampling_frequency);
        mp3_frame_len = (72000 * bitrate) / sampling_frequency + mp3_header->_padding;
        log_info(MODULE_IMIMUSIC_PLAYER,
                 "imimusic_transmp3 version = %d bitrate = %d sampling_frequency = %d mp3_frame_len = %d",
                 mp3_header->_version, bitrate, sampling_frequency, mp3_frame_len);
        while (mp3_frame_len * frame_index < mp3_len) {
            if ((mp3_len - (mp3_frame_len * frame_index)) > mp3_frame_len) {
                AVPacket avpacket;
                av_init_packet(&avpacket);
                avpacket.data = (uint8_t *) mp3_data + (mp3_frame_len * frame_index);
                avpacket.size = (int) mp3_frame_len;
                ret = avcodec_decode_audio4(_mp3_avcodec, _mp3_avframe, &got_frame_ptr, &avpacket);
                av_free_packet(&avpacket);
                if (got_frame_ptr != 0) {
                    ret = fproc((const char *) _mp3_avframe->data[0],
                                (unsigned int) _mp3_avframe->linesize[0],
                                (unsigned int) _mp3_avcodec->channels,
                                (unsigned int) _mp3_avcodec->sample_rate, id, userdata);
                    if (ret != 0) {
                        imimusic_transmp3_fini();
                        break;
                    }
                } else {
                    //do nothing
                    log_error(MODULE_IMIMUSIC_PLAYER,
                              "imimusic_transmp3 avcodec_decode_audio4 error");
                }
            }
            frame_index++;
        }
        if (mp3_frame_len * frame_index > mp3_len) {
            _mp3_len = mp3_len - (mp3_frame_len * (frame_index - 1));
            _mp3_frame = (unsigned char *) malloc(_mp3_len);
            memcpy(_mp3_frame, mp3_data + (mp3_len - _mp3_len), _mp3_len);
        } else {
            if (_mp3_frame) {
                free(_mp3_frame);
                _mp3_frame = NULL;
                _mp3_len = 0;
            }
        }
        free(mp3_header);
        if (mp3_complete) {
            free(mp3_complete);
            mp3_complete = NULL;
        }
    }
    return 0;
}

int imimusic_resample_init(unsigned int in_achannel, unsigned int in_asamplerate,
                           unsigned int out_achannel, unsigned int out_asamplerate) {
    if (_pcm_resample) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_resample_init pcm_resample is exist");
        return -1;
    }
    _pcm_resample = av_audio_resample_init(out_achannel,
                                           in_achannel,
                                           out_asamplerate,
                                           in_asamplerate,
                                           AV_SAMPLE_FMT_S16,
                                           AV_SAMPLE_FMT_S16,
                                           16,
                                           10,
                                           0,
                                           1);
    if (_pcm_resample == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_resample_init av_audio_resample_init error");
        return -1;
    }
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_resample_init success");
    return 0;
}

int imimusic_resample_fini() {
    if (_pcm_resample) {
        audio_resample_close(_pcm_resample);
        _pcm_resample = NULL;
    }
    log_info(MODULE_IMIMUSIC_PLAYER, "imimusic_resample_fini success");
    return 0;
}

int
imimusic_resample_data(unsigned char *in_data, unsigned int in_data_len, unsigned int in_nb_samples,
                       unsigned char *out_data, unsigned int *out_data_len) {
    int resample_len = 0;
    if (_pcm_resample == NULL) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_resample_data pcm_resample is unexist");
        return -1;
    }
    resample_len = audio_resample(_pcm_resample, (short *) out_data, (short *) in_data,
                                  in_nb_samples);
    if (resample_len == 0) {
        log_error(MODULE_IMIMUSIC_PLAYER, "imimusic_resample_data audio_resample error");
        return -1;
    }
    // *out_data_len = resample_len*2;//nb_sample*2
    *out_data_len = av_samples_get_buffer_size(NULL, 1, resample_len, AV_SAMPLE_FMT_S16, 1);
    return 0;
}