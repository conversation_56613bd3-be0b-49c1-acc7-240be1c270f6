plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        minSdkVersion 28
        targetSdkVersion 30
        versionCode 1
        versionName "1.0.0"

        sourceSets {
            main {
                jniLibs.srcDirs = ['jinLibs']
            }
        }

        externalNativeBuild {
            cmake {
                cppFlags("-frtti -fexceptions")
                abiFilters("armeabi-v7a"/*, "arm64-v8a"*/)
            }
        }
    }

    externalNativeBuild {
        cmake {
            path "CMakeLists.txt"
        }
    }

    android {
        lintOptions {
            abortOnError false
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    api files('libs/ipcamera.jar')
    api files('libs/guava-33.3.1-android.jar')
    api 'com.elvishew:xlog:1.11.1'
    implementation project(path: ':media_opus')
    implementation 'androidx.annotation:annotation:1.3.0'
}
