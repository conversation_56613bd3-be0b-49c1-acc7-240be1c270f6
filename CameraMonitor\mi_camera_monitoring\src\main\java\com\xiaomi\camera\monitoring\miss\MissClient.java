package com.xiaomi.camera.monitoring.miss;

import android.util.Log;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.entity.MissSession;
import com.xiaomi.camera.monitoring.utils.L;

import java.io.File;
import java.util.List;

public class MissClient {

    private static final String TAG = "MissClient";

    //====由于所有的MissClient生成的对象都是使用的都是同一个，所以要注意JNI的全局变量====
    static {
        //其他miss的so库已经在CMake里面定义加载完了，上层不要用就不需要加载
        System.loadLibrary("missjni-lib");
    }

    public MissClient(MissPorting missPorting) {
        int result = MissInit(missPorting);
        L.monitor.d("%s init miss result is %d", TAG, result);
        // 删除之前的日志文件，避免占用存储空间
        File missFile = new File("/mnt/sdcard/Android/data/com.xiaomi.mico.persistent.monitor/files/log/miss.log");
        if (missFile.exists()) {
            missFile.delete();
        }
        // 暂时不设置log路径，miss日志会和logcat一起输出
         MissSetLogPath();
    }

    /**
     * A native method that is implemented by the 'native-lib' native library,
     * which is packaged with this application.
     */
    public native int MissInit(MissPorting missPortingCb);

    public native int MissSessionQuery(int MissQueryCmd);

    public native int MissCleanBuffer(int session);

    public native int MissSetLogPath();

    public native int MissServerInit(MissDeviceInfo deviceInfo, int videoWidth, int videoHeight);

    public native int MissRpcProcess(int rpc_id, String obj, int length);

    public native void MissFinishServer();

    /*-----test-----*/
    public native int test(byte[] bytes1, byte[] bytes2);

    public void MissCmdSend(List<MissSession> sessions, int cmd, int[] params, int length, int code) {
        if (sessions != null) {
            for (MissSession session : sessions) {
                int result = MissCmdSend(session.getSession(), cmd, params, length, code);
                if (Constants.DEBUG) {
                    Log.d(Constants.TAG, "MissCmdSend session is " + session.getSession() + ", result is " + result);
                }
            }
        }
    }

    public void MissCmdSendOne(int session, int cmd, int[] params, int length, int code) {
        int result = MissCmdSend(session, cmd, params, length, code);
        if (Constants.DEBUG) {
            Log.d(Constants.TAG, "MissCmdSend session is " + session + ", result is " + result);
        }
    }

    public void MissCmdSendParams(List<MissSession> sessions, int cmd, String params, int length) {
        if (sessions != null) {
            for (MissSession session : sessions) {
                int result = MissCmdSendParams(session.getSession(), cmd, params, length);
                if (Constants.DEBUG) {
                    Log.d(Constants.TAG, "MissCmdSend session is " + session.getSession() + ", result is " + result);
                }
            }
        }
    }

    public void MissVideoSend(List<MissSession> sessions, byte[] data, int length, boolean isKeyframe, long seq, long timestamp, int codecId, int videoChn) {
        if (sessions != null) {
            for (MissSession session : sessions) {
                if (session.isRunVideo()) {
                    int result = MissVideoSend(session.getSession(), data, length, isKeyframe, seq, timestamp, codecId, videoChn);
                    if (Constants.DEBUG) {
                        Log.d(Constants.TAG, "MissVideoSend session is " + session.getSession() + ", result is " + result);
                    }
                }
            }
        }
    }

    public int MissVideoSend(MissSession session, byte[] data, int length, boolean isKeyframe, long seq, long timestamp, int codecId, int videoChn) {
        if (session != null) {
            if (session.isRunVideo()) {
                int result = MissVideoSend(session.getSession(), data, length, isKeyframe, seq, timestamp, codecId, videoChn);
                if (Constants.DEBUG) {
                    Log.d(Constants.TAG, "MissVideoSend session is " + session.getSession() + ", result is " + result);
                }
                return result;
            }
        }
        return -1;
    }

    public void MissAudioSend(List<MissSession> sessions, byte[] data, int length, long seq, long timestamp) {
        if (sessions != null) {
            for (MissSession session : sessions) {
                if (session.isRunAudio()) {
                    int result = MissAudioSend(session.getSession(), data, length, seq, timestamp);
                    if (Constants.DEBUG) {
                        Log.d(Constants.TAG, "MissAudioSend session is " + session.getSession() + ", result is " + result);
                    }
                }
            }
        }
    }

    public void MissCloseServerSession(List<MissSession> sessions) {
        if (sessions != null) {
            while (!sessions.isEmpty()) {
                MissCloseServerSession(sessions.get(0).getSession());
            }
        }
    }

    // code 0 成功 -1失败
    private native int MissCmdSend(int session, int cmd, int[] params, int length, int code);
    private native int MissCmdSendParams(int session, int cmd, String params, int length);

    private native int MissVideoSend(int session, byte[] data, int length, boolean isKeyframe, long seq, long timestamp, int codecId, int videoChn);

    private native int MissAudioSend(int session, byte[] data, int length, long seq, long timestamp);

    private native void MissCloseServerSession(int session);

    public static native byte[] PcmToG711A(byte[] pcmData, int length);

}
