package com.xiaomi.camera.monitoring;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.util.Log;

import java.nio.ByteBuffer;

/**
 * 解码一帧H265数据到yuv
 */
public class DecodeOneFrame{
    private static final String TAG = "DecodeOneFrame";

    private static final int FRAME_RATE = 20;
    private static final int BIT_RATE_720P = 800_000;

    private MediaCodec mediaCodec;
    private DecodeCallback mDecodeCallback;
    private MediaCodec.BufferInfo mBufferInfo;
    private MediaFormat mMediaFormat;

    public DecodeOneFrame(DecodeCallback callback) {
        this.mDecodeCallback = callback;
        mMediaFormat = MediaFormat.createVideoFormat(Constants.CAMERA_ENCODE_TYPE,
                Constants.CAMERA1_FRAME_SIZE_WIDTH, Constants.CAMERA1_FRAME_SIZE_HEIGHT);

        mMediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible);

        mMediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE_720P);

        mMediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);//I帧间隔1
        mMediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);//帧率
        try {
            mediaCodec = MediaCodec.createDecoderByType(Constants.CAMERA_ENCODE_TYPE);
            mediaCodec.configure(mMediaFormat, null, null, 0);
            mediaCodec.start();
        } catch (Exception e) {
            Log.e(TAG, "init DecodeHevc throw exception:", e);
            mediaCodec = null;
        }
        mBufferInfo = new MediaCodec.BufferInfo();
    }

    public synchronized void decodeOneFrame(byte[] onFrame) throws IllegalStateException {
        if (mediaCodec == null) {
            return ;
        }
        try {
            final int inputBufferIndex = mediaCodec.dequeueInputBuffer(-1);
            if (inputBufferIndex >= 0) {
                final ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputBufferIndex);
                inputBuffer.clear();
                if (onFrame != null) {
                    inputBuffer.put(onFrame);
                    inputBuffer.limit(onFrame.length);
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, onFrame.length, System.nanoTime(), 0);
                } else {//当buff为只读的情况下
                    inputBuffer.put(onFrame);
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, onFrame.length, System.nanoTime(), 0);
                }
            }

            int outputBufferIndex = mediaCodec.dequeueOutputBuffer(mBufferInfo, 0);
            while (outputBufferIndex >= 0) {
                ByteBuffer outputBuffer = mediaCodec.getOutputBuffer(outputBufferIndex);
                byte[] outData = new byte[mBufferInfo.size];
                outputBuffer.get(outData);
                mDecodeCallback.onDecode(outData);

                mediaCodec.releaseOutputBuffer(outputBufferIndex, true);
                outputBufferIndex = mediaCodec.dequeueOutputBuffer(mBufferInfo, 0);
            }
        } catch (Exception e) {
            Log.e(TAG, "DecodeHevc throw exception, then restart:", e);
            try {
                mediaCodec = MediaCodec.createDecoderByType(Constants.CAMERA_ENCODE_TYPE);
                mediaCodec.configure(mMediaFormat, null, null, 0);
                mediaCodec.start();
            } catch (Exception ex) {
                Log.e(TAG, "restart DecodeHevc throw exception:", ex);
                mediaCodec = null;
            }
        }
    }

    public void release() {
        if (mediaCodec != null) {
            try {
                mediaCodec.stop();
                mediaCodec.release();
            } catch (Exception e) {
                Log.e(TAG, "stop Encode throw exception:", e);
            }
        }
        this.mDecodeCallback = null;
    }
}
