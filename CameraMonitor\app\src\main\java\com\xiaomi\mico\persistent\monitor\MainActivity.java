package com.xiaomi.mico.persistent.monitor;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.media.MediaCodecInfo;
import android.media.MediaCodecList;
import android.media.MediaFormat;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.widget.Button;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;

import com.xiaomi.camera.monitoring.AudioRecordManager;
import com.xiaomi.camera.monitoring.CameraVideoManager;
import com.xiaomi.camera.monitoring.DecodeHevc;
import com.xiaomi.camera.monitoring.UVCCamera1VideoManager;
import com.xiaomi.camera.monitoring.entity.FrameData;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.cloud.CloudRecord;
import com.xiaomi.mico.persistent.utils.FileUtils;
import com.xiaomi.mico.persistent.utils.H265DeCodePlay;
import com.xiaomi.mico.persistent.utils.IntByteStringHexUtil;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.security.KeyStore;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Enumeration;
import java.util.List;


public class MainActivity extends AppCompatActivity {

    private static final String TAG = "MainActivity";

    private boolean mPaused = false;
    private boolean mUVCStart = false;
    private boolean mAudioStart = false;
    private SurfaceView mSurfaceView;
    private SurfaceHolder mSurfaceHolder;
    private DecodeHevc mDecodeHevc;
    private CloudRecord mCloudRecord;
    private H265DeCodePlay mH265DeCodePlay;

    private final int EVENT_CLOUD_TEST = 0x01;
    private Handler mHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            switch (msg.what) {
                case EVENT_CLOUD_TEST:
//                    public static final byte EVENT_PERSON_DETECT = 0;
//                    public static final byte EVENT_SCENE_CHANGED = 1;
//                    public static final byte EVENT_CRY_DETECT = 2;
//                    int randomIndex = (int) (Math.random() * 3);
//                    MotionDetection.getInstance().testEvent(randomIndex);
//                    mHandler.sendEmptyMessageDelayed(EVENT_CLOUD_TEST, 2000);
                    break;

            }
            return false;
        }
    });

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        Button btnConnect = findViewById(R.id.btn_miss);
        btnConnect.setOnClickListener(v ->
                CameraMonitorManager.getInstance().initMiss(getApplicationContext()));

        Button btnCloudUpload = findViewById(R.id.btn_cloud_upload);
        btnCloudUpload.setOnClickListener(v -> {
//            ArrayList<String> fileUrlList = new ArrayList<>();
//            fileUrlList.add("/mnt/sdcard/bbb.mp4");
//            fileUrlList.add("/mnt/sdcard/ccc.mp4");
//            new MiUploadFile(fileUrlList, new File("/mnt/sdcard/aaa.jpg"),
//                    false, false, "Demolition")
//                    .getDomainUrl();
        });
        Button btnEventStart = findViewById(R.id.btn_event_start);
        btnEventStart.setOnClickListener(v -> {
//            mHandler.sendEmptyMessage(EVENT_CLOUD_TEST);
//            Intent intent = new Intent(MainActivity.this, MiVoipActivity.class);
//            startActivity(intent);
        });

        Button btnEventStop = findViewById(R.id.btn_event_stop);
        btnEventStop.setOnClickListener(v -> {
//            UVCEventManager.getInstance().stopEvent();
//            if (mCloudRecord != null) {
//                mCloudRecord.stopRecord(System.currentTimeMillis());
//                mCloudRecord = null;
//            }
            if (mHandler.hasMessages(EVENT_CLOUD_TEST)) {
                mHandler.removeMessages(EVENT_CLOUD_TEST);
            }
        });

        Button btnAudio = findViewById(R.id.btn_audio);
        btnAudio.setOnClickListener(v -> {
//            if (!mAudioStart) {
//                mAudioStart = true;
//                FileUtils.createFileToByte("/mnt/sdcard/aaa.pcm");
//                AudioRecordManager.getInstance().setAudioProducerCamera((audioFrameData) -> {
//                    Log.i(TAG, "Audio buffer len:" + audioFrameData.length + " seq:" + audioFrameData.index);
//                    FileUtils.writeByteToFile(audioFrameData.dataBytes);
//                });
//                AudioRecordManager.getInstance().startAudio();
//            } else {
//                mAudioStart = false;
//                AudioRecordManager.getInstance().stopAudio();
//                FileUtils.closeFileToByte();
//            }
        });

        Button btnUvc = findViewById(R.id.btn_uvc);
        btnUvc.setOnClickListener(v -> {
//            if (!mUVCStart) {
//                mUVCStart = true;
//                UVCCamera1VideoManager.getInstance().setVideoProducerCamera(keyFrameData -> {
//                    if (mCloudRecord != null) {
//                        mCloudRecord.offerMainVideoData(keyFrameData);
//                    }
//                    if (mDecodeHevc != null) {
//                        FrameData frameDataTmp = new FrameData(System.currentTimeMillis());
//                        frameDataTmp.dataBytes = keyFrameData.dataBytes;
//                        mDecodeHevc.decode(frameDataTmp);
//                    }
//                });
//            } else {
//                mUVCStart = false;
//                UVCCamera1VideoManager.getInstance().removeVideoProducerCamera();
//            }
        });

        Button btnDecoder = findViewById(R.id.btn_decoder);
        mSurfaceView = findViewById(R.id.decode_surface);
        mSurfaceHolder = mSurfaceView.getHolder();
        btnDecoder.setOnClickListener(v -> {
            mDecodeHevc = new DecodeHevc(mSurfaceHolder.getSurface());
            mDecodeHevc.init(MediaFormat.MIMETYPE_VIDEO_HEVC, 640, 480);
            mDecodeHevc.startDecoder();
        });

        Button btnMp4 = findViewById(R.id.btn_mp4);
        btnMp4.setOnClickListener(v -> {
            if (!mPaused) {
                getSupportedEncoders();
                mPaused = true;
//                AKMp4Muxer.createFileJava("/mnt/sdcard/test.mp4", AKMediaCoreConstant.VideoCodecId.VIDEO_CODEC_H264);
                CameraManager cameraManager = (CameraManager) getSystemService(Context.CAMERA_SERVICE);
                try {
                    if (ActivityCompat.checkSelfPermission(MainActivity.this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                        Log.e(TAG, "the permission android.permission.CAMERA denied.");
                        return;
                    }
                    cameraManager.openCamera(Integer.toString(0), new CameraDevice.StateCallback() {
                        @Override
                        public void onOpened(@NonNull CameraDevice camera) {
                            Log.d(TAG, "camera opened.");
                            CameraVideoManager.getInstance().init(MediaFormat.MIMETYPE_VIDEO_AVC, keyFrameData -> {
//                                mMissClient.MissVideoSend(mSessionList, keyFrameData.dataBytes,
//                                        keyFrameData.length, keyFrameData.isIFrame, keyFrameData.index,
//                                        keyFrameData.timeStamp, getEncodeIdByType());
                                Log.d(TAG, "length: " + keyFrameData.length + " -->isIFrame: " + keyFrameData.isIFrame
                                        + " -->index:" + keyFrameData.index
                                        + " -->timeStamp:" + keyFrameData.timeStamp);
                                Log.d(TAG, "dataBytes: " + Arrays.toString(keyFrameData.dataBytes));
                                if (mDecodeHevc != null) {
                                    FrameData frameData = new FrameData(System.currentTimeMillis());
                                    frameData.dataBytes = keyFrameData.dataBytes;
                                    mDecodeHevc.decode(frameData);
                                }
//                                AKMp4Muxer.writeVideoFrameFileJava(keyFrameData.dataBytes, keyFrameData.timeStamp);
                            }, camera, 1920, 1080);
                            CameraVideoManager.getInstance().startRecord();
                        }

                        @Override
                        public void onDisconnected(@NonNull CameraDevice camera) {
                            Log.d(TAG, "camera disconnected.");
                        }

                        @Override
                        public void onError(@NonNull CameraDevice camera, int error) {
                            Log.e(TAG, "open camera onError, error is " + error);
                        }
                    }, new Handler(Looper.getMainLooper()));
                } catch (Exception e) {
                    Log.e(TAG, "open camera throw exception: ", e);
                }
            } else {
                mPaused = false;
                CameraVideoManager.getInstance().stopRecord();
//                AKMp4Muxer.closeFileJava();
            }
        });
    }

    public void getSupportedEncoders() {
        MediaCodecList codecList = new MediaCodecList(MediaCodecList.ALL_CODECS);
        MediaCodecInfo[] codecInfos = codecList.getCodecInfos();
        List<String> supportedCodecs = new ArrayList<>();
        for (MediaCodecInfo codecInfo : codecInfos) {
            if (codecInfo.isEncoder()) {
                String[] supportedTypes = codecInfo.getSupportedTypes();
                for (String type : supportedTypes) {
                    if (type.startsWith("video/")) {
                        supportedCodecs.add(codecInfo.getName());
                        break;
                    }
                }
            }
        }
        Log.d(TAG, "supportedCodecs" + supportedCodecs.size() + " --> Supported Codecs" +  supportedCodecs.toString());
    }

    private void getAndroidCAStore() {
        // 加载Android系统的默认KeyStore
        try {
            KeyStore ks = KeyStore.getInstance("AndroidCAStore");
            ks.load(null, null);

            // 获取所有证书条目
            Enumeration<String> aliases = ks.aliases();
            while (aliases.hasMoreElements()) {
                String alias = aliases.nextElement();
                X509Certificate cert = (X509Certificate) ks.getCertificate(alias);

                System.out.println("Certificate: " + alias);
                System.out.println("Not Before: " + cert.getNotBefore());
                System.out.println("Not After : " + cert.getNotAfter());
                System.out.println();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private static FileOutputStream fileOutputStream;
    private static BufferedOutputStream bufferedOutputStream;
    public static void createFileToByte(String filePath) {
        File file = new File(filePath);
        if (file.exists()) {
            file.delete();
        }
        try {
            fileOutputStream = new FileOutputStream(file);
            bufferedOutputStream = new BufferedOutputStream(fileOutputStream);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void writeByteToFile(byte[] data) {
        if (bufferedOutputStream != null) {
            try {
                bufferedOutputStream.write(data);
                bufferedOutputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void closeFileToByte() {
        if (bufferedOutputStream != null) {
            try {
                bufferedOutputStream.flush();
                bufferedOutputStream.close();
                bufferedOutputStream = null;
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    class ServerSocketThread extends Thread {

        @Override
        public void run() {
            try {
                // 创建ServerSocket
                ServerSocket serverSocket = new ServerSocket(8080);
                L.monitor.d("--开启服务器，监听端口 9999--");

                // 监听端口，等待客户端连接
                while (true) {
                    L.monitor.d("--等待客户端连接--");
                    Socket socket = serverSocket.accept(); //等待客户端连接
                    L.monitor.d("--得到客户端连接--");
                    startReader(socket);
                }

            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 从参数的Socket里获取消息
     */
    private static void startReader(final Socket socket) {
        new Thread(){
            @Override
            public void run() {
                byte[] buffer = new byte[1024 * 32];
                while (socket != null && !socket.isClosed()) {
                    try {
                        if (socket.getInputStream().available() > 0) {
                            int bytesRead = socket.getInputStream().read(buffer);
                            if (bytesRead > 0) {
                                L.monitor.d("AAAAAAA:" + bytesRead);
                                byte[] byteData = new byte[bytesRead];
                                System.arraycopy(buffer, 0, byteData, 0, bytesRead);
                                writeByteToFile(byteData);
                            }
                        } else {
                            Thread.sleep(100); // 休眠一段时间，避免忙等待
                        }
                    } catch (IOException | InterruptedException e) {
                        break;
                    }
                }
            }
        }.start();
    }

    public static void addHeaderInfo() {
        String inputFilePath = "/mnt/sdcard/encrypt-1744942750634.jpg";
        String outputFilePath = "/mnt/sdcard/bbb.jpg";

        try {
            // 读取原始文件内容
            byte[] fileContent = readFile(inputFilePath);

            // 获取文件长度
            int fileLength = fileContent.length;
            int headLen = 4;
            int infoLen = 53;

            // 创建一个新的字节数组，长度为原始文件长度 + 4
            byte[] newFileContent = new byte[fileLength + headLen + infoLen];

            // 1.将文件长度信息（4个字节）写入新数组的前4个字节
            System.arraycopy(intToByteArray(fileLength + infoLen), 0, newFileContent, 0, headLen);

            // 添加数据信息
            byte[] info = new byte[infoLen];
            info[0] = 0x01; //文件类型
            info[1] = 0x02; //事件类型
            info[2] = 0x00;
            info[3] = 0x00; //视频或图片index
            info[4] = 0x00; //是否最一个视频文件
            info[5] = 0x00; //预留站位
            info[6] = 0x00; //预留站位
            info[7] = 0x00; //预留站位

            String sha256 = FileUtils.getFileSign(new File(inputFilePath), "SHA-256");
            byte[] sha256Byte = IntByteStringHexUtil.hexStrToByteArray(sha256);
            System.arraycopy(sha256Byte, 0, info, 7, sha256Byte.length);

            String md5 = FileUtils.getFileSign(new File(inputFilePath), "MD5");
            byte[] md5Byte = IntByteStringHexUtil.hexStrToByteArray(md5);
            System.arraycopy(md5Byte, 0, info, 40, md5Byte.length);
            // 2.将数据信息写入数据数组
            System.arraycopy(info, 0, newFileContent, headLen, infoLen);
            // 3.将原始文件内容写入新数组的剩余部分
            System.arraycopy(fileContent, 0, newFileContent, infoLen + headLen, fileLength);

            // 将新数组写入输出文件
            writeFile(outputFilePath, newFileContent);
            L.monitor.d("addHeaderInfo done");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    // 读取文件内容到字节数组
    private static byte[] readFile(String filePath) throws IOException {
        File file = new File(filePath);
        byte[] buffer = new byte[(int) file.length()];
        try (FileInputStream fis = new FileInputStream(file)) {
            fis.read(buffer);
        }
        return buffer;
    }

    // 将字节数组写入文件
    private static void writeFile(String filePath, byte[] content) throws IOException {
        try (FileOutputStream fos = new FileOutputStream(filePath)) {
            fos.write(content);
        }
    }

    // 将整数转换为4个字节的字节数组
    private static byte[] intToByteArray(int value) {
        return new byte[] {
                (byte) (value >>> 24),
                (byte) (value >>> 16),
                (byte) (value >>> 8),
                (byte) value
        };
    }
}
