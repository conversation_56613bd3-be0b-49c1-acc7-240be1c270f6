package com.xiaomi.mico.persistent.func;

import android.hardware.ipcamera.V1_0.COLOR_MODE;
import android.hardware.ipcamera.V1_0.ICameraScenesController;
import android.hardware.ipcamera.V1_0.ISceneSwitchCallback;
import android.hardware.ipcamera.V1_0.SceneMode;
import android.os.RemoteException;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.spec.SpecEventSend;

/**
 * 夜视功能设置
 */
public class CameraScenesManager {

    private final String TAG = "CameraScenesManager";

    private volatile static CameraScenesManager mInstance;
    private ICameraScenesController mScenesController;
    private volatile int mCurrentMode = SceneMode.SCENE_UNKNOW;
    private volatile int mCurrentScenesMode = -1; // 设置夜视切换回调 0-夜晚 1-白天

    private byte[] scenesType = new byte[]{SceneMode.SCENE_NIGHT, SceneMode.SCENE_DAY, SceneMode.SCENE_AUTO};

    public static CameraScenesManager getInstance() {
        if (mInstance == null) {
            synchronized (CameraScenesManager.class) {
                if (mInstance == null) {
                    mInstance = new CameraScenesManager();
                }
            }
        }
        return mInstance;
    }

    private CameraScenesManager() {
        try {
            mScenesController =  ICameraScenesController.getService(Constants.IPCAMERA_SERVICE_NAME);
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    public void init() {
        if (mScenesController != null) {
//            L.monitor.d("%s =====init=====", TAG);
            registerSceneSwitchCb();
        }
    }

    // Spec对应值 0: 打开 1: 关闭 2: 自动
    public void updateScenesMode(int mode) {
        L.monitor.i("%s updateScenesMode: %d", TAG, mode);
        try {
            if (mCurrentMode == mode) {
                L.monitor.i("%s mCurrentMode == mode", TAG);
                return;
            }
            if (mScenesController != null) {
                //设置场景模式，0 白天 1 夜晚 2 自动（预留）
                mCurrentMode = mode;
                mScenesController.setCameraSceneMode(scenesType[mode]);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    // 设置微光全彩模式 0-正常模式 1-微光全彩
    public void updateColorMode(boolean isFullColor) {
        L.monitor.i("%s updateColorMode: %s", TAG, isFullColor);
        try {
            if (mScenesController != null) {
                mScenesController.setColorMode(isFullColor ?
                        COLOR_MODE.FULL_COLOR_MODE : COLOR_MODE.NORMAL_COLOR_MODE);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    /*
     * 设置夜视切换回调 0-夜晚 1-白天
     * 触发切换时会返回此回调
     */
    private void registerSceneSwitchCb() {
        try {
            if (mScenesController != null) {
                mScenesController.registerSceneSwitchCallBack(new ISceneSwitchCallback.Stub() {

                    @Override
                    public void onSceneSwitchCB(int mode) {
                        L.monitor.i("%s onSceneSwitchCB: %d", TAG, mode);
                        if (mCurrentScenesMode != mode) {
                            SpecEventSend.eventIRCurtReport(mode == 0);
                            mCurrentScenesMode = mode;
                        }
                    }
                });
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    // 设置休眠状态 睡眠:true 唤醒:false
    public void setCameraMode(boolean isSleep) {
        L.monitor.i("%s setCameraMode: %s", TAG, isSleep);
        try {
            if (mScenesController != null) {
                mScenesController.setCameraSleepMode(isSleep);
            }
         } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }
}
