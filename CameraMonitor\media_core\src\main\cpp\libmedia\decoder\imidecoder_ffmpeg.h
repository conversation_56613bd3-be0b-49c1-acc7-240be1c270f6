#pragma once

#include "imimedia_include.h"

typedef struct imidecoder_ffmpeg_info_s *imidecoder_ffmpeg_info_t;

typedef struct _imidecoder_codec_info {
	/*video_codec_id or audio_codec_id*/unsigned int codec;
	/*pixel_format_id or sample_format_id*/unsigned int format;
	unsigned int width, height;
	unsigned int sample_rate;
	unsigned int channels;
} imidecoder_codec_info;

int imidecoder_open(imidecoder_codec_info *info,
	/*out*/imidecoder_ffmpeg_info_t *handle);

int imidecoder_one_frame(imidecoder_ffmpeg_info_t handle,
	endecoder_frame_info *frame);

int imidecoder_close(imidecoder_ffmpeg_info_t handle);
