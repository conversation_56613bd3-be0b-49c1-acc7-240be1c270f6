package com.xiaomi.camera.monitoring;

import android.media.AudioRecord;
import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.util.Log;

import com.xiaomi.camera.monitoring.entity.AudioFrameData;

import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * 音频AAC编码器
 */
public class EncodeAAC {

    private static final String TAG = "SZMEncodeAAC";

    private MediaCodec mEncoder;
    private MediaCodec.BufferInfo bufferInfo;

    private AudioAACStreamProducer audioProducer;

    private int sampleRateInHz;
    private int channelConfig;
    private int audioFormat;

    private long generateIndex = 0;

    public EncodeAAC(AudioAACStreamProducer audioProducer) {
        this.audioProducer = audioProducer;
    }

    public void init(Object... params) {
        sampleRateInHz = (int) params[0];
        channelConfig = (int) params[1];
        audioFormat = (int) params[2];
        int minBufferSize = AudioRecord.getMinBufferSize(sampleRateInHz, channelConfig, audioFormat);
        try {
            //参数对应-> mime type、采样率、声道数
            MediaFormat encodeFormat = MediaFormat.createAudioFormat(MediaFormat.MIMETYPE_AUDIO_AAC, sampleRateInHz, 1);
            encodeFormat.setString(MediaFormat.KEY_MIME, MediaFormat.MIMETYPE_AUDIO_AAC);
            encodeFormat.setInteger(MediaFormat.KEY_AAC_PROFILE, MediaCodecInfo.CodecProfileLevel.AACObjectLC);
            encodeFormat.setInteger(MediaFormat.KEY_BIT_RATE, 20 * 10000);

            encodeFormat.setInteger(MediaFormat.KEY_SAMPLE_RATE, AudioConfig.AUDIO_SAMPLE_RATE);

            encodeFormat.setInteger(MediaFormat.KEY_CHANNEL_COUNT, 1);
            encodeFormat.setInteger(MediaFormat.KEY_CHANNEL_MASK, channelConfig);
            encodeFormat.setInteger(MediaFormat.KEY_MAX_INPUT_SIZE, minBufferSize * 4);//作用于inputBuffer的大小
            mEncoder = MediaCodec.createEncoderByType(MediaFormat.MIMETYPE_AUDIO_AAC);
            mEncoder.configure(encodeFormat, null, null, MediaCodec.CONFIGURE_FLAG_ENCODE);
        } catch (IOException e) {
            e.printStackTrace();
        }

        if (mEncoder == null) {
            Log.e(TAG, "create mEncoder failed");
            return;
        }

        mEncoder.start();
    }

    public void startEncoder() {
        if (mEncoder == null) {
            Log.e(TAG, "还未Init，请先init");
            return;
        }
        generateIndex = 0;
        bufferInfo = new MediaCodec.BufferInfo();
    }

    public void encode(byte[] buffer) throws IllegalStateException {
        int inputBufferIndex = mEncoder.dequeueInputBuffer(0);
        if (inputBufferIndex >= 0) {
            ByteBuffer inputBuffer = mEncoder.getInputBuffer(inputBufferIndex);
            if (inputBuffer != null) {
                inputBuffer.clear();
                if (inputBuffer.remaining() >= buffer.length) {
                    inputBuffer.put(buffer);
                    inputBuffer.limit(buffer.length);
                } else {
                    Log.e(TAG, "inputBuffer length to short");
                    return;
                }
                mEncoder.queueInputBuffer(inputBufferIndex, 0, buffer.length, System.nanoTime(), 0);
            }
        }

        // 取出编码好的一帧音频数据，然后给这一帧添加ADTS头
        int outputBufferIndex = mEncoder.dequeueOutputBuffer(bufferInfo, 0);
        generateIndex++;//有可能一帧音频原始数据会被AAC编码成多个数据返回，也有可能是0个 （0~2）
        while (outputBufferIndex >= 0) {
            ByteBuffer outputBuffer = mEncoder.getOutputBuffer(outputBufferIndex);
            if (outputBuffer != null) {
                int outBufferSize = outputBuffer.limit() + 7;
                byte[] aacBytes = new byte[outBufferSize];
                addADTStoPacket(aacBytes, outBufferSize);
                outputBuffer.get(aacBytes, 7, outBufferSize - 7);
                //AAC格式的时间戳，使用是编码完成时候的时间戳，而不是音频原始数据生成的时候
                AudioFrameData pFrameData = new AudioFrameData(System.currentTimeMillis());
                pFrameData.index = generateIndex;
                pFrameData.dataBytes = aacBytes;
                pFrameData.setDataLen(outBufferSize);
                if (audioProducer != null) {
                    audioProducer.offerData(pFrameData);
                }
                //释放资源
                mEncoder.releaseOutputBuffer(outputBufferIndex, false);
            }
            outputBufferIndex = mEncoder.dequeueOutputBuffer(bufferInfo, 0);
        }

    }

    /**
     * Add ADTS header at the beginning of each and every AAC packet.
     * This is needed as MediaCodec encoder generates a packet of raw
     * AAC data.
     * <p>
     * Note the packetLen must count in the ADTS header itself !!! .
     * 注意，这里的packetLen参数为raw aac Packet Len + 7; 7 bytes adts header
     **/
    private void addADTStoPacket(byte[] packet, int packetLen) {
        int profile = MediaCodecInfo.CodecProfileLevel.AACObjectLC;  //AAC LC，MediaCodecInfo.CodecProfileLevel.AACObjectLC;
        int freqIdx = AudioConfig.AUDIO_SAMPLE_RATE == 8000 ? 11 : 8;  //见后面注释avpriv_mpeg4audio_sample_rates中对应的数组下标，来自ffmpeg源码
        int chanCfg = 1;  //见后面注释channel_configuration，AudioFormat.CHANNEL_IN_MONO 单声道(声道数量)

        /*int avpriv_mpeg4audio_sample_rates[] = {96000, 88200, 64000, 48000, 44100, 32000,24000, 22050, 16000, 12000, 11025, 8000, 7350};
        channel_configuration: 表示声道数chanCfg
        0: Defined in AOT Specifc Config
        1: 1 channel: front-center
        2: 2 channels: front-left, front-right
        3: 3 channels: front-center, front-left, front-right
        4: 4 channels: front-center, front-left, front-right, back-center
        5: 5 channels: front-center, front-left, front-right, back-left, back-right
        6: 6 channels: front-center, front-left, front-right, back-left, back-right, LFE-channel
        7: 8 channels: front-center, front-left, front-right, side-left, side-right, back-left, back-right, LFE-channel
        8-15: Reserved
        */

        // fill in ADTS data
        packet[0] = (byte) 0xFF;
        //packet[1] = (byte)0xF9;
        packet[1] = (byte) 0xF1;//解决ios 不能播放问题
        packet[2] = (byte) (((profile - 1) << 6) + (freqIdx << 2) + (chanCfg >> 2));
        packet[3] = (byte) (((chanCfg & 3) << 6) + (packetLen >> 11));
        packet[4] = (byte) ((packetLen & 0x7FF) >> 3);
        packet[5] = (byte) (((packetLen & 7) << 5) + 0x1F);
        packet[6] = (byte) 0xFC;
    }

    public void stopEncoder() {
        if (mEncoder != null) {
            try {
                mEncoder.stop();
                mEncoder.release();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    public void release() {
        stopEncoder();
        if (mEncoder != null) {
            mEncoder.release();
            mEncoder = null;
        }
        audioProducer = null;
    }

    public interface AudioAACStreamProducer {
        void offerData(AudioFrameData data);
    }
}
