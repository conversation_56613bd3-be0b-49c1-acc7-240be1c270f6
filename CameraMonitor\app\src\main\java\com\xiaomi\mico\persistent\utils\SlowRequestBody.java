package com.xiaomi.mico.persistent.utils;

import java.io.IOException;
import java.io.InputStream;

import okhttp3.MediaType;
import okhttp3.RequestBody;
import okio.Buffer;
import okio.BufferedSink;
import okio.Okio;
import okio.Source;

public class SlowRequestBody extends RequestBody {

    private final RequestBody originalBody;
    private final long maxBytesPerSecond; // 限速，比如：50 * 1024 表示每秒 50KB

    public SlowRequestBody(RequestBody originalBody, long maxBytesPerSecond) {
        this.originalBody = originalBody;
        this.maxBytesPerSecond = maxBytesPerSecond;
    }

    @Override
    public MediaType contentType() {
        return originalBody.contentType();
    }

    @Override
    public long contentLength() throws IOException {
        return originalBody.contentLength();
    }

    @Override
    public void writeTo(BufferedSink sink) throws IOException {
        Source source = Okio.source(originalBodyToInputStream(originalBody));
        long totalRead = 0;
        long bufferSize = 8 * 1024; // 每次最多读取 8KB
        long sleepTime = (1000 * bufferSize) / maxBytesPerSecond;

        Buffer buffer = new Buffer();
        long read;

        while ((read = source.read(buffer, bufferSize)) != -1) {
            sink.write(buffer, read);
            totalRead += read;
            sink.flush(); // 确保实时传输
            try {
                Thread.sleep(sleepTime); // 控制速度
            } catch (InterruptedException e) {
                throw new IOException("Upload throttled", e);
            }
        }
    }

    private InputStream originalBodyToInputStream(RequestBody body) throws IOException {
        Buffer buffer = new Buffer();
        body.writeTo(buffer);
        return buffer.inputStream();
    }
}

