package com.xiaomi.mico.persistent.entry;

public class AiDetectionConfig {
    private boolean faceDetection = false; //人脸检测开关
    private boolean humanDetection = true; //人形检测开关
    private boolean moveDetection = false; //画面变动检测
    private boolean babyCryDetection = false; //哭声检测开关
    private int sensitivity = 0; //画面变动灵敏度

    public boolean isFaceDetection() {
        return faceDetection;
    }

    public void setFaceDetection(boolean faceDetection) {
        this.faceDetection = faceDetection;
    }

    public boolean isHumanDetection() {
        return humanDetection;
    }

    public void setHumanDetection(boolean humanDetection) {
        this.humanDetection = humanDetection;
    }

    public boolean isMoveDetection() {
        return moveDetection;
    }

    public void setMoveDetection(boolean moveDetection) {
        this.moveDetection = moveDetection;
    }

    public boolean isBabyCryDetection() {
        return babyCryDetection;
    }

    public void setBabyCryDetection(boolean babyCryDetection) {
        this.babyCryDetection = babyCryDetection;
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = sensitivity;
    }

    @Override
    public String toString() {
        return "AiDetectionConfig{" +
                "faceDetection=" + faceDetection +
                ", humanDetection=" + humanDetection +
                ", moveDetection=" + moveDetection +
                ", babyCryDetection=" + babyCryDetection +
                ", sensitivity=" + sensitivity +
                '}';
    }
}
