package com.xiaomi.mico.persistent.spec;

import android.content.ContentProvider;
import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.cloud.MotionDetection;
import com.xiaomi.mico.persistent.func.CameraRotateManager;

public class SpecActionContentProvider extends ContentProvider {
    private static final String TAG = "SpecActionContentProvider";

    private final String METHOD_EXECUTE_ACTION = "execute_action";
    private final String KEY_CODE = "code";
    private final String KEY_SIID = "siid";
    private final String KEY_AIID = "aiid";
    private final String METHOD_TRANSMIT_OT_MESSAGE = "transmit_ot_message";
    private final String KEY_MESSAGE = "message";

    // 摄像机控制
    private final int CAMERA_CONTROL_SIID = 11;
    private final int CAMERA_CONTROL_PTZ_AIID = 1; // 云台校准
    private final int CAMERA_CONTROL_RESTART_AIID = 2; // 重启设备

    // 自定义功能
    private final int OTHER_FUNCTION_SIID = 12;
    private final int OTHER_FUNCTION_UPLOAD_VIDEO_AIID = 1; // 录制视频并上传

    @Nullable
    @Override
    public Bundle call(@NonNull String method, @Nullable String arg, @Nullable Bundle extras) {
        L.monitor.i("%s method is %s", TAG, method);
        Bundle result = new Bundle();
        if (extras != null) {
            if (METHOD_EXECUTE_ACTION.equals(method)) {
                int siid = extras.getInt(KEY_SIID);
                int aiid = extras.getInt(KEY_AIID);
                L.monitor.i("%s siid: %d aiid: %d", TAG, siid, aiid);
                if (CAMERA_CONTROL_SIID == siid) {
                    if (CAMERA_CONTROL_PTZ_AIID == aiid) { // 电机校准
                        CameraRotateManager.getInstance().resetMotorStep(false);
                    } else if (CAMERA_CONTROL_RESTART_AIID == aiid) { // 重启设备
                        PowerManager pManager = (PowerManager) getContext().getSystemService(Context.POWER_SERVICE);
                        pManager.reboot("reboot-app-control");
                    }
                } else if (OTHER_FUNCTION_SIID == siid) {
                    if (OTHER_FUNCTION_UPLOAD_VIDEO_AIID == aiid) { // 录制视频并上传
                        MotionDetection.getInstance().uploadVideo();
                    }
                }
            } else if (METHOD_TRANSMIT_OT_MESSAGE.equals(method)) {
//                {"from":"smartCamera","id":40950086,"method":"motionDetectionSwitch","params":{"detectionSwitch":true,"endTime":"23:59:00","interval":10,"startTime":"00:00:00","trackSwitch":false},"miio_client_from":"cloud"}
//                {"from":"smartCamera","id":53285122,"method":"cloudSwitch","params":{"cloudSwitch":true},"miio_client_from":"cloud"}
                String message = extras.getString(KEY_MESSAGE);
                L.monitor.i("%s message: %s", TAG, message);
                JsonObject jsonObject = new Gson().fromJson(message, JsonObject.class);
                String transmitMethod = jsonObject.get("method").getAsString();
                if ("cloudSwitch".equals(transmitMethod)) {
                    JsonObject paramsObj = jsonObject.getAsJsonObject("params");
                    boolean cloudSwitch = paramsObj.get("cloudSwitch").getAsBoolean();
                    Settings.Global.putInt(getContext().getContentResolver(), Constants.KEY_CLOUD_SWITCH_CONFIG, cloudSwitch ? 1 : 0);
                }
            }
            result.putInt(KEY_CODE, 0);
        } else {
            result.putInt(KEY_CODE, -1);
        }

        return result;
    }

    @Override
    public int delete(Uri uri, String selection, String[] selectionArgs) {
        return 0;
    }

    @Override
    public String getType(Uri uri) {
        return null;
    }

    @Override
    public Uri insert(Uri uri, ContentValues values) {
        return null;
    }

    @Override
    public boolean onCreate() {
        return true;
    }

    @Override
    public Cursor query(Uri uri, String[] projection, String selection,
                        String[] selectionArgs, String sortOrder) {
        return null;
    }

    @Override
    public int update(Uri uri, ContentValues values, String selection,
                      String[] selectionArgs) {
        return 0;
    }
}