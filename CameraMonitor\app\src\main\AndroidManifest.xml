<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.xiaomi.mico.persistent.monitor">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="com.xiaomi.permission.MICO_GROUP_HOME" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
    <uses-permission android:name="android.permission.REBOOT" />
    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="com.xiaomi.voip.permissions.VOIP" />
    <uses-permission android:name="android.permission.REAL_GET_TASKS" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <permission
        android:name="com.xiaomi.mico.IPC"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.xiaomi.mico.IPC" />

    <application
        android:name=".CameraMonitorApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/MicoActivityTheme">

        <activity
            android:name=".MainActivity"
            android:label="@string/app_name"
            android:launchMode="singleTask"
            android:theme="@style/Theme.CameraMonitor">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <service
            android:name=".CameraMonitorService"
            android:exported="true">
            <intent-filter>
                <action android:name="com.xiaomi.mico.persistent.monitor.START_SERVICE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mico.persistent.monitor.STOP_SERVICE" />
            </intent-filter>
            <intent-filter>
                <action android:name="com.xiaomi.mico.persistent.monitor.CLOSE_SERVICE" />
            </intent-filter>
        </service>
        <service
            android:name=".CheckMonitorRunningService"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <activity
            android:name="com.xiaomi.mico.persistent.voip.MiVoipActivity"
            android:exported="true" />

        <provider
            android:name="com.xiaomi.mico.persistent.spec.SpecActionContentProvider"
            android:authorities="com.xiaomi.mico.provider.video_monitor"
            android:exported="true"
            android:permission="com.xiaomi.mico.IPC" />

        <receiver android:name=".CameraMonitorReceiver">
            <intent-filter>
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <service
            android:name="com.xiaomi.mico.persistent.func.FloatSoundBoxService"
            android:enabled="true"
            android:exported="true"/>
    </application>

</manifest>