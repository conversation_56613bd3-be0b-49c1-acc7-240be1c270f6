package com.xiaomi.camera.monitoring;

import android.graphics.ImageFormat;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CaptureRequest;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Log;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.xiaomi.camera.monitoring.entity.FrameData;
import com.xiaomi.camera.monitoring.utils.ImageUtils;

import java.util.ArrayList;
import java.util.List;

public class CameraVideoManager {

    private final String TAG = "CameraVideoManager";

    private volatile static CameraVideoManager mInstance;

    private EncodeHevc encodeHevc;
    private CameraDevice mCameraDevice;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private CameraCaptureSession mCameraCaptureSession;
    private ImageReader mImageReader;

    public int mVideoWidth;
    public int mVideoHeight;

    public static CameraVideoManager getInstance() {
        if (mInstance == null) {
            synchronized (CameraVideoManager.class) {
                if (mInstance == null) {
                    mInstance = new CameraVideoManager();
                }
            }
        }
        return mInstance;
    }

    public CameraVideoManager() {
    }

    public void init(String encodeType, VideoCameraStreamProducer videoProducer, CameraDevice camera, int videoWidth, int videoHeight) {
        mVideoWidth = videoWidth;
        mVideoHeight = videoHeight;
        encodeHevc = new EncodeHevc(videoProducer);
        encodeHevc.init(encodeType, mVideoWidth, mVideoHeight);

        mCameraDevice = camera;
    }

    Runnable recordRunnable = new Runnable() {
        @Override
        public void run() {
            if (mCameraDevice == null || encodeHevc == null) {
                Log.e(TAG, "Please init first!!!!!!");
                return;
            }
            createCaptureSession(mCameraDevice);
        }

    };

    public synchronized void startRecord() {
        mHandlerThread = new HandlerThread("CameraVideoBackground");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        mBackgroundHandler.post(recordRunnable);
    }

    public synchronized void stopRecord() {
        try {
            if (mImageReader != null) {
                mImageReader.setOnImageAvailableListener(null, null);
            }
            if (mCameraCaptureSession != null) {
                mCameraCaptureSession.stopRepeating();
                mCameraCaptureSession.abortCaptures();
                mCameraCaptureSession.close();
            }
            if (mCameraDevice != null) {
                mCameraDevice.close();
            }
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
            if (encodeHevc != null) {
                encodeHevc.release();
            }
        } catch (Exception e) {
            Log.e(TAG, "destroy thread throw exception:", e);
        } finally {
            mHandlerThread = null;
            mBackgroundHandler = null;
            mCameraCaptureSession = null;
            mCameraDevice = null;
            encodeHevc = null;
            mImageReader = null;
        }
    }

    private void createCaptureSession(CameraDevice camera) {
        mImageReader = ImageReader.newInstance(mVideoWidth, mVideoHeight,
                ImageFormat.YUV_420_888, 2);
        mImageReader.setOnImageAvailableListener(new ImageReader.OnImageAvailableListener() {
            @Override
            public void onImageAvailable(ImageReader reader) {
                Image image = reader.acquireLatestImage();
                if (image == null) {
                    return;
                }
                byte[] dataFromImage = ImageUtils.getDataFromImage(image, 1);
                FrameData frameData = new FrameData(System.currentTimeMillis());
                frameData.dataBytes = dataFromImage;
                frameData.formatType = FrameData.YUV_420_888;

                if (encodeHevc != null) {
                    encodeHevc.encode(frameData);
                }

                image.close();
            }
        }, mBackgroundHandler);

        List<Surface> surfaces = new ArrayList<>();
        surfaces.add(mImageReader.getSurface());

        if (encodeHevc != null) {
            encodeHevc.startEncoder();
        }

        try {
            final CaptureRequest.Builder captureRequest = camera.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            captureRequest.addTarget(mImageReader.getSurface());
            camera.createCaptureSession(surfaces, new CameraCaptureSession.StateCallback() {
                @Override
                public void onConfigured(@NonNull CameraCaptureSession session) {
                    try {
                        mCameraCaptureSession = session;
                        mCameraCaptureSession.setRepeatingRequest(captureRequest.build(), null, null);
                    } catch (Exception e) {
                        Log.e(TAG, "set repeating request throw exception:", e);
                    }
                }

                @Override
                public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                    Log.w(TAG, "create capture session onConfigureFailed.");
                }
            }, null);
        } catch (Exception e) {
            Log.e(TAG, "create capture session throw exception:", e);
        }
    }
}
