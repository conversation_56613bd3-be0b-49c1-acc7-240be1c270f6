#include <jni.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include <assert.h>
#include <pthread.h>
#include <unistd.h>
#include <sys/time.h>
#include <android/log.h>

#include "missJNI.h"
#include "miss/miss.h"
#include "miss/miss_server.h"
#include "miss/miss_porting.h"

#define LOGD(...)  __android_log_print(ANDROID_LOG_DEBUG, "CameraMonitoring JNI ---->> ", __VA_ARGS__)
#define LOGE(...)  __android_log_print(ANDROID_LOG_DEBUG, "CameraMonitoring JNI ---->> ", __VA_ARGS__)

#define RET_SUCC                    (0)
#define RET_FAIL                    (-1)

#define MAX_TUTK_SESSION_NUMBER     (2)
#define MAX_AUDIO_FRAME_LEN         (8 * 1024)
#define MAX_VIDEO_FRAME_LEN         (3 * 640 * 480)
#define MAX_VIDEO_FRAME_LEN_SEND         (307200 + 100 * 1024)

#define MAIN_STREAM 0
#define SUB_STREAM 1

#define MISS_LOG_PATH               "/mnt/sdcard/Android/data/com.xiaomi.mico.persistent.monitor/files/log/miss.log"

JavaVM *gJavaVM;
jobject gJavaObj;

miss_server_config_t g_server_config = {0};
miss_device_info_t g_dev_info = {0};

pthread_mutex_t imi_p2p_thread_lock = PTHREAD_MUTEX_INITIALIZER;

static short aLawDecompressTable[] = {-5504, -5248,
                                      -6016, -5760, -4480, -4224, -4992, -4736, -7552, -7296, -8064,
                                      -7808, -6528, -6272, -7040, -6784, -2752, -2624, -3008, -2880,
                                      -2240, -2112, -2496, -2368, -3776, -3648, -4032, -3904, -3264,
                                      -3136, -3520, -3392, -22016, -20992, -24064, -23040, -17920,
                                      -16896, -19968, -18944, -30208, -29184, -32256, -31232, -26112,
                                      -25088, -28160, -27136, -11008, -10496, -12032, -11520, -8960,
                                      -8448, -9984, -9472, -15104, -14592, -16128, -15616, -13056,
                                      -12544, -14080, -13568, -344, -328, -376, -360, -280, -264, -312,
                                      -296, -472, -456, -504, -488, -408, -392, -440, -424, -88, -72,
                                      -120, -104, -24, -8, -56, -40, -216, -200, -248, -232, -152, -136,
                                      -184, -168, -1376, -1312, -1504, -1440, -1120, -1056, -1248, -1184,
                                      -1888, -1824, -2016, -1952, -1632, -1568, -1760, -1696, -688, -656,
                                      -752, -720, -560, -528, -624, -592, -944, -912, -1008, -976, -816,
                                      -784, -880, -848, 5504, 5248, 6016, 5760, 4480, 4224, 4992, 4736,
                                      7552, 7296, 8064, 7808, 6528, 6272, 7040, 6784, 2752, 2624, 3008,
                                      2880, 2240, 2112, 2496, 2368, 3776, 3648, 4032, 3904, 3264, 3136,
                                      3520, 3392, 22016, 20992, 24064, 23040, 17920, 16896, 19968, 18944,
                                      30208, 29184, 32256, 31232, 26112, 25088, 28160, 27136, 11008,
                                      10496, 12032, 11520, 8960, 8448, 9984, 9472, 15104, 14592, 16128,
                                      15616, 13056, 12544, 14080, 13568, 344, 328, 376, 360, 280, 264,
                                      312, 296, 472, 456, 504, 488, 408, 392, 440, 424, 88, 72, 120, 104,
                                      24, 8, 56, 40, 216, 200, 248, 232, 152, 136, 184, 168, 1376, 1312,
                                      1504, 1440, 1120, 1056, 1248, 1184, 1888, 1824, 2016, 1952, 1632,
                                      1568, 1760, 1696, 688, 656, 752, 720, 560, 528, 624, 592, 944, 912,
                                      1008, 976, 816, 784, 880, 848};

static int cClip = 32635;
static char aLawCompressTable[] = {1, 1, 2, 2, 3, 3, 3,
                                   3, 4, 4, 4, 4, 4, 4, 4, 4, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5,
                                   5, 5, 5, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6,
                                   6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 6, 7, 7, 7, 7, 7, 7, 7, 7, 7,
                                   7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
                                   7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7,
                                   7, 7, 7, 7, 7, 7, 7, 7, 7, 7, 7};

static char g711a_linearToALawSample(short sample)
{
    int sign;
    int exponent;
    int mantissa;
    int s;
    sign = ((~sample) >> 8) & 0x80;
    if (!(sign == 0x80)) {
        sample = (short) -sample;
    }
    if (sample > cClip) {
        sample = cClip;
    }
    if (sample >= 256) {
        exponent = (int) aLawCompressTable[(sample >> 8) & 0x7F];
        mantissa = (sample >> (exponent + 3)) & 0x0F;
        s = (exponent << 4) | mantissa;
    } else {
        s = sample >> 4;
    }
    s ^= (sign ^ 0x55);
    return (char) s;
}

static int g711a_Encode(char* src, int offset, int len, char* res)
{
    int j = offset;
    int count = len / 2;
    short sample = 0;
    int i = 0;
    for (i = 0; i < count; i++) {
        sample = (short) (((src[j++] & 0xff) | (src[j++]) << 8));
        res[i] = g711a_linearToALawSample(sample);
    }
    return count;
}

static int g711a_decode(char* src, int offset, int len, char* res)
{
    int j = 0;
    int tmp_offset = 0;
    int i = 0;
    for (i = 0; i < len; i++) {
        short s = aLawDecompressTable[src[i + tmp_offset] & 0xff];
        res[j++] = (char) s;
        res[j++] = (char) (s >> 8);
    }
    return j;
}

void SetVideoFrameFlag(unsigned int *flags, char watermark, char isplayback) {
    if (1 == isplayback) {
        *flags |= FLAG_STREAM_TYPE_PLAYBACK << 11;
    } else {
        *flags |= FLAG_STREAM_TYPE_LIVE << 11;
    }

    if (1 == watermark) {
        *flags |= FLAG_WATERMARK_TIMESTAMP_EXIST << 13;
    } else {
        *flags |= FLAG_WATERMARK_TIMESTAMP_EXIST << 13;
    }

    *flags |= FLAG_WATERMARK_LOGO_EXIST << 15;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissInit(JNIEnv *env, jclass object, jobject callBack) {
    /* 线程不允许共享env环境变量，但JavaVM指针是整个JVM共用的 */
    jint ret = (*env)->GetJavaVM(env, &gJavaVM);
    if (0 != ret) {
        LOGD("Get JavaVM fail ...");
        return RET_FAIL;
    }

    /* 没有经过NewGlobalRef修饰过的jobject对象会在函数执行完后直接释放掉!!! */
    gJavaObj = (*env)->NewGlobalRef(env, callBack);
    if (NULL == gJavaObj) {
        LOGD("New Global Ref fail");
        return RET_FAIL;
    }

    srand((int) time(0));
    LOGD("native miss init success");
    return RET_SUCC;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissSessionQuery(JNIEnv *env, jclass object, jint MissQueryCmd) {
    LOGD("MissSessionQuery:%d \n", MissQueryCmd);

    int version;
    int ret = miss_session_query(NULL, MISS_QUERY_CMD_LIB_VERSION, (void *) &version);
    if (MISS_NO_ERROR != ret) {
        LOGE("[%s], [%d], miss_session_query get lib version fial, errcode:%d \n", __func__, __LINE__, ret);
        return RET_FAIL;
    }

    char p2p_version[16] = {0};
    snprintf(p2p_version, sizeof(p2p_version) - 1, "%d.%d.%d", (version >> 16 & 0xff), (version >> 8 & 0xff), (version & 0xff));
    LOGD("miss version is %s\n", p2p_version);
    return RET_SUCC;
}

JNIEXPORT jint JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCleanBuffer(JNIEnv *env, jobject thiz, jint session) {
    miss_session_t *miss_session = getP_Session(session);
    int ret = miss_session_query(miss_session, MISS_QUERY_CMD_SEND_CLEAR_BUFFER, NULL);
    LOGE("[%s], [%d], miss_session_query clean buffer:%d \n", __func__, __LINE__, ret);
    return RET_SUCC;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissSetLogPath(JNIEnv *env, jclass object) {
    miss_log_set_level(MISS_LOG_DEBUG);

    FILE *fp;
    fp = fopen(MISS_LOG_PATH, "a+");
    if (NULL == fp) {
        LOGE("[%s], [%d], fail \n", __func__, __LINE__);
    } else {
        fclose(fp);
    }

    int ret = miss_log_set_path(MISS_LOG_PATH);
    if (RET_SUCC != ret) {
        LOGE("[%s], [%d], miss_log_set_path fial, errcode:%d \n", __func__, __LINE__, ret);
        return RET_FAIL;
    }

    return RET_SUCC;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCmdSend
        (JNIEnv *env, jclass object, jint session, jint cmd, jintArray params, jint length, jint code) {
    LOGE("[%s][%d], Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCmdSend cmd is %d, code is %d\n", __func__, __LINE__, cmd, code);
    int ret = -1;
    int miss_code = code;
    miss_session_t *miss_session = getP_Session(session);

    ret = miss_cmd_send(miss_session, cmd, (void*)&miss_code, sizeof(int));
    if (0 != ret) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return RET_FAIL;
    } else {
        LOGE("[%s][%d], success ... ret %d } \n", __func__, __LINE__, ret);
    }

    return RET_SUCC;
}

JNIEXPORT jint JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCmdSendParams(JNIEnv *env, jobject thiz,
                                                                    jint session, jint cmd,
                                                                    jstring params, jint length) {
    LOGE("[%s][%d], Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCmdSend cmd is %d, code is %d\n", __func__, __LINE__, cmd);
    int ret = -1;
    miss_session_t *miss_session = getP_Session(session);
    const char *device_id = (*env)->GetStringUTFChars(env, params, NULL);
    ret = miss_cmd_send(miss_session, cmd, device_id, length);
    if (0 != ret) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return RET_FAIL;
    } else {
        LOGE("[%s][%d], success ... ret %d } \n", __func__, __LINE__, ret);
    }

    return RET_SUCC;
}

JNIEXPORT jint JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissServerInit(JNIEnv *env, jclass object, jobject device_info, jint videoWidth, jint videoHeight) {
    jclass objectClass;
    jfieldID fid;
    jstring jstr;
    const char *device_id;
    const char *device_model;
    const char *device_key;
    const char *device_token;

    /* Step 1: 通过GetObjectClass方法得到这个对象, 即得到java层该对象实例的类引用 */
    objectClass = (*env)->GetObjectClass(env, device_info);

    /************************device_id***************************/
    /* Step 2: 通过GetFieldID方法得到这个对象的属性句柄 */
    fid = (*env)->GetFieldID(env, objectClass, "device_id", "Ljava/lang/String;");
    if (NULL == fid) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    /* Step 3: 读取实例文件 */
    jstr = (*env)->GetObjectField(env, device_info, fid);
    device_id = (*env)->GetStringUTFChars(env, jstr, NULL);
    if (NULL == device_id) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    g_dev_info.did_len = strlen(device_id);
    g_dev_info.did = malloc(g_dev_info.did_len + 1);
    memset(g_dev_info.did, 0x0, g_dev_info.did_len + 1);
    memcpy(g_dev_info.did, device_id, strlen(device_id));
    LOGD("device_id:%s \n", g_dev_info.did);

    /************************device_model***************************/
    /* Step 2: 通过GetFieldID方法得到这个对象的属性句柄 */
    fid = (*env)->GetFieldID(env, objectClass, "device_model", "Ljava/lang/String;");
    if (NULL == fid) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    /* Step 3: 读取实例文件 */
    jstr = (*env)->GetObjectField(env, device_info, fid);
    device_model = (*env)->GetStringUTFChars(env, jstr, NULL);
    if (NULL == device_model) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    g_dev_info.device_model_len = strlen(device_model);
    g_dev_info.model = malloc(g_dev_info.device_model_len + 1);
    memset(g_dev_info.model, 0x0, g_dev_info.device_model_len + 1);
    memcpy(g_dev_info.model, device_model, strlen(device_model));
    LOGD("device_model:%s \n", g_dev_info.model);

    /************************sdk_type***************************/
    g_dev_info.device_sdk_type_len = strlen("device");
    g_dev_info.sdk_type = malloc(g_dev_info.device_sdk_type_len + 1);
    memset(g_dev_info.sdk_type, 0x0, g_dev_info.device_sdk_type_len + 1);
    memcpy(g_dev_info.sdk_type, "device", strlen("device"));
    LOGD("sdk_type:%s \n", g_dev_info.sdk_type);

    /************************device_key***************************/
    /* Step 2: 通过GetFieldID方法得到这个对象的属性句柄 */
    fid = (*env)->GetFieldID(env, objectClass, "device_key", "Ljava/lang/String;");
    if (NULL == fid) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    /* Step 3: 读取实例文件 */
    jstr = (*env)->GetObjectField(env, device_info, fid);
    device_key = (*env)->GetStringUTFChars(env, jstr, NULL);
    if (NULL == device_key) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    g_server_config.device_key_len = strlen(device_key);
    g_server_config.device_key = malloc(g_server_config.device_key_len + 1);
    memset(g_server_config.device_key, 0x0, g_server_config.device_key_len + 1);
    memcpy(g_server_config.device_key, device_key, strlen(device_key));
    LOGD("device_key:%s \n", g_server_config.device_key);

    /************************device_token***************************/
    /* Step 2: 通过GetFieldID方法得到这个对象的属性句柄 */
    fid = (*env)->GetFieldID(env, objectClass, "miio_token", "Ljava/lang/String;");
    if (NULL == fid) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    /* Step 3: 读取实例文件 */
    jstr = (*env)->GetObjectField(env, device_info, fid);
    device_token = (*env)->GetStringUTFChars(env, jstr, NULL);
    if (NULL == device_token) {
        LOGE("[%s][%d], fail ... \n", __func__, __LINE__);
        return -1;
    }

    g_server_config.max_session_num = MAX_TUTK_SESSION_NUMBER;
    g_server_config.max_video_recv_size = MAX_VIDEO_FRAME_LEN;
    g_server_config.max_video_send_size = MAX_VIDEO_FRAME_LEN_SEND;
    g_server_config.max_audio_recv_size = MAX_AUDIO_FRAME_LEN;
    g_server_config.max_audio_send_size = MAX_AUDIO_FRAME_LEN;
    g_server_config.device_token_len = strlen(device_token);
    g_server_config.device_token = malloc(g_server_config.device_token_len + 1);
    memset(g_server_config.device_token, 0x0, g_server_config.device_token_len + 1);
    memcpy(g_server_config.device_token, device_token, strlen(device_token));
    LOGD("device_token:%s \n", g_server_config.device_token);

    int ret = miss_server_init(&g_dev_info, &g_server_config);
    if (MISS_NO_ERROR != ret) {
        LOGE("[%s], [%d], miss_server_init fail, errcode:%d \n", __func__, __LINE__, ret);
        return -1;
    } else {
        LOGE("[%s], [%d], miss_server_init success \n", __func__, __LINE__);
    }

    return 0;
}

char *Jstring2CStr(JNIEnv *env, jstring jstr) {
    char *rtn = NULL;
    jclass clsstring = (*env)->FindClass(env, "java/lang/String");
    jstring strencode = (*env)->NewStringUTF(env, "GB2312");
    jmethodID mid = (*env)->GetMethodID(env, clsstring, "getBytes", "(Ljava/lang/String;)[B");
    jbyteArray barr = (jbyteArray) (*env)->CallObjectMethod(env, jstr, mid, strencode); // String .getByte("GB2312");
    jsize alen = (*env)->GetArrayLength(env, barr);
    jbyte *ba = (*env)->GetByteArrayElements(env, barr, JNI_FALSE);
    if (alen > 0) {
        rtn = (char *) malloc(alen + 1); //"\0"
        memcpy(rtn, ba, alen);
        rtn[alen] = '\0';
    }
    (*env)->ReleaseByteArrayElements(env, barr, ba, 0); //释放内存
    return rtn;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissRpcProcess(JNIEnv *env, jclass object, jint rpc_id,
                                                                 jstring msg, jint length) {
    LOGD("[%s], [%d] ... \n", __func__, __LINE__);

    char *str = NULL;
    str = Jstring2CStr(env, msg);
    LOGD("msg:|%s|", str);

    void *p_rpcid = getP_Rpc_id(rpc_id);
    unsigned int len = length;

    LOGD("p_rpcid:%p ", p_rpcid);
    LOGD("rpc_id:0x%x ", rpc_id);

    pthread_mutex_lock(&imi_p2p_thread_lock);
    int ret = miss_rpc_process(p_rpcid, str, len);
    LOGD("after miss_rpc_process");
    pthread_mutex_unlock(&imi_p2p_thread_lock);

    if (MISS_NO_ERROR != ret) {
        LOGE("[%s], [%d] ..., code:%d \n", __func__, __LINE__, ret);
        miss_server_finish();
        return -1;
    }

    return 0;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissVideoSend
        (JNIEnv *Env, jclass object, jint session, jbyteArray data, jint length, jboolean isKeyframe, jlong seq, jlong timestamp, jint codecId, jint videoChn) {

    miss_session_t *miss_session = getP_Session(session);
    miss_frame_header_t frameInfo = {0};
    jbyte *nv21 = (*Env)->GetByteArrayElements(Env, data, NULL);
    unsigned int flags = 0;
    int ret = -1;

    if (isKeyframe) {
        flags |= FLAG_FRAME_TYPE_IFRAME << 0;
    } else {
        flags |= FLAG_FRAME_TYPE_PBFRAME << 0;
    }

    if (MAIN_STREAM == videoChn) {
        flags |= FLAG_RESOLUTION_USER_DEFINE << 17;
    } else {
        flags |= FLAG_RESOLUTION_VIDEO_480P << 17;
    }
    SetVideoFrameFlag(&flags, 1, 0);

    if (codecId == 1) {
        frameInfo.codec_id = MISS_CODEC_VIDEO_HEVC;
    } else if (codecId == 2) {
        frameInfo.codec_id = MISS_CODEC_VIDEO_H264;
    } else {
        LOGE("unKnow codecId !!!!!!!!");
        return ret;
    }
    frameInfo.sequence = (uint32_t) (seq % USHRT_MAX);
    frameInfo.timestamp = (uint64_t) timestamp;
    frameInfo.timestamp_s = (uint32_t) time(NULL);
    frameInfo.length = (uint32_t) length;
    frameInfo.flags = flags;
    if (MAIN_STREAM == videoChn) {
        frameInfo.reserve = 0x06820B90;
    }

    if (miss_session) {
        ret = miss_video_send(miss_session, &frameInfo, nv21);
        if (0 != ret) {
            LOGE("avSendFrameData Error:%d ", ret);
        }
    }

    (*Env)->ReleaseByteArrayElements(Env, data, nv21, 0);
    return ret;
}

JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissAudioSend
        (JNIEnv *Env, jclass object, jint session, jbyteArray data, jint length, jlong seq, jlong timestamp) {

    miss_session_t *miss_session = getP_Session(session);
    jbyte *nv21 = (*Env)->GetByteArrayElements(Env, data, NULL);

    miss_frame_header_t frameInfo = {0};

    unsigned int audio_sample = FLAG_AUDIO_SAMPLE_16K;
    unsigned int audio_channel_num = FLAG_AUDIO_CHANNEL_MONO;
    int ret = -1;

    frameInfo.flags = audio_sample << 3 | FLAG_AUDIO_DATABITS_16 << 7 | audio_channel_num << 9 | FLAG_RESOLUTION_AUDIO_DEFAULT << 17;
    frameInfo.codec_id = MISS_CODEC_AUDIO_OPUS;
    frameInfo.sequence = (uint32_t) (seq % USHRT_MAX);
    frameInfo.timestamp = (uint64_t) timestamp;
    frameInfo.timestamp_s = (uint32_t) time(NULL);
    frameInfo.length = (uint32_t) length;

    if (miss_session) {
        ret = miss_audio_send(miss_session, &frameInfo, nv21);
        if (0 != ret) {
            LOGE("avSendFrameData audio Error:%d ", ret);
        }

    } else {
        LOGE("avSendFrameData audio miss_session is null ");
    }

    (*Env)->ReleaseByteArrayElements(Env, data, nv21, 0);
    return ret;
}

JNIEXPORT void JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissCloseServerSession(JNIEnv *Env, jclass object, jint session) {
    miss_session_t *miss_session = getP_Session(session);

    pthread_mutex_lock(&imi_p2p_thread_lock);
    miss_server_session_close(miss_session);
    pthread_mutex_unlock(&imi_p2p_thread_lock);
}

JNIEXPORT void JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_MissFinishServer(JNIEnv *Env, jclass object) {
    pthread_mutex_lock(&imi_p2p_thread_lock);
    miss_server_finish();
    clearP_Session();
    pthread_mutex_unlock(&imi_p2p_thread_lock);
}

/*-----test-----*/
JNIEXPORT int JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_test(JNIEnv *env, jclass object, jbyteArray b1, jbyteArray b2) {
    jbyte *dataSrc = (*env)->GetByteArrayElements(env, b1, NULL);
    jbyte *datab2 = (*env)->GetByteArrayElements(env, b2, NULL);


    int i = 0;
    for (i = 0; i < 12; i++) {
        LOGD("b[%d]:%d \n", i, *(dataSrc + i));
//        LOGD("test:%d \n", (char *)dataSrc[i]);
    }

    for (i = 0; i < 12; i++) {
        *(datab2 + i) = 12 - i;
    }

    (*env)->ReleaseByteArrayElements(env, b1, dataSrc, 0);
    (*env)->ReleaseByteArrayElements(env, b2, datab2, 0);
    return RET_SUCC;
}

JNIEXPORT jbyteArray JNICALL
Java_com_xiaomi_camera_monitoring_miss_MissClient_PcmToG711A(JNIEnv *env, jclass clazz, jbyteArray pcmData, jint length) {
    jbyte *pcm_data = (*env)->GetByteArrayElements(env, pcmData, NULL);
    jbyteArray g711aData = (*env)->NewByteArray(env, length / 2);
    jbyte *g711a_data = (*env)->GetByteArrayElements(env, g711aData, NULL);
    g711a_Encode(pcm_data, 0, length, g711a_data);
    (*env)->ReleaseByteArrayElements(env, pcmData, pcm_data, 0);
    (*env)->ReleaseByteArrayElements(env, g711aData, g711a_data, 0);
    return g711aData;
}