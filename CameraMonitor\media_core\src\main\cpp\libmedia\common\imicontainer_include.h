#ifndef __IMICONTAINER_INCLUDE_H__
#define __IMICONTAINER_INCLUDE_H__

#include <memory.h>
#include "imimedia_include.h"

#define IMICONTAINER_HEADER 'CMCM'
#define IMICONTAINER_PROTOCOL 0x01
#define IMICONTAINER_FRAME 'CMFR'

#pragma pack(push,1)

typedef struct imicontainer_header_s {
	int header;
	short protocol;
	unsigned char resv[6];
} imicontainer_header_s, *imicontainer_header_t;

typedef struct imicontainer_frame_s {
	int header;
	unsigned char codec;
	unsigned char type;
	unsigned short vwidth_or_achannel;
	unsigned short vheight_or_asample;
	unsigned short vfps_or_abit;
	unsigned int timestamp_ms;
	unsigned int timestamp_s;
	unsigned int index;
	unsigned int framelen;
	unsigned int extra_len;
	unsigned char resv[4];
	//char extra[extra_len];
	//char framebuff[framelen];
} imicontainer_frame_s, *imicontainer_frame_t;

static imicontainer_header_t imicontainer_header_make_in_params(container_format_id container) {
	imicontainer_header_t header = NULL;
	int header_len = sizeof(imicontainer_header_s);
	header = (imicontainer_header_t)malloc(header_len);
	if (header == NULL) {
		return NULL;
	}
	memset(header, 0, header_len);
	header->header = IMICONTAINER_HEADER;
	header->protocol = IMICONTAINER_PROTOCOL;
	return header;
}

static imicontainer_frame_t imicontainer_frame_make_in_params(unsigned int data_len,
	video_codec_id video,
	audio_codec_id audio,
	frame_type_id frametype,
	unsigned int vwidth_or_achannel,
	unsigned int vheight_or_asamplerate,
	unsigned int vfps_or_abit,
	unsigned int timestamp_ms,
	unsigned int timestamp_s,
	unsigned int index) {
	imicontainer_frame_t frame = NULL;
	int frame_len = sizeof(imicontainer_frame_s);
	frame = (imicontainer_frame_t)malloc(frame_len);
	if (frame == NULL) {
		return NULL;
	}
	memset(frame, 0, frame_len);
	frame->header = IMICONTAINER_FRAME;
	switch (frametype)
	{
	case frame_type_i:
	case frame_type_p:
	case frame_type_b:
	{
		frame->codec = (unsigned char)video;
	}
	break;
	case frame_type_audio:
	{
		frame->codec = (unsigned char)audio;
	}
	break;
	default:
		break;
	}
	frame->type = (unsigned char)frametype;
	frame->vwidth_or_achannel = (unsigned short)vwidth_or_achannel;
	frame->vheight_or_asample = (unsigned short)vheight_or_asamplerate;88
	frame->vfps_or_abit = (unsigned short)vfps_or_abit;
	frame->timestamp_ms = timestamp_ms;
	frame->timestamp_s = timestamp_s;
	frame->index = index;
	frame->framelen = data_len;
	return frame;
}

static imicontainer_header_t imicontainer_header_make_in_buffer(void* buffer) {
	imicontainer_header_t header = NULL;
	int header_len = sizeof(imicontainer_header_s);
	header = (imicontainer_header_t)malloc(header_len);
	if (header == NULL) {
		return NULL;
	}
	memcpy(header, buffer, header_len);
	return header;
}

static imicontainer_frame_t imicontainer_frame_make_in_buffer(void* buffer) {
	imicontainer_frame_t frame = NULL;
	int frame_len = sizeof(imicontainer_frame_s);
	frame = (imicontainer_frame_t)malloc(frame_len);
	if (frame == NULL) {
		return NULL;
	}
	memcpy(frame, buffer, frame_len);
	return frame;
}

#pragma pack(pop)

#endif // __IMICONTAINER_INCLUDE_H__
