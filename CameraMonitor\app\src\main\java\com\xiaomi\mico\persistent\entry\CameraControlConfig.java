package com.xiaomi.mico.persistent.entry;

public class CameraControlConfig {
    private boolean switchStatus = true; //休眠开关
    private int nightShot = 2; //夜视功能
    private boolean timeWatermark = true; //时间水印
    private boolean ptzCamera  = true; //云台控制
    private boolean imageDistortionCorrection  = true; //畸变矫正
    private boolean glimmer = true; // 微光全彩

    public boolean isSwitchStatus() {
        return switchStatus;
    }

    public void setSwitchStatus(boolean switchStatus) {
        this.switchStatus = switchStatus;
    }

    public int getNightShot() {
        return nightShot;
    }

    public void setNightShot(int nightShot) {
        this.nightShot = nightShot;
    }

    public boolean isTimeWatermark() {
        return timeWatermark;
    }

    public void setTimeWatermark(boolean timeWatermark) {
        this.timeWatermark = timeWatermark;
    }

    public boolean isPtzCamera() {
        return ptzCamera;
    }

    public void setPtzCamera(boolean ptzCamera) {
        this.ptzCamera = ptzCamera;
    }

    public boolean isImageDistortionCorrection() {
        return imageDistortionCorrection;
    }

    public void setImageDistortionCorrection(boolean imageDistortionCorrection) {
        this.imageDistortionCorrection = imageDistortionCorrection;
    }

    public boolean isGlimmer() {
        return glimmer;
    }

    public void setGlimmer(boolean glimmer) {
        this.glimmer = glimmer;
    }

    @Override
    public String toString() {
        return "CameraControlConfig{" +
                "switchStatus=" + switchStatus +
                ", nightShot=" + nightShot +
                ", timeWatermark=" + timeWatermark +
                ", ptzCamera=" + ptzCamera +
                ", imageDistortionCorrection=" + imageDistortionCorrection +
                ", glimmer=" + glimmer +
                '}';
    }
}
