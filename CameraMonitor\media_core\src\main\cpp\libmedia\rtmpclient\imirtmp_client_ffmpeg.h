#pragma once

#include "imimedia_include.h"

typedef struct imirtmp_client_ffmpeg_info_s *imirtmp_client_ffmpeg_info_t;

void imirtmp_client_init_ffmpeg();

int imirtmp_client_open(const char* url,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	/*out*/imirtmp_client_ffmpeg_info_t* handle);

int imirtmp_client_write_video_frame(imirtmp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp);

int imirtmp_client_write_audio_frame(imirtmp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp);

int imirtmp_client_close(imirtmp_client_ffmpeg_info_t handle);