package com.xiaomi.mico.persistent.cloud;

public class CloudErrorCode {
    //400/401等开头的，是请求本身错误，参数格式错误等，客户端不可重试
    //400219对于⾮会员当天上传次数超限，后续上传需取消，第二天才可继续上传
    //400219对于⾮会员当天上传次数超限，后续上传需取消，第二天才可继续上传
    //400219对于⾮会员当天上传次数超限，后续上传需取消，第二天才可继续上传
    //400或401或4XX的错误码⼀定不要重复上传
    //400或401或4XX的错误码⼀定不要重复上传
    //400或401或4XX的错误码⼀定不要重复上传
    //400206视频/图⽚签名或者加密错误，或者视频格式错误⼀定不可重复上传
    //400221 上传⽂件传⼊vip状态和云端不⼀致
    //返回值中code为500等5开头的错误，是服务端错误，客户端可延时重试
    public static final int ERROR_CODE_400221 = 400221;
    //==========================自定义错误===========================
    public static final int ERROR_CODE_PRE_UPLOAD_ERROR = 1000000; //获取FieldId异常
    public static final int ERROR_CODE_FILE_UPLOAD_ERROR = 1000001; //文件上传异常
    public static final int ERROR_CODE_META_UPLOAD_ERROR = 1000002; //Meta上传异常
}
