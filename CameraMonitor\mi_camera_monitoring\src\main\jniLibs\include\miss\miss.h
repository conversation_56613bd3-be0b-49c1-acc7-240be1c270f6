/*
 * Copyright (c) 2019 Xiaomi. All Rights Reserved.
 */

/**
 * @file miss.h
 *
 * MISS (MI IoT Streaming SDK)
 *
 * The role of this SDK is trying to hide all the complexity of the streaming
 * stuff from the application developers. This includes but not limited to:
 *
 *   * Initiate the tunnel with the target, weather in P2P or Relay;
 *   * Transfer data contents, usually this is media stream, through the
 *     tunnel we created above;
 *   * Signalling through the tunnel we created above to control the media
 *     stream;
 *   * Tunnel information exchange encryption, this includes stream
 *     encryption ioctl commands encryption;
 *   * QoS and quality control;
 *
 * By adopting this SDK in your streaming application development, you do not
 * need to care details of how your contents are exchanged with the target,
 * you just focus on the functionality implementation of your product.
 * Hopefully this will easy the overall task, and eventually shorten the time
 * to market of your products.
 *
 * This SDK is brought to you by MI IoT edge computing and IPC team.
 * Any questions and suggestions, please contact them.
 *
 * This file contains functions MISS SDK provided for applications to call.
 */

#ifndef MISS_H
#define MISS_H

#define MISS_VERSION_MAJOR 3
#define MISS_VERSION_MINOR 2
#define MISS_VERSION_MICRO 12
#define MISS_VERSION_TMP 1

#include <stdint.h>

typedef struct miss_session_s miss_session_t;

/**
 * miss_encrypt_type_e - The enum of MISS encrypt type
 */
typedef enum {
    MISS_INTERNAL_ENCRYPT   = 0x0, // CHACHA20_XOR
    MISS_AES_128_CBC        = 0x1, // use miss_encrypt_data/miss_decrypt_data api to en/de data
    MISS_AES_128_ECB        = 0x2, // has been deprecated, recommand use CBC or CTR
    MISS_AES_128_CTR        = 0x3, // use miss_encrypt_data/miss_decrypt_data api to en/de data
} miss_encrypt_type_e;

/**
 * miss_init_mode_e - The enum of MISS init mode type
 */
typedef enum {
    MISS_NORMAL_MODE   = 0x0,   // normal camera device
    MISS_WAKEUP_MODE   = 0x1,   // remote wakeup for low power camera device
} miss_init_mode_e;

typedef enum {
    MISS_SUPPORT_HWYK  = 0x1,   // support hwyk
    MISS_SUPPORT_PLACEHOLDER1 = 0x2,   // support ..
    MISS_SUPPORT_PLACEHOLDER2 = 0x4,   // support ..
} miss_support_type_e;


/**
 * miss_error_e - MISS error
 */
typedef enum {
    MISS_NO_ERROR, // 0
    MISS_ERR_CLOSE_BY_LOCAL,
    MISS_ERR_CLOSE_BY_REMOTE,
    MISS_ERR_AUTHORIZED,
    MISS_ERR_CLOSE_BY_TIMEOUT,
    MISS_ERR_NOT_SUPPORT_VENDOR,// 5
    MISS_ERR_ALREADY_INITIALIZED,
    MISS_ERR_NOT_INITIALIZED,
    MISS_ERR_ABORTED,
    MISS_ERR_CREATE_MUTEX,
    MISS_ERR_CREATE_THREAD, // 10
    MISS_ERR_NOT_ENOUGH_MEMORY,
    MISS_ERR_CREATE_SOCKET,
    MISS_ERR_SOCKET_OPTIONS,
    MISS_ERR_SOCKET_BIND,
    MISS_ERR_MAX_SESSION, // 15
    MISS_ERR_SESSION_HANDLE,
    MISS_ERR_TIMOUT,
    MISS_ERR_RPC_SEND,
    MISS_ERR_RPC_JSON_PARSE,
    MISS_ERR_DISCONNECT_TIMOUT, // 20
    MISS_ERR_CREATE_CHANNEL,
    MISS_ERR_INVALID_ARG,
    MISS_ERR_CLIENT_NO_SUPPORT,
    MISS_ERR_MAX_CHANNEL,
    MISS_ERR_NO_RESPONSE, // 25
    MISS_ERR_NO_BUFFER,
    MISS_ERR_CHANNEL_EXCEED_MAX_SIZE,
    MISS_ERR_FUNCTION_ALREADY_CALLED,
    MISS_ERR_FRAME_DECRYPTO,
    MISS_ERR_FRAME_ENCTYPTO, // 30
    MISS_ERR_FUNCTION_DISABLE,
    MISS_ERR_SLEEPING,
    MISS_ERR_OFFLINE,
    MISS_ERR_CONNECT_SERVER,
    MISS_ERR_CHECK_VENDOR,   // 35
    MISS_ERROR_DEVICE_SESSION_MAX,
    MISS_ERROR_REPEATED_SESSION,
    MISS_ERROR_CLOSE_PRELINK_SESSION,
    MISS_ERROR_INIT,
    MISS_ERROR_THREAD_SATRT_FAIL, //40
    MISS_ERROR_TIMER_POOL_EMPTY
} miss_error_e;

/**
 * miss_log_level_e - MISS Log Levels
 */
typedef enum {
    MISS_LOG_VERBOSE,
    MISS_LOG_DEBUG,
    MISS_LOG_INFO,
    MISS_LOG_WARNING,
    MISS_LOG_ERROR,
    MISS_LOG_LEVEL_MAX = MISS_LOG_ERROR
} miss_log_level_e;

/**
 * miss_frame_header_t - MISS video/audio frame header
 */
typedef struct miss_frame_header_s {
    uint32_t length;	/**< data length after this frame header */
    uint32_t codec_id;	/**< one of ::miss_codec_e */
    uint32_t sequence;	/**< frame sequence */
    uint32_t flags;     // 详见下边的 miss_frame_flags_t flag_* 枚举, https://xiaomi.f.mioffice.cn/docs/dock48hDJKYtRiBkXYTKVSqD8hh
    uint64_t timestamp;
    uint32_t timestamp_s;
    uint32_t reserve;   /**< flags = FLAG_RESOLUTION_USER_DEFINE,0~15bit: width,16~31: height */
} miss_frame_header_t;

/**
 * miss_device_info_t - MISS device info
 */
typedef struct miss_device_info_s {
    char* did;                   /**< device id */
    uint32_t did_len;            /**< device id length */
    char* model;                 /**< device model */
    uint32_t device_model_len;   /**< device model length */
    char* sdk_type;              /**< device sdk type :"android", "ios", "device", "pc"*/
    uint32_t device_sdk_type_len;/**< device sdk type length*/
#ifdef BUILT_FOR_IOS_PLATFORM
    int obsolete_tutk;           /**< old tutk device without miss */
    int fixed_rdt;               /**< old tutk device without miss use fixed rdt or not(free channel) */
#endif
} miss_device_info_t;

/**
 * miss_server_config_t - MISS server configs
 */
typedef struct miss_server_config_s {
    uint32_t length;
    uint32_t max_session_num;       /**< max session this server supports */
    char* device_key;               /**< device key */
    uint32_t device_key_len;        /**< device key length */
    char* device_token;             /**< device token */
    uint32_t device_token_len;      /**< device token length */
    int max_video_send_size;        /**< max video send size, <0 for disable send video, 0 for default(512k), others for real size */
    int max_audio_send_size;        /**< max audio send size, <0 for disable send audio, 0 for default(32k), others for real size */
    int max_video_recv_size;        /**< max video recv size, <0 for disable recv video, 0 for default(512k), others for real size */
    int max_audio_recv_size;        /**< max audio recv size, <0 for disable recv audio, 0 for default(32k), others for real size */
    miss_encrypt_type_e encrypt_type;
    int multi_connect_flag;         /**< = 0: one client one session, = 1: one client multi session  */
    miss_init_mode_e init_mode;     /**< config miss init model for different device*/
    int support_type;               /**< see miss_support_type_e*/
    char* info;                    /**< device other info, json string*/
} miss_server_config_t;

/**
 * miss_client_config_t - MISS client configs
 */
typedef struct miss_client_config_s {
    uint32_t length;
    uint32_t max_frame_size;  /**< max single frame size, 0 for default(255k)*/
    uint32_t max_resend_size; /**< max resend buffer size, 0 for default(256k)*/
    int max_video_send_size;  /**< max video send size, <0 for disable send video, 0 for default(255k), others for real size */
    int max_audio_send_size;  /**< max audio send size, <0 for disable send audio, 0 for default(8k), others for real size */
    int max_video_recv_size;  /**< max video recv size, <0 for disable recv video, 0 for default(255k), others for real size */
    int max_audio_recv_size;  /**< max audio recv size, <0 for disable recv audio, 0 for default(8k), others for real size */
    int external_flag;        /**< can be defined for special purpose by yourself, 0 for device always on line, 1 for device need wakeup*/
} miss_client_config_t;


/**
 * miss_cmd_e - The enum of MISS commands
 * https://xiaomi.f.mioffice.cn/docx/doxk479hNRevyNGDe2XFwpwq9md
 * S:server, C:Client
 */
typedef enum {
    MISS_CMD_AUTHENTICATE_REQ   = 0x100,        /**< 0x100/256, C->S, send auth info */
    MISS_CMD_AUTHENTICATE_RESP,                 /**< 0x101/257, S->C, resp auth result */
    MISS_CMD_VIDEO_START,                       /**< 0x102/258, C->S, video start */
    MISS_CMD_VIDEO_STOP,                        /**< 0x103/259, C->S, video stop */
    MISS_CMD_AUDIO_START,                       /**< 0x104/260, C->S, audio start */
    MISS_CMD_AUDIO_STOP,                        /**< 0x105/261, C->S, audio stop */
    MISS_CMD_SPEAKER_START_REQ,                 /**< 0x106/262, C->S, speaker start req */
    MISS_CMD_SPEAKER_START_RESP,                /**< 0x107/263, C->S, speaker start resp */
    MISS_CMD_SPEAKER_STOP,                      /**< 0x108/264, C->S, speaker stop */
    MISS_CMD_STREAM_CTRL_REQ,                   /**< 0x109/265, C->S, video quality req */
    MISS_CMD_STREAM_CTRL_RESP,                  /**< 0x10a/266, S->C, video quality response */
    MISS_CMD_GET_AUDIO_FORMAT_REQ,              /**< 0x10b/267, C->S, get audio format */
    MISS_CMD_GET_AUDIO_FORMAT_RESP,             /**< 0x10c/268, S->C, audio format response */
    MISS_CMD_PLAYBACK_REQ,                      /**< 0x10d/269, C->S, playback request */
    MISS_CMD_PLAYBACK_RESP,                     /**< 0x10e/270, S->C, playback response */
    MISS_CMD_PLAYBACK_SET_SPEED,                /**< 0x10f/271, C->S, playback speed */
    MISS_CMD_DEVINFO_REQ,                       /**< 0x110/272, C->S, device info request */
    MISS_CMD_DEVINFO_RESP,                      /**< 0x111/273, S->C, device info response */
    MISS_CMD_MOTOR_REQ,                         /**< 0x112/274, C->S, device motor control */
    MISS_CMD_MOTOR_RESP,                        /**< 0x113/275, S->C, device motor control response */
    MISS_CMD_NETWORK_STATUS,                    /**< 0x114/276, S->C, device network status */
    MISS_CMD_SERVICE_ERROR_NOTIFY,              /**< 0x115/277, S->C, device session error notify */
    MISS_CMD_LOCK_REQ,                          /**< 0x116/278, C->S, lock request */
    MISS_CMD_LOCK_RESP,                         /**< 0x117/279, S->C, lock response */
    MISS_CMD_VIDEOCALL_START_REQ,               /**< 0x118/280, C->S, videocall start req */
    MISS_CMD_VIDEOCALL_START_RESP,              /**< 0x119/281, C->S, videocall start resp */
    MISS_CMD_VIDEOCALL_STOP,                    /**< 0x120/282, C->S, videocall stop */

    MISS_CMD_CRUISE_STATE_REQ = 0x200,          /**< 0x200/512, C->S, 查询巡航状态 */
    MISS_CMD_CRUISE_STATE_RESP = 0x201,         /**< 0x201/513, S->C, 响应查询巡航状态 */
    MISS_CMD_CALL_STATUS_RESP = 0x301,          /**< 0x301/769, S->C, 呼叫响应 */

    MISS_CMD_MAX = 0x1001
} miss_cmd_e;

/**
 * flag_frame_type_e - frame type flags
 *
 * flags:0~2 bit
 */
typedef enum {
    FLAG_FRAME_TYPE_PBFRAME, /**< A/V P/B frame */
    FLAG_FRAME_TYPE_IFRAME,	 /**< A/V I frame */
    FLAG_FRAME_TYPE_MD,	 /**< For motion detection */
    FLAG_FRAME_TYPE_IO,	 /**< For Alarm IO detection */
} flag_frame_type_e;


/**
 * flag_audio_samplerate_e - audio samplerate flags
 *
 * flags:3~6 bit
 */
typedef enum {
    FLAG_AUDIO_SAMPLE_8K,
    FLAG_AUDIO_SAMPLE_11K,
    FLAG_AUDIO_SAMPLE_12K,
    FLAG_AUDIO_SAMPLE_16K,
    FLAG_AUDIO_SAMPLE_22K,
    FLAG_AUDIO_SAMPLE_24K,
    FLAG_AUDIO_SAMPLE_32K,
    FLAG_AUDIO_SAMPLE_44K,
    FLAG_AUDIO_SAMPLE_48K,
} flag_audio_samplerate_e;


/**
 * flag_audio_databits_e - audio databits flags
 *
 * flags:7~8 bit
 */
typedef enum {
    FLAG_AUDIO_DATABITS_8,
    FLAG_AUDIO_DATABITS_16,
} flag_audio_databits_e;


/**
 * flag_audio_channel_e - audio channel flags
 *
 * flags:9~10 bit
 */
typedef enum {
    FLAG_AUDIO_CHANNEL_MONO,
    FLAG_AUDIO_CHANNEL_STERO,
} flag_audio_channel_e;


/**
 * flag_stream_type_e - stream type flags
 *
 * flags:11~12 bit
 */
typedef enum {
    FLAG_STREAM_TYPE_LIVE,		    /**< live stream */
    FLAG_STREAM_TYPE_PLAYBACK,	    /**< playback stream */
} flag_stream_type_e;


/**
 * flag_watermark_timestamp_e - watermark timestamp flags
 *
 * flags:13~14 bit
 */
typedef enum {
    FLAG_WATERMARK_TIMESTAMP_NOT_EXIST, /**< have no water mark (watermark is timestamp) */
    FLAG_WATERMARK_TIMESTAMP_EXIST,	/**< have water mark (watermark is timestamp) */
} flag_watermark_timestamp_e;


/**
 * flag_watermark_logo_e - watermark logo flags
 *
 * flags:15~16 bit
 */
typedef enum {
    FLAG_WATERMARK_LOGO_NOT_EXIST, /**< have no water mark (watermark is logo) */
    FLAG_WATERMARK_LOGO_EXIST, /**< have water mark (watermark is logo) */
} flag_watermark_logo_e;


/**
 * flag_data_resolution_e - data resolution flags
 *
 * flags:17~20 bit
 */

typedef enum {
    FLAG_RESOLUTION_AUDIO_DEFAULT   = 0x00,		/**< audio */
    FLAG_RESOLUTION_VIDEO_1080P     = 0x01,		/**< 1920*1080 */
    FLAG_RESOLUTION_VIDEO_720P      = 0x02,		/**< 1280*720 */
    FLAG_RESOLUTION_VIDEO_360P      = 0x03,		/**< 640*360 */
    FLAG_RESOLUTION_VIDEO_1296P     = 0x04,		/**< 2304*1296 */
    FLAG_RESOLUTION_VIDEO_480P      = 0x05,		/**< 864*480 */
    FLAG_RESOLUTION_VIDEO_1080x1920 = 0x06,     /**< 1080*1920 */
    FLAG_RESOLUTION_VIDEO_1440P     = 0x07,     /**< 2560*1440 */
    FLAG_RESOLUTION_VIDEO_720x1280  = 0x08,     /**< 720*1280 */
    FLAG_RESOLUTION_VIDEO_1088x1080 = 0x09,     /**< 1088*1080 */
    FLAG_RESOLUTION_VIDEO_1536x1536 = 0xa,      /**< 1536*1536 */
    FLAG_RESOLUTION_VIDEO_480x480   = 0xb,      /**< 480*480 */
    FLAG_RESOLUTION_USER_DEFINE     = 0xf,      /**< use miss_frame_header_t reserve to define video resolution,0~15bit:width,16~31:height */
} flag_data_resolution_e;

// union miss_frame_flags_t flag = {0};
// flag.flags.DataBits = FLAG_AUDIO_DATABITS_16;
// printf("%ld, %d\n", sizeof(flag), flag.flag_val == FLAG_AUDIO_DATABITS_16 << 7);  // 4, 1
#pragma pack(1)  // 1字节对齐, 默认是4字节对齐，小端产品有效
union miss_frame_flags_t {
    uint32_t flag_val;
    struct {
        flag_frame_type_e FrameType : 3;                        // 0-2
        flag_audio_samplerate_e SampleRate : 4;                 // 3-6
        flag_audio_databits_e DataBits : 2;                     // 7-8
        flag_audio_channel_e Channel : 2;                       // 9-10
        flag_stream_type_e StreamType : 2;                      // 11-12
        flag_watermark_timestamp_e WaterMarkTimestamp : 2;      // 13-14
        flag_watermark_logo_e WaterMarkLogo : 2;                // 15-16
        flag_data_resolution_e VideoResolution : 4;             // 17-20
        char video_channel : 6;                                 // 21-26
        char revert2 : 5;                                       // 27-31
    } flags;
};
#pragma pack()

/**
 * miss_codec_e - The enum of MISS supported codecs
 */
typedef enum {
    MISS_CODEC_UNKNOWN,
    MISS_CODEC_VIDEO_MPEG4,
    MISS_CODEC_VIDEO_MJPEG,
    MISS_CODEC_VIDEO_H263,
    MISS_CODEC_VIDEO_H264,
    MISS_CODEC_VIDEO_HEVC,
    MISS_CODEC_VIDEO_H265 = MISS_CODEC_VIDEO_HEVC,
    MISS_CODEC_VIDEO_CUSTOM,
    MISS_CODEC_AUDIO_PCM = 1024,
    MISS_CODEC_AUDIO_ADPCM,
    MISS_CODEC_AUDIO_G711U,
    MISS_CODEC_AUDIO_G711A,
    MISS_CODEC_AUDIO_G726,
    MISS_CODEC_AUDIO_MP3,
    MISS_CODEC_AUDIO_AAC,
    MISS_CODEC_AUDIO_SPEEX,
    MISS_CODEC_AUDIO_OPUS,
    MISS_CODEC_AUDIO_CUSTOM
} miss_codec_e;

/**
 * miss_query_cmd_e - MISS local status query commands.
 */

typedef enum {
    MISS_QUERY_CMD_LIB_VERSION,
    MISS_QUERY_CMD_LIB_VERSION_MAJOR,
    MISS_QUERY_CMD_LIB_VERSION_MINOR,
    MISS_QUERY_CMD_LIB_VERSION_MICRO,
    MISS_QUERY_CMD_SEND_CLEAR_BUFFER,
    MISS_QUERY_CMD_AUDIO_SEND_CLEAR_BUFFER,
    // for tutk vendor, return the size of re-send buffer, in unit of kilo-byte.
    MISS_QUERY_CMD_AUDIO_SEND_GET_BUFFER_MAX_SIZE,
    MISS_QUERY_CMD_AUDIO_SEND_GET_FREE_BUFFER_SIZE,
    MISS_QUERY_CMD_AUDIO_SEND_GET_USED_BUFFER_SIZE,
    MISS_QUERY_CMD_AUDIO_SEND_GET_FRAME_CNT,

    MISS_QUERY_CMD_VIDEO_SEND_CLEAR_BUFFER,
    // for tutk vendor, return the size of re-send buffer, in unit of kilo-byte.
    MISS_QUERY_CMD_VIDEO_SEND_GET_BUFFER_MAX_SIZE,
    MISS_QUERY_CMD_VIDEO_SEND_GET_FREE_BUFFER_SIZE,
    MISS_QUERY_CMD_VIDEO_SEND_GET_USED_BUFFER_SIZE,
    MISS_QUERY_CMD_VIDEO_SEND_GET_FRAME_CNT,

    MISS_QUERY_CMD_AUDIO_RECV_CLEAR_BUFFER,
    MISS_QUERY_CMD_VIDEO_RECV_CLEAR_BUFFER,
    MISS_QUERY_CMD_VIDEO_RECV_GET_BUFFER_MAX_SIZE,

    MISS_QUERY_CMD_SESSION_STATUS,
    MISS_QUERY_CMD_GET_DID

} miss_query_cmd_e;

/**
 * miss_statistics_type_e - The enum of MISS statistics type
 */
typedef enum {
    MISS_STATISTICS_TYPE_MIN,
    MISS_STATISTICS_TYPE_FRAMEINFO,
    MISS_STATISTICS_TYPE_NET_INFO,  /**< update net info before invoke miss_client_session_open */
    MISS_STATISTICS_TYPE_UUID,      /**< set client uuid  */
    MISS_STATISTICS_TYPE_MAX
} miss_statistics_type_e;

/**
 * miss_frame_statistics_t - MISS video frame statistics info
 */
typedef struct miss_frame_statistics_s {
    uint32_t framecount;	    /**< Total number of video frames created */
    uint32_t dropFrame;	        /**< Total number of video frames droped */
    uint64_t byteSize;	        /**< Total size of video frames sented */
    uint64_t duration;	        /**< Duration of this paly, millisecond*/
    uint32_t audioFrameCount;   /**< Total number of audio frames created */
    uint32_t audioDropFrame;    /**< Total number of audio frames droped */
    uint64_t audioByteSize;     /**< Total size of audio frames sented */
    uint32_t autoSwitchTimes;   /**< Total video resolution switch times */
    uint32_t reserve;
} miss_frame_statistics_t;

/**
 * miss_encrypt_param_t - MISS encrypt param
 */
typedef struct {
    miss_encrypt_type_e encrypt_type;
    unsigned char *data;            /**< input data, need to be encrypt or decrypt */
    unsigned int  data_len;         /**< input data lenght */
    unsigned char *data_out;        /**< output data, only encrypt is valid */
    unsigned int  data_out_len;     /**< output data lenght,only encrypt is valid, don't modify this value */
    unsigned char *key;             /**< aes key */
    unsigned char *iv;              /**< aes initialization vector,CBC and CTR mode is valid */
    void *user_data;                /**< miss internal use data */
    unsigned char res[12];
} miss_encrypt_param_t;

/**
 * miss_lan_info_t - MISS lan model info
 */
typedef struct miss_lan_info_s {
    char* share_key;             /**< AES key*/
    uint32_t share_key_len;      /**< AES key length */

    char* p2p_id;                /**< p2p_id */
    uint32_t p2p_id_len;         /**< p2p_id length */

    char* license;               /**< p2p_id */
    uint32_t license_len;        /**< license length */

    char* crc_key;               /**< crc_key */
    uint32_t crc_key_len;        /**< crc_key length */

    char* init_string;           /**< init_string */
    uint32_t init_string_len;    /**< init_string length */

    char* Remote_ip;             /**< Remote ip info*/
    uint32_t Remote_ip_len;      /**< Remote ip length*/
} miss_lan_info_t;

/**
 * miss_session_type_e
 */
typedef enum {
    MISS_SESSION_NORMAL  = 0x0,
    MISS_SESSION_PRELINK = 0x1,
    MISS_SESSION_HWYK    = 0x2,  // 海外语控的session
} miss_session_type_e;

/**
 * miss_session_query() - MISS local status query function.
 *
 * @param[in] session 		MISS session
 * @param[in] query_cmd	::miss_query_cmd_e
 * @param[out] result		local status result
 *
 * @retval MISS_NO_ERROR: if no error
 * @retval != MISS_NO_ERROR: if error ocurrs, return the error code.
 */
int miss_session_query(miss_session_t *session, miss_query_cmd_e query_cmd, void *result);

/***************************************************
 * MISS client specific APIs (start)
 ***************************************************/
/**
 * miss_client_init() - Initiation the client
 *
 * @retval 0: if initiation success
 * @retval !=0: if initiation failed, return the error code
 */
int miss_client_init();

/**
 * miss_client_finish() - Finish MISS client.
 */
void miss_client_finish();

/**
 * miss_client_session_preopen() - Preopen MISS client.
 */
void miss_client_session_preopen(char *dids[], int size);

/**
 * miss_client_session_open() - Open one session to specific device
 *
 * This function will invoke host function miss_rpc_send() to send rpc to
 * MIoT cloud through OT, and return a @p miss_session_t placeholder.
 *
 * Please note the creation of MISS session is asynchronous, as show
 * below. miss_client_session_open() will return once miss_rpc_send() is
 * done. When ACK of RPC comes back, the upper layer will hand over that
 * RPC to MISS through miss_rpc_process().
 *
 * MISS will handle the RPC, and tunnel creation msg is notified to upper
 * layer asynchronously though the callback function miss_on_connect() and
 * miss_on_error().
 *
 * Asychrony of MISS client session creation:
 *
 * @verbatim embed:rst:leading-asterisk
 * .. uml::
 *
 *   @startuml
 *   hide footbox
 *   skinparam monochrome true
 *
 *   participant "MISS SDK (Client)" as sdk
 *   participant App as app
 *   participant "MIoT Cloud"  as cloud
 *   group MISS Session Create
 *	app -> sdk: miss_client_session_open()
 *	sdk -> app: miss_rpc_send()
 *	app -> cloud: RPC send
 *
 *	... **ASYNC**: MISS returns App a ~~session~~ placeholder ...
 *
 *	cloud -> app: RPC back
 *	app -> sdk: miss_rpc_process()
 *
 *	... **ASYNC**: MISS will notify App when ~~session~~ ready ...
 *
 *	sdk -> app: miss_on_connect()
 *   end
 *
 *   @enduml
 *
 * Pseudo code example:
 *
 * .. code-block:: c
 *
 *   miss_session_t *s;
 *   s = miss_client_session_open("1234567890");
 *
 *   // **ASYNC**
 *   // MISS will call miss_rpc_send() with first arg rpc_id.
 *   // Inside miss_rpc_send(), the upper layer will handle the
 *   // rpc_id mapping, and do some RPC send retry if necessary.
 *
 *   // **ASYNC**
 *   // Once ACK of specific RPC returns, the upper layer needs
 *   // to notify MISS.
 *   miss_rpc_process(rpc_id, "msg contents", length);
 *
 *   // **ASYNC**
 *   // The upper layer will get notified when session ready through
 *   // miss_on_connect() callback, and begin the data transfer...
 *
 * @endverbatim
 *
 * If things does not go right, i.e.: after the invoking of
 * miss_rpc_process(), whether the RPC is lost or for some reason, the ACK
 * of RPC does not come back within a timeout, we depend on the upper
 * layer to do the retry, and call miss_rpc_process() (with NULL in @p msg
 * and 0 in @p length param) if it gives up.
 *
 * MISS will handle this failure case, and notify the upper layer with
 * specific error in miss_on_error(), the upper layer needs to call
 * miss_client_session_close() so that MISS can free the resources.
 *
 * @verbatim embed:rst:leading-asterisk
 * .. uml::
 *
 *   @startuml
 *   hide footbox
 *   skinparam monochrome true
 *
 *   participant "MISS SDK (Client)" as sdk
 *   participant App as app
 *   participant "MIoT Cloud"  as cloud
 *   group MISS Session Create
 *	app -> sdk: miss_client_session_open()
 *	sdk -> app: miss_rpc_send()
 *	app -> cloud: RPC send
 *
 *	... **ASYNC**: MISS returns App a ~~session~~ placeholder ...
 *
 *	... If no RPC ACK comes back and retry still fails ...
 *
 *	app -> sdk: miss_rpc_process()
 *      sdk -> app: miss_on_error()
 *	app -> sdk: miss_client_session_close()
 *   end
 *
 *   @enduml
 *
 * Pseudo code example:
 *
 * .. code-block:: c
 *
 *   miss_session_t *s;
 *   s = miss_client_session_open("1234567890");
 *
 *   // **ASYNC**
 *   // MISS will call miss_rpc_send() with first arg rpc_id.
 *   // Inside miss_rpc_send(), the upper layer will handle the
 *   // rpc_id mapping, and do some RPC send retry if necessary.
 *
 *   // **ASYNC**
 *   // If no RPC ACK comes back and retry still fails, the upper
 *   // layer needs to notify MISS.
 *   miss_rpc_process(rpc_id, NULL, 0);
 *
 *   // **ASYNC**
 *   // The upper layer will get some error code through
 *   // miss_on_error() callback, and needs to close session.
 *   miss_client_session_close(s);
 *
 * @endverbatim
 *
 * @param[in] did device id.
 *
 * @retval miss_session_t: if send success.
 * @retval NULL: if send failed.
 */
miss_session_t *miss_client_session_open(miss_device_info_t *dev_info, miss_client_config_t *config);

/**
 * miss_client_session_lan_open() - start lan model
 * @param[in] dev_info device info(did,model ....)
 * @param[in] config   config info(max video send size,max audio send size...)
 * @param[in] lan_info lan info	(p2p_id,share_ke....)
 *
 * @retval miss_session_t: if send success.
 * @retval NULL: if send failed.
 *
 */
miss_session_t * miss_client_session_lan_open(miss_device_info_t *dev_info,
        miss_client_config_t *config, miss_lan_info_t *lan_info);

/**
 * miss_client_session_close() - Close MISS client session.
 *
 * The upper layer use this function to notify MISS to free resources.
 *
 * @param[in] session	The session returned from miss_client_session_open().
 *
 * @retval 0: if no error
 * @retval <0: if error ocurrs.
 *
 */
int miss_client_session_close(miss_session_t *session);
/***************************************************
 * MISS client specific APIs (end)
 ***************************************************/

/***************************************************
 * MISS server specific APIs (start)
 ***************************************************/
/**
 * miss_server_init() - Create MISS Server
 *
 * This function will invoke host function miss_rpc_send() to send rpc to
 * MIoT cloud through OT, and return immediately once the sent done.
 *
 * Please note the MISS server initiation and session creation are
 * asynchronous, as show below. miss_server_init() will return once
 * miss_rpc_send() is done. When ACK of RPC comes back, the upper layer
 * will hand over that RPC to MISS through miss_rpc_process().
 *
 * MISS will handle the RPC, and make sure server ready and start listen.
 *
 * When new session request comes, MISS will notify the upper layer
 * asynchronously though the callback function miss_on_connect() and
 * miss_on_error().
 *
 * Asychrony of MISS server initiation and session creation:
 *
 * @verbatim embed:rst:leading-asterisk
 * .. uml::
 *
 *   @startuml
 *   hide footbox
 *   skinparam monochrome true
 *
 *   participant "MISS SDK (Server)" as sdk
 *   participant Device as device
 *   participant "MIoT Cloud"  as cloud
 *   group MISS Server Listen & Receive Session
 *	device -> sdk: miss_server_init()
 *	sdk -> device: miss_rpc_send()
 *	device -> cloud: RPC send
 *
 *	... **ASYNC**: MISS returns Device once RPC sent ...
 *
 *	cloud -> device: RPC back
 *	device -> sdk: miss_rpc_process()
 *
 *	... **ASYNC**: MISS will notify Device when new ~~session~~ request ...
 *
 *	sdk -> device: miss_on_connect()
 *   end
 *
 *   @enduml
 *
 * Pseudo code example:
 *
 * .. code-block:: c
 *
 *   int ret;
 *   ret = miss_server_init(config);
 *
 *   // **ASYNC**
 *   // MISS will call miss_rpc_send() with first arg rpc_id.
 *   // Inside miss_rpc_send(), the upper layer will handle the
 *   // rpc_id mapping, and do some RPC send retry if necessary.
 *
 *   // **ASYNC**
 *   // Once ACK of specific RPC returns, the upper layer needs
 *   // to notify MISS.
 *   miss_rpc_process(rpc_id, "msg contents", length);
 *
 *   // **ASYNC**
 *   // The upper layer will get notified when a new connection comes through
 *   // miss_on_connect() callback, and begin the data transfer...
 *
 * @endverbatim
 *
 * If things does not go right, i.e.: after the invoking of
 * miss_rpc_process(), whether the RPC is lost or for some reason, the ACK
 * of RPC does not come back within a timeout, we depend on the upper
 * layer to do the retry, and call miss_rpc_process() (with NULL in @p msg
 * and 0 in @p length param) if it gives up.
 *
 * MISS will handle this failure case, and notify the upper layer with
 * specific error in miss_on_error(), the upper layer needs to call
 * miss_server_session_close() so that MISS can free the resources.
 *
 * @verbatim embed:rst:leading-asterisk
 * .. uml::
 *
 *   @startuml
 *   hide footbox
 *   skinparam monochrome true
 *
 *   participant "MISS SDK (Server)" as sdk
 *   participant Device as device
 *   participant "MIoT Cloud"  as cloud
 *   group MISS Server Listen & Receive Session
 *	device -> sdk: miss_server_init()
 *	sdk -> device: miss_rpc_send()
 *	device -> cloud: RPC send
 *
 *	... **ASYNC**: MISS returns Device once RPC sent ...
 *
 *	... If no RPC ACK comes back and retry still fails ...
 *
 *      device -> sdk: miss_rpc_process()
 *      sdk -> device: miss_on_error()
 *	device -> sdk: miss_server_session_close()
 *   end
 *
 *   @enduml
 *
 * Pseudo code example:
 *
 * .. code-block:: c
 *
 *   int ret;
 *   ret = miss_server_init(config);
 *
 *   // **ASYNC**
 *   // MISS will call miss_rpc_send() with first arg rpc_id.
 *   // Inside miss_rpc_send(), the upper layer will handle the
 *   // rpc_id mapping, and do some RPC send retry if necessary.
 *
 *   // **ASYNC**
 *   // If no RPC ACK comes back and retry still fails, the upper
 *   // layer needs to notify MISS.
 *   miss_rpc_process(rpc_id, NULL, 0);
 *
 *   // **ASYNC**
 *   // The upper layer will get some error code through
 *   // miss_on_error() callback, and needs to close session.
 *   miss_server_session_close(s);
 *
 * @endverbatim
 *
 * @param[in] config	configs of miss server, such as the max_session_num
 *
 * @retval 0: if send success.
 * @retval <0: if send failed.
 *
 */
int miss_server_init(miss_device_info_t *dev_info, miss_server_config_t *config);

/**
 * miss_server_add_lan_info() - start lan model
 *
 * @param[in] lan_info
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_server_add_lan_info(miss_lan_info_t *lan_info);

/**
 * miss_server_finish() - Finish MISS Server.
 */
void miss_server_finish(void);

/**
 * miss_server_session_close() - Close one session.
 *
 * @param[in] session	a specific MISS session.
 *
 */
void miss_server_session_close(miss_session_t *session);
/***************************************************
 * MISS server specific APIs (end)
 ***************************************************/

/**
 * miss_rpc_process() - RPC process function
 *
 * The upper layer use this function to hand over RPC ACK to MISS.
 *
 * The upper layer needs to maintain a mapping of the @p rpc_id and RPC in
 * miss_rpc_send(), and when RPC ACK comes back, hand over the correct RPC
 * ACK to MISS through this function. With wrong ACK msg, undefined error
 * will happen in MISS.
 *
 * If @p msg is from OT itself (not from MIoT cloud), set @p rpc_id to NULL.
 *
 * @param[in] rpc_id	used to identify each separate RPC.
 * @param[in] msg	the message receive from OT/MIoT Cloud
 * @param[in] length	length of message
 *
 * @retval 0: if no error
 * @retval <0: if error ocurrs.
 */
int miss_rpc_process(void *rpc_id, const char *msg, unsigned int length);

/**
 * miss_video_send() - MISS video send function
 *
 * @param[in] session		MISS session
 * @param[in] frame_header	frame header
 * @param[in] data		video frame data
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_video_send(miss_session_t *session, miss_frame_header_t *frame_header, void *data);

/**
 * miss_audio_send() - MISS audio send function
 *
 * @param[in] session		MISS session
 * @param[in] frame_header	frame header
 * @param[in] data		video frame data
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_audio_send(miss_session_t *session, miss_frame_header_t *frame_header, void *data);

#ifdef BUILT_FOR_IOS_PLATFORM
/**
 * miss_tutk_audio_send() - TUTK audio send function
 *
 * @param[in] session		MISS session
 * @param[in] header	    frame header
 * @param[in] header_length		header length
 * @param[in] data		    audio frame data
 * @param[in] length		audio length
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_tutk_audio_send(miss_session_t *session, void *data, int length, void *header, int header_length);
#endif
/**
 * miss_rdt_send() - MISS Reliable Data Tunnel send function
 *
 * @param[in] session		MISS session
 * @param[in] data		video frame data
 * @param[in] length		data length, the value must <= 2048
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_rdt_send(miss_session_t *session, void *data, unsigned int length);

/**
 * miss_cmd_send() - MISS CMD send function
 *
 * @param[in] session		MISS session
 * @param[in] cmd		::miss_cmd_e
 * @param[in] param		::miss_cmd_e params
 * @param[in] length		param length, the value must <= 1000
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_cmd_send(miss_session_t *session, miss_cmd_e cmd, void *params, unsigned int length);

/**
 * miss_log_set_level() - MISS set log level
 *
 * @param[in] level ::miss_log_level_e
 */
void miss_log_set_level(miss_log_level_e level);

/**
 * miss_log_set_path() - MISS set log path
 *
 * @param[in] path full log path (including file name), default stdout
 * @retval 0: if no error.
 * @retval != 0: if error ocurrs.
 */
int miss_log_set_path(const char *path);

/**
 * miss_statistics_set() - MISS set statistics info
 *
 * @param[in] session		MISS session
 * @param[in] type			::miss_statistics_type_e
 * @param[in] params		::miss_statistics_type_e params
 * @param[in] length		param length, the value must <= 1000
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_statistics_set(miss_session_t *session, miss_statistics_type_e type, void *params, unsigned int length);

/**
 * miss_mirtc_receive() - ot cloud MiRTC rpc message down(transfer) to miss process function
 *
 * The upper layer use this function to transfer MiRTC RPC message to MISS.
 *
 * @param[in] did   the mirtc message 's device id;
 * @param[in] message   the mirtc message receive from OT/MIoT Cloud
 * @param[in] length    length of message
 *
 * @retval 0: if no error
 * @retval <0: if error ocurrs.
 */
int miss_mirtc_receive(const char *did, const char *message, unsigned int length);

/**
 * miss_mirtc_respond() - OT or MQTT return response of the mirtc request message by miss_mirtc_send()
 *
 * @param[in] mirtc_id  miss_mirtc_send() 's mirtc_id;
 * @param[in] message   the mirtc response message receive from OT/MIoT Cloud or MQTT
 * @param[in] length    length of message
 */
int miss_mirtc_respond(const char *mirtc_id, const char *message, unsigned int length);

const char* miss_get_cmd_desc(miss_cmd_e cmd);
#endif
