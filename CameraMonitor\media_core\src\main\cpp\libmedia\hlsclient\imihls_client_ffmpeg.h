#pragma once

#include "imimedia_include.h"

typedef struct imihls_client_ffmpeg_info_s *imihls_client_ffmpeg_info_t;

void imihls_client_init_ffmpeg();

int imihls_client_play(const char* url,
	video_codec_id *video,
	audio_codec_id *audio,
	unsigned int *vfps,
	unsigned int *vwidth,
	unsigned int *vheight,
	unsigned int *achannel,
	unsigned int *asamplerate,
	unsigned long long *duration,
	/*out*/imihls_client_ffmpeg_info_t* handle);

int imihls_read_frame(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int len,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype);

int imihls_client_push(const char* url,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	unsigned long long duration,
	/*out*/imihls_client_ffmpeg_info_t* handle);

int imihls_client_write_video_frame(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype);

int imihls_client_write_audio_frame(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp);

int imihls_client_pause(imihls_client_ffmpeg_info_t handle);

int imihls_client_resume(imihls_client_ffmpeg_info_t handle);

int imihls_client_seek(imihls_client_ffmpeg_info_t handle,
	unsigned long long timestamp);

int imihls_client_close(imihls_client_ffmpeg_info_t handle);