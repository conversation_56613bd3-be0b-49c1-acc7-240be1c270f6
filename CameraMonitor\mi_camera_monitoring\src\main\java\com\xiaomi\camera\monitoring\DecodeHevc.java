package com.xiaomi.camera.monitoring;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.util.Log;
import android.view.Surface;

import com.xiaomi.camera.monitoring.entity.FrameData;

import java.nio.ByteBuffer;

/**
 * hevc格式编码器
 */
public class DecodeHevc implements IDecoder {

    private static final String TAG = "DecodeHevc";

    private static final int FRAME_RATE = 15;//帧率15帧，和底层固定，发送的时间戳计算也要，更重要的是合成视频处也要
    private static final int BIT_RATE_1080P = 2000_000;
    private static final int BIT_RATE_720P = 800_000;

    private MediaCodec mediaCodec;
    private MediaCodec.BufferInfo bufferInfo;
    private Surface mSurface;

    private byte[] configByte;
    private int width;
    private int height;
    private long generateIndex = 1;
    private MediaFormat mMediaFormat;
    private String mEncodeType = MediaFormat.MIMETYPE_VIDEO_HEVC;

    public DecodeHevc(Surface surface) {
        this.mSurface = surface;
    }

    @Override
    public void init(Object... params) {
        mEncodeType = (String) params[0];
        this.width = (int) params[1];
        this.height = (int) params[2];
        Log.v(TAG, "init DecodeHevc :" + width + "  " + height);

        mMediaFormat = MediaFormat.createVideoFormat(mEncodeType, width, height);

        mMediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible);

        mMediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE_720P);

        mMediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);//I帧间隔1
        mMediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);//帧率
        try {
            mediaCodec = MediaCodec.createDecoderByType(mEncodeType);
            mediaCodec.configure(mMediaFormat, mSurface, null, 0);
            mediaCodec.start();
        } catch (Exception e) {
            Log.e(TAG, "init DecodeHevc throw exception:", e);
            mediaCodec = null;
        }
    }

    @Override
    public synchronized void decode(FrameData frameData) throws IllegalStateException {
        if (mediaCodec == null) {
            return;
        }
        byte[] input = frameData.dataBytes;
        try {
            final int inputBufferIndex = mediaCodec.dequeueInputBuffer(10000);
            if (inputBufferIndex >= 0) {
                final ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputBufferIndex);
                inputBuffer.clear();
                if (input != null) {
                    inputBuffer.put(input);
                    inputBuffer.limit(frameData.getDataLen());
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, input.length, computePresentationTime(generateIndex), 0);
                } else {//当buff为只读的情况下
                    inputBuffer.put(frameData.dataBytes);
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, frameData.getDataLen(), computePresentationTime(generateIndex), 0);
                }
            }

            int outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 10000);
            while (outputBufferIndex >= 0) {
                //如果surface绑定了，则直接输入到surface渲染并释放
                mediaCodec.releaseOutputBuffer(outputBufferIndex, true);
                outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 0);
            }
            generateIndex++;
        } catch (Exception e) {
            Log.e(TAG, "DecodeHevc throw exception, then restart:", e);
            try {
                mediaCodec = MediaCodec.createDecoderByType(mEncodeType);
                mediaCodec.configure(mMediaFormat, mSurface, null, 0);
                mediaCodec.start();
            } catch (Exception ex) {
                Log.e(TAG, "restart DecodeHevc throw exception:", ex);
                mediaCodec = null;
            }
        }
    }

    @Override
    public void startDecoder() {
        if (mediaCodec == null) {
            Log.e(TAG, "Please initialize first.");
            return;
        }
        generateIndex = 1;
        bufferInfo = new MediaCodec.BufferInfo();
    }

    @Override
    public void stopDecoder() {
        if (mediaCodec != null) {
            try {
                mediaCodec.stop();
                mediaCodec.release();
            } catch (Exception e) {
                Log.e(TAG, "stop Encode throw exception:", e);
            }
        }
    }

    @Override
    public synchronized void release() {
        stopDecoder();
        if (mediaCodec != null) {
            mediaCodec.release();
            mediaCodec = null;
        }
    }

    private long computePresentationTime(long frameIndex) {
        return frameIndex * 1000000 / (width * height * 5);
    }
}
