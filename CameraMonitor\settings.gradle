include ':app'
include ':mi_camera_monitoring'
include ':media_core'
include ':media_opus'
include ':media_yuv'
include ':socket-client'
project(':socket-client').projectDir = new File("./OkSocket/socket-client")
include ':socket-server'
project(':socket-server').projectDir = new File("./OkSocket/socket-server")
include ':socket-common-interface'
project(':socket-common-interface').projectDir = new File("./OkSocket/socket-common-interface")
include ':socket-core'
project(':socket-core').projectDir = new File("./OkSocket/socket-core")
rootProject.name = "CameraMonitor"