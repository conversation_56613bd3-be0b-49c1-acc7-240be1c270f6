package com.xiaomi.mico.persistent.entry;

import java.io.Serializable;
import java.util.ArrayList;

public class RetryUploadEntry implements Serializable {
    private int sid;
    private String imgPath;
    private ArrayList<String> videoPathList;
    private boolean isBell;
    private boolean needPush;
    private String eventType;

    public int getSid() {
        return sid;
    }

    public void setSid(int sid) {
        this.sid = sid;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public ArrayList<String> getVideoPathList() {
        return videoPathList;
    }

    public void setVideoPathList(ArrayList<String> videoPathList) {
        this.videoPathList = videoPathList;
    }

    public boolean isBell() {
        return isBell;
    }

    public void setBell(boolean bell) {
        isBell = bell;
    }

    public boolean isNeedPush() {
        return needPush;
    }

    public void setNeedPush(boolean needPush) {
        this.needPush = needPush;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
}
