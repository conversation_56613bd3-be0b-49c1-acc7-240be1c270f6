package com.xiaomi.mico.persistent.utils;

import android.content.Context;
import android.net.Uri;
import android.os.Bundle;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.EventConfig;

import org.json.JSONException;
import org.json.JSONObject;

public class MiotManager {
    private static final String TAG = "MiotManager";

    private final Uri MONITOR_URI = Uri.parse("content://com.xiaomi.mico.home.provider.VIDEO_MONITOR_FEATURE");
    private final String KEY_CODE = "code"; // 0 - 成功， 其他 - 失败
    private final String KEY_CONTENT = "content";
    private final String KEY_METHOD_NAME = "method_name";

    private final String METHOD_GET_MIOT_TOKEN = "get_miot_token";
    private final String METHOD_CAMERA_LOGIN = "_sync.camera_login";
    private final String KEY_P2P_CHECKTOKEN = "p2p_checktoken";

    private final String METHOD_MITO_SEND = "miot_send";
    private final String METHOD_MITO_SEND_EVENT = "miot_send_event";

    // 智能摄像机获得认证token
    public String getMiotToken() {
        String token = "";
        Bundle result = mContext.getContentResolver()
                .call(MONITOR_URI, METHOD_GET_MIOT_TOKEN, null, null);
        if (result == null) {
            L.monitor.e("%s get mIot token failed, bundle is null", TAG);
        } else if (result.getInt(KEY_CODE) == 0) {
            if (Constants.DEBUG) {
                L.monitor.d("%s get token success. token is %s", TAG, result.getString(KEY_CONTENT));
            }
            token = result.getString(KEY_CONTENT);
        } else {
            L.monitor.e("%s get mIot token failed, code is %s", TAG, result.getInt(KEY_CODE));
        }
        return token;
    }

    // 智能摄像机上传云存视频的URL
    public String cameraLogin(String miotToken) throws JSONException {
        JSONObject arg = new JSONObject();
        arg.put(KEY_P2P_CHECKTOKEN, MiUploadUtils.convertStringToHex(miotToken));
        Bundle extras = new Bundle();
        extras.putString(KEY_METHOD_NAME, METHOD_CAMERA_LOGIN);
        return callOT(METHOD_MITO_SEND, arg.toString(), extras);
    }

    // 发送事件
    public String miEVentSend(EventConfig eventConfig) throws JSONException  {
        Bundle extras = new Bundle();
        extras.putInt("siid", eventConfig.getSiid());
        extras.putInt("eiid", eventConfig.getEiid());
        if (eventConfig.getPiid() != 0 && eventConfig.getProperty_value() != null) {
            extras.putInt("piid", eventConfig.getPiid());
            extras.putString("property_value", String.valueOf(eventConfig.getProperty_value()));
        }
        return callOT(METHOD_MITO_SEND_EVENT, null, extras);
    }

    private String callOT(String method, String arg, Bundle extras) {
        Bundle result = mContext.getContentResolver()
                .call(MONITOR_URI, method, arg, extras);

        if (result == null) {
            L.monitor.e("%s mIot send failed, bundle is null", TAG);
        } else if (result.getInt(KEY_CODE) == 0) {
            String content = result.getString(KEY_CONTENT);
            if (Constants.DEBUG) {
                L.monitor.d("%s mIot send provider result is %s", TAG, content);
            }
            return content;
        } else {
            L.monitor.e("%s mIot send failed, code is %s", TAG, result.getInt(KEY_CODE));
        }
        return "";
    }

    private static volatile MiotManager sInstance;
    private Context mContext;
    public static MiotManager getInstance() {
        if (sInstance == null) {
            synchronized (MiotManager.class) {
                if (sInstance == null) {
                    sInstance = new MiotManager();
                }
            }
        }
        return sInstance;
    }

    private MiotManager() {}

    public void initMiotManager(Context context) {
        this.mContext = context;
    }
}
