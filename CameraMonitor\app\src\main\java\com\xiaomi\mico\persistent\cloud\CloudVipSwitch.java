package com.xiaomi.mico.persistent.cloud;

import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.CloudVipConfig;
import com.xiaomi.mico.persistent.utils.MiUploadUtils;
import com.xiaomi.mico.persistent.utils.MiotManager;

import org.apache.commons.codec.binary.Base64;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

public class CloudVipSwitch {
    private static final String TAG = "CloudVipSwitch";

    private UploadCallback mUploadCallback;
    private OkHttpClient mHttpClient;

    public CloudVipSwitch(UploadCallback callback) {
        mHttpClient = MiUploadUtils.getOkHttpClient();
        this.mUploadCallback = callback;
    }

    //1.智能摄像机获得认证token
    public void getDomainUrl() {
//        String miotToken = MiotManager.getInstance().getMiotToken();
//        if (TextUtils.isEmpty(miotToken)) {
//            Log.e(TAG, "getDomainUrl error, token is null");
//            return;
//        }
        try {
            String cameraLoginResult = MiotManager.getInstance().cameraLogin(Constants.OT_TOKEN);
            if (!TextUtils.isEmpty(cameraLoginResult)) {
                JSONObject domainJson = new JSONObject(cameraLoginResult);
                JSONObject domainData = new JSONObject(domainJson.getString("data"));
                String bUrl = domainData.getString("bUrl");
                String urlToken = domainData.getString("token");
                String securityKey = domainData.getString("security");
                // 获取vip状态
                getVipStatus(bUrl, urlToken, "CN", securityKey);
                getCloudSwitch(bUrl, urlToken, securityKey);
            } else {
                mUploadCallback.getDomainUrlFailed();
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //2.获取vip状态
    private void getVipStatus(String bUrl, String urlToken, String region, String securityKey) {
        String vipStatusUrl = "/common/device/vip/status";
        //IV
        String vipStatusIv = Base64.encodeBase64URLSafeString(MiUploadUtils.generateIvParameterSpec().getIV());
        //region
        String encryptRegion = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(region.getBytes(), securityKey, vipStatusIv));
        //signature
        String params = "GET&" + vipStatusUrl + "&" + vipStatusIv + "&" + encryptRegion;
        String paramsSign = MiUploadUtils.generateSign(params.getBytes());

        //请求参数封装
        Request.Builder builder = new Request.Builder();
        HttpUrl.Builder urlBuild = HttpUrl.parse(bUrl + vipStatusUrl).newBuilder();
        urlBuild.addQueryParameter("iv", vipStatusIv);
        urlBuild.addQueryParameter("signature", paramsSign);
        urlBuild.addQueryParameter("region", encryptRegion);
        builder.url(urlBuild.build());
        builder.addHeader("Cookie", "serviceToken=" + urlToken);
        Request request = builder.build();

        //发送请求
        Call call = mHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseString = response.body().string();
                if (response.isSuccessful()) {
                    try {
                        String vipStatusCallback = MiUploadUtils.decryptData(responseString, securityKey);
                        //L.monitor.i("%s getVipStatus vipStatusCallback: %s", TAG, vipStatusCallback);
                        CloudVipConfig vipConfig = getCloudVipConfig(vipStatusCallback);
                        mUploadCallback.vipStatusCallback(vipConfig);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }

                }else {
                    L.monitor.e("%s getVipStatus onResponse: %s", TAG, responseString);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                L.monitor.e("%s getVipStatus onFailure: %s", TAG, e.getMessage());
            }
        });
    }

    private static @NonNull CloudVipConfig getCloudVipConfig(String vipStatusCallback) throws JSONException {
        JSONObject vipObject = new JSONObject(vipStatusCallback);
        JSONObject dataObj = vipObject.getJSONObject("data");
        boolean isVip = dataObj.getBoolean("vip");
        long startTime = dataObj.getLong("startTime");
        long endTime = dataObj.getLong("endTime");
        boolean closeWindow = dataObj.getBoolean("closeWindow");
        long freeHomeSurExpireTime = dataObj.getLong("freeHomeSurExpireTime");
        CloudVipConfig vipConfig = new CloudVipConfig(isVip, startTime, endTime, closeWindow, freeHomeSurExpireTime);
        return vipConfig;
    }

    //3.如果是Vip用户获取云存开关
    private void getCloudSwitch(String bUrl, String urlToken, String securityKey) {
        String cloudSwitch = "/miot/camera/device/v2/switch";
        //IV
        String cloudSwitchIV = Base64.encodeBase64URLSafeString(MiUploadUtils.generateIvParameterSpec().getIV());
        //signature
        String params = "GET&" + cloudSwitch + "&" + cloudSwitchIV;
        String paramsSign = MiUploadUtils.generateSign(params.getBytes());
        //请求参数封装
        Request.Builder builder = new Request.Builder();
        HttpUrl.Builder urlBuild = HttpUrl.parse(bUrl + cloudSwitch).newBuilder();
        urlBuild.addQueryParameter("iv", cloudSwitchIV);
        urlBuild.addQueryParameter("signature", paramsSign);
        builder.url(urlBuild.build());
        builder.addHeader("Cookie", "serviceToken=" + urlToken);
        Request request = builder.build();

        //发送请求
        Call call = mHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseString = response.body().string();
                if (response.isSuccessful()) {
                    try {
                        String cloudSwitchCallback = MiUploadUtils.decryptData(responseString, securityKey);
                        //L.monitor.i("%s getCloudSwitch cloudSwitchCallback: %s", TAG, cloudSwitchCallback);
                        JSONObject cloudSwitchObj = new JSONObject(cloudSwitchCallback);
                        boolean cloudSwitch = cloudSwitchObj.getJSONObject("data").getBoolean("cloudSwitch");
                        mUploadCallback.cloudSwitchCallback(cloudSwitch);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    L.monitor.e("%s getCloudSwitch onResponse: %s", TAG, responseString);
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                L.monitor.e("%s getVipStatus onFailure: %s", TAG, e.getMessage());
            }
        });
    }

    public interface UploadCallback {
        void getDomainUrlFailed();
        void vipStatusCallback(CloudVipConfig vipConfig);
        void cloudSwitchCallback(boolean cloudSwitch);
    }
}
