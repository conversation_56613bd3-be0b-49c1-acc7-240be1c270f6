#include <stdlib.h>
#include <string.h>
#ifndef WIN32
#include <pthread.h>
#else
#include <Windows.h>
#include "imimedia_win2linux.h"
#endif

#include "imihls_client_ffmpeg.h"
#include "imiffmpeg_common.h"
#include "imiparser_aac.h"

#if defined(WIN32) && !defined(__cplusplus)  
#define inline __inline  
#endif 

#ifdef __cplusplus
extern "C"{
#endif
#include <libavformat/avformat.h>
#include <libavformat/avio.h>
#include <libavutil/mem.h>
#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#endif

typedef struct imihls_client_ffmpeg_info_s {
#ifndef WIN32
	pthread_mutex_t _lock_mutex;
#endif
	int _getfirstframe;
	AVFormatContext* _format_context;
	video_codec_id _video_codec;
	audio_codec_id _audio_codec;
	AVStream* _video_stream;
	unsigned char* _video_extradata;
	unsigned int _video_extradata_size;
	unsigned int _video_frame_index;
	unsigned int _video_fps;
	unsigned long long _video_lasttimestamp;
	unsigned int _video_lastpts;
	AVStream* _audio_stream;
	AVBSFContext *_audio_bsfc;
	unsigned char* _audio_extradata;
	unsigned int _audio_extradata_size;
	unsigned long long _audio_lasttimestamp;
} imihls_client_ffmpeg_info_s, *imihls_client_ffmpeg_info_t;

void imihls_client_init_ffmpeg()
{
	printf("imihls_client_init_ffmpeg\n");
	av_register_all();
	avformat_network_init();
}

int _hls_play(imihls_client_ffmpeg_info_t handle,
	const char* url,
	video_codec_id *video,
	audio_codec_id *audio,
	unsigned int *vfps,
	unsigned int *vwidth,
	unsigned int *vheight,
	unsigned int *achannel,
	unsigned int *asamplerate,
	unsigned long long *duration)
{
	int ret = IMIMEDIA_OK;
	unsigned int i = 0;
	if (handle == 0) return IMIMEDIA_PARAMS_ERROR;//ASSERT WARNING
	//////////////////////////////////////////////////////////////////////////
	handle->_format_context = avformat_alloc_context();
	if (handle->_format_context == NULL) {
		printf("imihls_client_play avformat_alloc_context error\n");
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	handle->_format_context->flags |= AVFMT_FLAG_NOBUFFER;
	ret = avformat_open_input(&handle->_format_context, url, NULL, NULL);
	if (ret < 0) {
		ffmpeg_err2str("imihls_client_play avformat_open_input", ret);
		goto error;
	}
	ret = avformat_find_stream_info(handle->_format_context, NULL);
	if (ret < 0) {
		ffmpeg_err2str("imihls_client_play avformat_find_stream_info", ret);
		goto error;
	}
	for (i = 0; i < handle->_format_context->nb_streams; i++) {
		AVStream* stream = handle->_format_context->streams[i];
		switch (stream->codec->codec_type)
		{
		case AVMEDIA_TYPE_VIDEO:
			{
				switch (stream->codec->codec_id)
				{
				case AV_CODEC_ID_H264:
					{
						handle->_video_codec = video_codec_h264;
					}
					break;
				case AV_CODEC_ID_H265:
					{
						handle->_video_codec = video_codec_h265;
					}
					break;
				default:
					printf("imihls_client_play video codec error %d\n", stream->codec->codec_id);
					return IMIMEDIA_PARAMS_ERROR;
				}
				*video = handle->_video_codec;
				*vfps = stream->avg_frame_rate.num/stream->avg_frame_rate.den;
				*vwidth = stream->codec->width;
				*vheight = stream->codec->height;
			}
			break;
		case AVMEDIA_TYPE_AUDIO:
			{
				switch (stream->codec->codec_id)
				{
				case AV_CODEC_ID_AAC:
					{
						handle->_audio_codec = audio_codec_aac;
					}
					break;
				case AV_CODEC_ID_PCM_ALAW:
					{
						handle->_audio_codec = audio_codec_g711a;
					}
					break;
				case AV_CODEC_ID_PCM_MULAW:
					{
						handle->_audio_codec = audio_codec_g711u;
					}
					break;
				default:
					printf("imihls_client_play auido codec error %d\n", stream->codec->codec_id);
					return IMIMEDIA_PARAMS_ERROR;
				}
				*audio = handle->_audio_codec;
				*achannel = stream->codec->channels;
				*asamplerate = stream->codec->sample_rate;
			}
			break;
		default:
			continue;
		}
	}
	*duration = (unsigned long long)handle->_format_context->duration;
	//////////////////////////////////////////////////////////////////////////
	printf("imihls_client_play success\n");
	return IMIMEDIA_OK;

error:
	avformat_close_input(&handle->_format_context);
	handle->_format_context = NULL;
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imihls_client_play(const char* url,
	video_codec_id *video,
	audio_codec_id *audio,
	unsigned int *vfps,
	unsigned int *vwidth,
	unsigned int *vheight,
	unsigned int *achannel,
	unsigned int *asamplerate,
	unsigned long long *duration,
	/*out*/imihls_client_ffmpeg_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imihls_client_ffmpeg_info_t handle_impl = NULL;
	handle_impl = (imihls_client_ffmpeg_info_t)malloc(sizeof(imihls_client_ffmpeg_info_s));
	if (handle_impl == NULL) {
		printf("imihls_client_ffmpeg_info_t malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imihls_client_ffmpeg_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _hls_play(handle_impl, url, video, audio, vfps, vwidth, vheight, achannel, asamplerate, duration);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imihls_client_ffmpeg_info_t handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _hls_read(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int len,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype)
{
	int ret = IMIMEDIA_OK;
	int64_t time = 0;
	AVStream* stream = NULL;
	AVPacket pkt = { 0 };
	if (handle->_format_context == NULL) {
		printf("imihls_read_frame format_context is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	ret = av_read_frame(handle->_format_context, &pkt);
	if (ret < 0) {
		if (ret == AVERROR_EOF) {
			//this is correct error
			printf("imihls_read_frame av_read_frame eof\n");
			return IMIMEDIA_EOF;
		} else {
			ffmpeg_err2str("imihls_read_frame av_read_frame fail", ret);
#ifdef WIN32
			Sleep(1);
#else
			usleep(1000);//1ms
#endif
			return ret;
		}
	}
	if (pkt.size <= 0 || pkt.data == NULL) {
		printf("imihls_read_frame size %d data %d\n", pkt.size, pkt.data);
		goto GET_FAIL;
	}
	if (pkt.stream_index >= (int)handle->_format_context->nb_streams) {
		printf("imihls_read_frame index %d nsstreams %d\n", pkt.stream_index, handle->_format_context->nb_streams);
		goto GET_FAIL;
	}
	stream = handle->_format_context->streams[pkt.stream_index];
	switch (stream->codec->codec_type)
	{
	case AVMEDIA_TYPE_VIDEO:
		{
			if (pkt.flags & AV_PKT_FLAG_KEY) {
				*frametype = frame_type_i;
			} else {
				*frametype = frame_type_p;
			}
			if (len >= (unsigned int)pkt.size) {
				*data_len = pkt.size;
				memcpy(data, (unsigned char*)pkt.data, pkt.size);
			} else {
				goto GET_FAIL;
			}
		}
		break;
	case AVMEDIA_TYPE_AUDIO:
		{
			*frametype = frame_type_audio;
			if (len >= (unsigned int)pkt.size) {
				*data_len = pkt.size;
				memcpy(data, (unsigned char*)pkt.data, pkt.size);
			} else {
				goto GET_FAIL;
			}
		}
		break;
	default:
		{
			if (stream->codec->codec_type == pkt.size) {//maybe is audio???
				*frametype = frame_type_audio;
				if (len >= (unsigned int)pkt.size) {
					*data_len = pkt.size;
					memcpy(data, (unsigned char*)pkt.data, pkt.size);
				} else {
					goto GET_FAIL;
				}
			} else {
				*frametype = (frame_type_id)(0xFF + stream->codec->codec_type);
				goto GET_FAIL;
			}
		}
		break;
	}
	if (pkt.pts && stream->time_base.den) {
		*timestamp = (unsigned long long)(((double)pkt.pts / (double)stream->time_base.den) * 1000);
	}
	av_packet_unref(&pkt);
	return IMIMEDIA_OK;
GET_FAIL:
	av_packet_unref(&pkt);
	return IMIMEDIA_GET_FAIL;
}

int imihls_read_frame(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int len,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _hls_read(handle, data, len, data_len, timestamp, frametype);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _hls_push(imihls_client_ffmpeg_info_t handle,
	const char* url,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	unsigned long long duration)
{
	int ret = IMIMEDIA_OK;
	unsigned int i = 0;
	const AVBitStreamFilter *filter = NULL;
	if (handle == 0) return IMIMEDIA_PARAMS_ERROR;//ASSERT WARNING
	//////////////////////////////////////////////////////////////////////////
	handle->_format_context = avformat_alloc_context();
	if (handle->_format_context == NULL) {
		printf("imihls_client_record avformat_alloc_context error\n");
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	handle->_format_context->flags |= AVFMT_FLAG_NOBUFFER;
	handle->_format_context->oformat = av_guess_format("flv", url, NULL);
	if (handle->_format_context->oformat == NULL) {
		printf("imihls_client_record av_guess_format error url = %s\n", url);
		goto error;
	}
	handle->_format_context->duration = (int64_t)duration;
	//////////////////////////////////////////////////////////////////////////
	switch (video)
	{
	case video_codec_h264:
		handle->_format_context->oformat->video_codec = AV_CODEC_ID_H264;
		break;
	case video_codec_h265:
		handle->_format_context->oformat->video_codec = AV_CODEC_ID_H265;
		break;
	default:
		printf("imihls_client_record video_codec_id default = %d\n", video);
		goto error;
	}
	switch (audio)
	{
	case audio_codec_g711a:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_PCM_ALAW;
		break;
	case audio_codec_g711u:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_PCM_MULAW;
		break;
	case audio_codec_aac:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_AAC;
		break;
	default:
		printf("imihls_client_record audio_codec_id default = %d\n", audio);
		goto error;
	}
	strncpy(handle->_format_context->filename, url, sizeof(handle->_format_context->filename));
	//////////////////////////////////////////////////////////////////////////
	handle->_video_stream = avformat_new_stream(handle->_format_context, NULL);
	if (handle->_video_stream == NULL) {
		printf("imihls_client_record avformat_new_stream video error\n");
		goto error;
	}
	handle->_video_stream->id = handle->_format_context->nb_streams - 1;
	handle->_video_stream->codec->codec_type = AVMEDIA_TYPE_VIDEO;
	switch (video)
	{
	case video_codec_h264:
		handle->_video_stream->codec->codec_id = AV_CODEC_ID_H264;
		handle->_video_stream->codec->level = 0x7F;
		break;
	case video_codec_h265:
		handle->_video_stream->codec->codec_id = AV_CODEC_ID_H265;
		break;
	default:
		printf("imihls_client_record video_codec_id default = %d\n", video);
		goto error;
	}
	handle->_video_stream->codec->width = vwidth;
	handle->_video_stream->codec->height = vheight;
	handle->_video_stream->codec->time_base = av_d2q(1.0/vfps, 255);
	handle->_video_stream->r_frame_rate = av_d2q(vfps, 255);
	handle->_video_stream->time_base.num = 1;
	handle->_video_stream->time_base.den = IMI_VIDEO_TIME_BASE;
	handle->_video_fps = vfps;
	handle->_video_codec = video;
	//////////////////////////////////////////////////////////////////////////
	handle->_audio_stream = avformat_new_stream(handle->_format_context, NULL);
	if (handle->_audio_stream == NULL) {
		printf("imihls_client_record avformat_new_stream audio error\n");
		goto error;
	}
	handle->_audio_stream->id = handle->_format_context->nb_streams - 1;
	handle->_audio_stream->codec->codec_type = AVMEDIA_TYPE_AUDIO;
	switch (audio)
	{
	case audio_codec_g711a:
		{
			handle->_audio_stream->codec->codec_id = AV_CODEC_ID_PCM_ALAW;
			handle->_audio_stream->codec->sample_rate = asamplerate;
			handle->_audio_stream->codec->channels = achannel;
		}
		break;
	case audio_codec_g711u:
		{
			handle->_audio_stream->codec->codec_id = AV_CODEC_ID_PCM_MULAW;
			handle->_audio_stream->codec->sample_rate = asamplerate;
			handle->_audio_stream->codec->channels = achannel;
		}
		break;
	case audio_codec_aac:
		{
			handle->_audio_stream->codec->codec_id = AV_CODEC_ID_AAC;
			handle->_audio_stream->codec->sample_rate = asamplerate;
			handle->_audio_stream->codec->channels = achannel;
			handle->_audio_stream->codec->profile = FF_PROFILE_AAC_LOW;
			handle->_audio_stream->codec->level = 0x02;
			handle->_audio_extradata = imi_make_aac_track_configure(0x02, imi_make_aac_header_sampling_frequency_index(asamplerate), achannel);
			handle->_audio_extradata_size = 2;
			handle->_audio_stream->codec->extradata = handle->_audio_extradata;
			handle->_audio_stream->codec->extradata_size = handle->_audio_extradata_size;
			handle->_audio_stream->codec->frame_size = (int)((double)IMI_AAC_TIME_BASE*(double)(handle->_audio_stream->codec->sample_rate/(double)16000));
		}
		break;
	default:
		printf("imihls_client_record audio_codec_id default = %d\n", audio);
		goto error;
	}
	handle->_audio_stream->codec->time_base.num = 1;
	handle->_audio_stream->codec->time_base.den = asamplerate;
	handle->_audio_stream->time_base.num = 1;
	handle->_audio_stream->time_base.den = asamplerate;
	handle->_audio_codec = audio;
	//////////////////////////////////////////////////////////////////////////
	filter = av_bsf_get_by_name("aac_adtstoasc");
	if (filter == NULL) {
		printf("imi_mp4_open_file av_bitstream_filter_init hevc_mp4toannexb error\n");
		goto error;
	}
	ret = av_bsf_alloc(filter, &handle->_audio_bsfc);
	if (ret != 0) {
		ffmpeg_err2str("imihls_client_record av_bsf_alloc", ret);
		goto error;
	}
	//////////////////////////////////////////////////////////////////////////
	av_dump_format(handle->_format_context, 0, url, 1);
	ret = avio_open(&handle->_format_context->pb, url, AVIO_FLAG_WRITE);
	if (ret < 0) {
		ffmpeg_err2str("imihls_client_record avio_open", ret);
		goto error;
	}
	//////////////////////////////////////////////////////////////////////////
	handle->_video_extradata_size = 0;
	handle->_video_extradata = (unsigned char*)malloc(4096);
	memset(handle->_video_extradata, 0, 4096);
	printf("imihls_client_record success\n");
	return IMIMEDIA_OK;

error:
	if (handle->_audio_bsfc) {
		av_bsf_free(&handle->_audio_bsfc);
		handle->_audio_bsfc = NULL;
	}
	if (handle->_video_stream) {
		avcodec_close(handle->_video_stream->codec);
		handle->_video_stream->codec->extradata = NULL;
		handle->_video_stream->codec->extradata_size = 0;
		handle->_video_stream = NULL;
	}
	if (handle->_audio_stream) {
		avcodec_close(handle->_audio_stream->codec);
		handle->_audio_stream->codec->extradata = NULL;
		handle->_audio_stream->codec->extradata_size = 0;
		handle->_audio_stream = NULL;
	}
	avformat_close_input(&handle->_format_context);
	handle->_format_context = NULL;
	if (handle->_audio_extradata) {
		free(handle->_audio_extradata);
		handle->_audio_extradata = NULL;
	}
	if (handle->_video_extradata) {
		free(handle->_video_extradata);
		handle->_video_extradata = NULL;
	}
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imihls_client_push(const char* url,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	unsigned long long duration,
	/*out*/imihls_client_ffmpeg_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imihls_client_ffmpeg_info_t handle_impl = NULL;
	handle_impl = (imihls_client_ffmpeg_info_t)malloc(sizeof(imihls_client_ffmpeg_info_s));
	if (handle_impl == NULL) {
		printf("imihls_client_ffmpeg_info_t malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imihls_client_ffmpeg_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _hls_push(handle_impl, url, video, audio, vfps, vwidth, vheight, achannel, asamplerate, duration);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imihls_client_ffmpeg_info_t handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _hls_write_video(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype)
{
	int key = 0;
	unsigned int offset = 0;
	int ret = IMIMEDIA_OK;
	AVPacket pkt = { 0 };
	imi_nalu_array_t nalu_array = NULL;
	unsigned char* data_buff_src = (unsigned char*)data;
	if (handle->_video_stream == NULL) {
		printf("imihls_client_write_video_frame video_stream is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	nalu_array = imi_get_nalu_array(data_buff_src, data_len);
	if (nalu_array == NULL) {
		printf("imi_mp4_write_video_frame get_nalu_info error\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	switch (handle->_video_codec)
	{
	case video_codec_h264:
		key = ffmpeg_get_extradata_h264(nalu_array, handle->_video_extradata, &handle->_video_extradata_size, &offset);
		break;
	case video_codec_h265:
		key = ffmpeg_get_extradata_h265(nalu_array, handle->_video_extradata, &handle->_video_extradata_size, &offset);
		break;
	default:
		printf("imihls_client_write_video_frame video_codec_id default = %d\n", handle->_video_codec);
		imi_free_nalu_array(nalu_array);
		return IMIMEDIA_PARAMS_ERROR;
	}
	imi_free_nalu_array(nalu_array);
	if (key == 0 && handle->_getfirstframe == 0) {
		printf("imi_mp4_write_video_frame getfirstframe is not key frame\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	if (handle->_getfirstframe == 0) {
		int ret = IMIMEDIA_OK;
		handle->_video_stream->codec->extradata = handle->_video_extradata;
		handle->_video_stream->codec->extradata_size = handle->_video_extradata_size;
		ret = avformat_write_header(handle->_format_context, NULL);
		if (ret < 0) {
			ffmpeg_err2str("imihls_client_record avformat_write_header", ret);
		}
		handle->_getfirstframe = 1;
	}
	ret = av_new_packet(&pkt, data_len);
	if (ret != 0) {
		ffmpeg_err2str("imihls_client_write_video_frame av_new_packet", ret);
		return ret;
	}
	if (frametype == frame_type_i) {
		pkt.flags |= AV_PKT_FLAG_KEY;
	}
	memcpy(pkt.data, (uint8_t*)data, data_len);
	pkt.size = data_len;
	if (vtimestamp == 0) {
		pkt.pts = av_rescale(handle->_video_frame_index++,
			handle->_video_stream->time_base.den,
			handle->_video_stream->codec->time_base.den);
		pkt.duration = IMI_VIDEO_TIME_BASE/handle->_video_fps;
	} else {
		if (handle->_video_lasttimestamp == 0) {
			pkt.pts = av_rescale(handle->_video_frame_index++,
				handle->_video_stream->time_base.den,
				handle->_video_stream->codec->time_base.den);
			pkt.duration = IMI_VIDEO_TIME_BASE/handle->_video_fps;
		} else {
			int64_t time = IMI_PTS2TIME_SCALE(vtimestamp, handle->_video_lasttimestamp, IMI_VIDEO_TIME_BASE);
			if (time >= IMI_VIDEO_TIME_BASE) {
				time = IMI_VIDEO_TIME_BASE/handle->_video_fps;
			}
			pkt.pts = handle->_video_lastpts + time;
			pkt.duration = time;
		}
		handle->_video_lasttimestamp = vtimestamp;
		handle->_video_lastpts = (unsigned int)pkt.pts;
	}
	pkt.stream_index = handle->_video_stream->index;
	pkt.dts = pkt.pts;
	pkt.pos = -1;
	ret = av_interleaved_write_frame(handle->_format_context, &pkt);
	if (ret != 0) {
		ffmpeg_err2str("imihls_client_write_video_frame av_interleaved_write_frame", ret);
#ifdef WIN32
		Sleep(1);
#else
		usleep(1000);//1ms
#endif
	}
	av_packet_unref(&pkt);
	return ret;
}

int imihls_client_write_video_frame(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _hls_write_video(handle, data, data_len, vtimestamp, frametype);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _hls_write_audio(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp)
{
	int ret = IMIMEDIA_OK;
	AVPacket pkt = { 0 };
	if (handle->_audio_stream == NULL) {
		printf("imihls_client_write_audio_frame audio_stream is null\n");
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	ret = av_new_packet(&pkt, data_len);
	if (ret != 0) {
		ffmpeg_err2str("imihls_client_write_audio_frame av_new_packet", ret);
		return ret;
	}
	pkt.flags |= AV_PKT_FLAG_KEY;
	memcpy(pkt.data, (uint8_t*)data, data_len);
	pkt.size = data_len;
	switch (handle->_audio_codec)
	{
	case audio_codec_aac:
		{
			if (handle->_audio_bsfc) {
				ret = av_bsf_send_packet(handle->_audio_bsfc, &pkt);
				if (ret != 0) {
					ffmpeg_err2str("imihls_client_write_audio_frame av_bsf_send_packet", ret);
					av_packet_unref(&pkt);
					return ret;
				}
				ret = av_bsf_receive_packet(handle->_audio_bsfc, &pkt);
				if (ret != 0) {
					ffmpeg_err2str("imihls_client_write_audio_frame av_bsf_receive_packet", ret);
					av_packet_unref(&pkt);
					return ret;
				}
			}
			pkt.duration = (int)((double)IMI_AAC_TIME_BASE*(double)(handle->_audio_stream->codec->sample_rate/(double)16000));
		}
		break;
	case audio_codec_g711a:
	case audio_codec_g711u:
		{
			pkt.duration = data_len;
		}
		break;
	default:
		printf("imihls_client_write_audio_frame audio_codec_id default = %d\n", handle->_audio_codec);
		return IMIMEDIA_PARAMS_ERROR;
	}
	pkt.stream_index = handle->_audio_stream->index;
	pkt.pts = handle->_audio_lasttimestamp + pkt.duration;
	pkt.dts = pkt.pts;
	handle->_audio_lasttimestamp = (unsigned long long)pkt.pts;
	pkt.pos = -1;
	ret = av_interleaved_write_frame(handle->_format_context, &pkt);
	if (ret != 0) {
		ffmpeg_err2str("imihls_client_write_audio_frame av_interleaved_write_frame", ret);
#ifdef WIN32
		Sleep(1);
#else
		usleep(1000);//1ms
#endif
	}
	av_packet_unref(&pkt);
	return ret;
}

int imihls_client_write_audio_frame(imihls_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _hls_write_audio(handle, data, data_len, atimestamp);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _hls_pause(imihls_client_ffmpeg_info_t handle)
{
	if (handle->_format_context == NULL) {
		printf("imihls_client_pause format_context is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	return av_read_pause(handle->_format_context);
}

int imihls_client_pause(imihls_client_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _hls_pause(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _hls_resume(imihls_client_ffmpeg_info_t handle)
{
	if (handle->_format_context == NULL) {
		printf("imihls_client_resume format_context is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	return av_read_play(handle->_format_context);
}

int imihls_client_resume(imihls_client_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _hls_resume(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _hls_seek(imihls_client_ffmpeg_info_t handle, unsigned long long timestamp)
{
	int ret = IMIMEDIA_OK;
	int64_t time = 0;
	printf("imihls_client_seek av_seek_frame timestamp = %lld\n", timestamp);
	time = (int64_t)(((double)(timestamp)/(double)1000)*AV_TIME_BASE + (double)handle->_format_context->start_time);
	ret = av_seek_frame(handle->_format_context, -1, time, AVSEEK_FLAG_BACKWARD);//AVSEEK_FLAG_BACKWARD
	if (ret < 0) {
		ffmpeg_err2str("imihls_client_seek av_seek_frame", ret);
		return ret;
	}
	return IMIMEDIA_OK;
}

int imihls_client_seek(imihls_client_ffmpeg_info_t handle, unsigned long long timestamp)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _hls_seek(handle, timestamp);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _hls_close(imihls_client_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	if (handle->_audio_bsfc) {
		av_bsf_free(&handle->_audio_bsfc);
		handle->_audio_bsfc = NULL;
	}
	if (handle->_video_stream) {
		avcodec_close(handle->_video_stream->codec);
		handle->_video_stream->codec->extradata = NULL;
		handle->_video_stream->codec->extradata_size = 0;
		handle->_video_stream = NULL;
	}
	if (handle->_audio_stream) {
		avcodec_close(handle->_audio_stream->codec);
		handle->_audio_stream->codec->extradata = NULL;
		handle->_audio_stream->codec->extradata_size = 0;
		handle->_audio_stream = NULL;
	}
	avformat_close_input(&handle->_format_context);
	handle->_format_context = NULL;
	if (handle->_audio_extradata) {
		free(handle->_audio_extradata);
		handle->_audio_extradata = NULL;
	}
	if (handle->_video_extradata) {
		free(handle->_video_extradata);
		handle->_video_extradata = NULL;
	}
	handle->_video_frame_index = 0;
	handle->_video_fps = 0;
	handle->_video_lasttimestamp = 0;
	handle->_video_lastpts = 0;
	handle->_getfirstframe = 0;
	printf("imihls_client_teardown success\n");
	return ret;
}

int imihls_client_close(imihls_client_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	printf("imihls_client_teardown handle = %x\n", handle);
	ret = _hls_close(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	pthread_mutex_destroy(&handle->_lock_mutex);
	free(handle);
	return ret;
}