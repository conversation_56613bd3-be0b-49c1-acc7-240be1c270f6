#pragma once

#define inline __inline
#define bool int
#define true 1
#define false 0

typedef int (*imimusic_callback_play_pcm)(const char* pcm, unsigned int pcm_len, unsigned int achannels, unsigned int asamplerate, unsigned int id, void* userdata);

void imimusic_init_ffmpeg();

int imimusic_open_file(const char* path,
					   unsigned int* achannel,
					   unsigned int* asamplerate,
					   unsigned int* abitrate,
					   unsigned int* duration);

int imimusic_get_one_frame(unsigned char* data,
						   unsigned int* data_len,
						   unsigned int* timestamp,
						   unsigned int* nb_samples,
						   float volume);

int imimusic_close_file();

int imimusic_transmp3_init();

int imimusic_transmp3_fini();

int imimusic_transmp3(unsigned char* data, unsigned int data_len, imimusic_callback_play_pcm fproc, unsigned int id, void* userdata);

int imimusic_resample_init(unsigned int in_achannel, unsigned int in_asamplerate,
						   unsigned int out_achannel, unsigned int out_asamplerate);

int imimusic_resample_fini();

int imimusic_resample_data(unsigned char* in_data, unsigned int in_data_len, unsigned int in_nb_samples,
						   unsigned char* out_data, unsigned int* out_data_len);