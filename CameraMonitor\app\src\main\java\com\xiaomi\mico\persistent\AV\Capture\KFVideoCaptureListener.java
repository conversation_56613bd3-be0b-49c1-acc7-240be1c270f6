package com.xiaomi.mico.persistent.AV.Capture;
//
//  KFVideoCaptureListener
//  KFAVDemo
//  微信搜索『gzjkeyframe』关注公众号『关键帧Keyframe』获得最新音视频技术文章和进群交流。
//  Created by [公众号：关键帧Keyframe] on 2021/12/28.
//

import com.xiaomi.mico.persistent.AV.Base.KFFrame;

public interface KFVideoCaptureListener {
    ///< 摄像机打开
    void cameraOnOpened();
    ///< 摄像机关闭
    void cameraOnClosed();
    ///< 摄像机出错
    void cameraOnError(int error,String errorMsg);
    ///< 数据回调给外层
    void onFrameAvailable(KFFrame frame);
}
