package com.xiaomi.camera.monitoring.entity;

public class VideoFrameData {
    public long index;
    public byte[] dataBytes;

    public boolean isIFrame = false;
    public int length = 0;
    public long timeStamp;

    public VideoFrameData(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public void setDataLen(int length) {
        this.length = length;
    }

    public void setIFrame(boolean b) {
        isIFrame = b;
    }
}
