package com.xiaomi.mico.persistent.cloud;

import com.xuhao.didi.core.iocore.interfaces.ISendable;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class SendStopRecord implements ISendable {

    private byte type = 0x03;

    @Override
    public byte[] parse() {
        int length = 4 + 1;
        ByteBuffer bb = ByteBuffer.allocate(length);
        bb.order(ByteOrder.BIG_ENDIAN);
        bb.putInt(length - 4);
        bb.put(type);
        return bb.array();
    }
}
