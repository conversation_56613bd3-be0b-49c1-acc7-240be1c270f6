package com.xiaomi.mico.persistent.utils;

import android.app.ActivityManager;
import android.content.Context;

import com.xiaomi.camera.monitoring.utils.L;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.List;

public class VDecUtils {
    private static final String TAG = "VDecUtils";

    public static final String CAT_VIDEO_DEC_INFO = "cat /sys/class/vdec/core";

    // 音频列表
    public static final String PACKAGE_NAME_LAUNCHER = "com.xiaomi.micolauncher"; // Launcher包名
    public static final String PACKAGE_NAME_LAUNCHER_MUSIC = "com.xiaomi.micolauncher.skills.music.view_v2.PlayerActivityV2"; // Launcher音乐
    public static final String PACKAGE_NAME_CLOUD_MUSIC = "com.netease.cloudmusic.iot"; // 网易云

    public static final String PACKAGE_NAME_MICO_VOIP = "com.xiaomi.micovoip";
    public static final String PACKAGE_NAME_MICO_VOIP_CLASS1 = "com.xiaomi.mico.call.page.XiaoMiCallActivity";
    public static final String PACKAGE_NAME_MICO_VOIP_CLASS2 = "com.example.wxvoip.page.WeChatCallActivity";
    public static final String PACKAGE_NAME_MONITOR_VOIP = "com.xiaomi.mico.persistent.monitor";
    public static final String PACKAGE_NAME_MONITOR_CLASS = "com.xiaomi.mico.persistent.voip.MiVoipActivity";

    // 执行 shell 命令
    public static String executeShellCommand(String command) {
        StringBuilder output = new StringBuilder();
        Process process = null;
        BufferedReader reader = null;

        try {
            // 执行 shell 命令
            process = Runtime.getRuntime().exec(command);
            reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

            // 读取命令输出
            String line;
            while ((line = reader.readLine()) != null) {
                output.append(line).append("\n");
            }

            // 等待命令执行完成
            process.waitFor();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            if (reader != null) {
                try {
                    reader.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            if (process != null) {
                process.destroy();
            }
        }

        return output.toString();
    }

    // 检查解码器状态, 0没有解码, >0 有解码
    public static int checkVDECStatus(String vdecContent) {
        if (vdecContent == null || vdecContent.isEmpty()) {
            L.monitor.e("%s No vdec content found.", TAG);
            return 0;
        }

        // 判断是否有解码任务
        if (vdecContent.contains("connected vdec list empty")) {
            return 0;
        }

        // 统计解码器数量
        int vdecCount = 0;
        String[] lines = vdecContent.split("\n");
        for (String line : lines) {
            if (line.trim().startsWith("vdec.")) {
                vdecCount++;
            }
        }
        return vdecCount;
    }

    // 获取当前最上层的 Activity
    public static String getTopActivity(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager == null) {
            return null;
        }

        List<ActivityManager.RunningTaskInfo> tasks = activityManager.getRunningTasks(1);
        if (tasks != null && !tasks.isEmpty()) {
            // 获取最上层的任务
            ActivityManager.RunningTaskInfo topTask = tasks.get(0);
            if (topTask != null && topTask.topActivity != null) {
                String packageName = topTask.topActivity.getPackageName();
                if (packageName.equals(PACKAGE_NAME_LAUNCHER)
                        || packageName.equals(PACKAGE_NAME_MICO_VOIP)
                        || packageName.equals(PACKAGE_NAME_MONITOR_VOIP)) {
                    return topTask.topActivity.getClassName();
                } else {
                    return topTask.topActivity.getPackageName();
                }
            }
        }
        return null;
    }

    public static String getClassName(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (activityManager == null) {
            return null;
        }

        List<ActivityManager.RunningTaskInfo> tasks = activityManager.getRunningTasks(1);
        if (tasks != null && !tasks.isEmpty()) {
            // 获取最上层的任务
            ActivityManager.RunningTaskInfo topTask = tasks.get(0);
            if (topTask != null && topTask.topActivity != null) {
                return topTask.topActivity.getClassName();
            }
        }
        return null;
    }
}
