package com.argusak.media.core;

import android.util.Log;

import com.argusak.media.core.constant.AKMediaCoreConstant;

import java.util.Arrays;

public class AkDecodeFrame {
    private static final String TAG = "AkDecodeFrame";

    static {
        System.loadLibrary("ffmpeg-org");
        System.loadLibrary("media_core");
    }

    private static long mHandlerId;

    public static void initDecoder() {
        mHandlerId = initDecoder(AKMediaCoreConstant.VideoCodecId.VIDEO_CODEC_H265, 1, 640, 480);
    }

    public static void decodeOneFrame(byte[] inputData, int inputLen, byte[] out, int outLen) {
//        long handle = initDecoder(AKMediaCoreConstant.VideoCodecId.VIDEO_CODEC_H265, 1, 640, 480);
        int ret = decodeFrame(mHandlerId, inputData, inputLen, out, outLen);
        Log.i(TAG, "decodeOneFrame:" + ret + " -->out:" + Arrays.toString(out));
    }

    public static void closeDecoder() {
        closeDecoder(mHandlerId);
    }

    private static native long initDecoder(int codec, int format, int width, int height);

    private static native int decodeFrame(long handle, byte[] inputData, int inputLen, byte[] out, int outLen);

    private static native int closeDecoder(long handle);
}
