package com.xiaomi.mico.persistent.cloud;

import android.content.Context;

public abstract class MotionDetection {

    private static volatile MotionDetection sInstance;
    public static MotionDetection getInstance() {
        if (sInstance == null) {
            synchronized (MotionDetection.class) {
                if (sInstance == null) {
                    sInstance = new MotionDetectionLocal();
                }
            }
        }
        return sInstance;
    }

    public abstract void initMotionDetection(Context context);
    public abstract void initStatus();
    public abstract boolean isRecording();
    public abstract boolean isVip();
    public abstract void uploadVideo();
    public abstract void updatePowerStatus(boolean status);
    public abstract void updateMDConfig();
    public abstract void updateAIConfig();
    public abstract void updateCloudSwitch();
    public abstract void updateSystemUpdate();
}
