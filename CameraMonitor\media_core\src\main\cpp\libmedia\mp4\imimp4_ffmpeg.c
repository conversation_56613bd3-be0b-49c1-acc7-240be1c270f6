#include <stdlib.h>
#include <string.h>

#ifndef WIN32

#include <pthread.h>
#include <../log.h>

#else
#include "imimedia_win2linux.h"
#endif

#include "imimp4_ffmpeg.h"
#include "imiffmpeg_common.h"
#include "imiparser_aac.h"
#include "libavcodec/avcodec.h"
#include "libavcodec/bsf.h"
#include "libavutil/avstring.h"

#if defined(WIN32) && !defined(__cplusplus)
#define inline __inline
#endif

#ifdef __cplusplus
extern "C"{
#endif

#include <libavformat/avformat.h>
#include <libavformat/avio.h>
#include <libavutil/mem.h>

#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#endif

#define MODULE_IMIMP4_FFMPEG "imimp4ffmpeg"

typedef struct imimp4_ffmpeg_info_s {
#ifndef WIN32
    pthread_mutex_t _lock_mutex;
#endif
    int _getfirstframe;
    timestamp_correct_id _correct;
    video_codec_id _video_codec;
    audio_codec_id _audio_codec;
    AVFormatContext *_format_context;
    AVStream *_video_stream;
    unsigned char *_video_extradata;
    unsigned int _video_extradata_size;
    unsigned int _video_frame_index;
    unsigned int _video_fps;
    unsigned long long _video_lasttimestamp;
    int64_t _video_lastpts;
    int64_t _video_duration;
    AVBitStreamFilter *_video_bsfc;
    AVBSFContext *bsf_ctx;
    AVDictionary *_video_opts;
    AVStream *_audio_stream;
    unsigned char *_audio_extradata;
    unsigned int _audio_extradata_size;
    unsigned long long _audio_lasttimestamp;
    unsigned int _audio_frame_index;
    int64_t _audio_duration;
    unsigned long long _audio_lasttimems;
} imimp4_ffmpeg_info_s, *imimp4_ffmpeg_info_t;

void imi_mp4_init_ffmpeg() {
    LOGD("imi_mp4_init_ffmpeg\n");
    avformat_network_init();
}

#define AIDLK_AUDIO_OPUS_HEAD_LEN 19
typedef struct {
    uint8_t magic[8];
    uint8_t version;
    uint8_t channels;
    uint16_t pre_skip;
    uint32_t sample_rate;
    uint16_t output_gain;
    uint8_t map;
} OPUS_HEAD_T;

uint32_t big_to_little_endian(uint32_t x) { return ((x & 0xFF) << 24) | ((x & 0xFF00) << 8) | ((x & 0xFF0000) >> 8) | ((x & 0xFF000000) >> 24); }

static uint8_t* aidlk_make_opus_extra_data(int sample_rate)
{
    uint8_t* opus_extra_data = NULL;
    opus_extra_data = malloc(AIDLK_AUDIO_OPUS_HEAD_LEN);
#if 0
    // 可以参见 opus 头结构
	uint8_t opus_head[AIDLK_AUDIO_OPUS_HEAD_LEN]  = {
		'O', 'p', 'u', 's', 'H', 'e', 'a', 'd',
		 0x1, 0x1, 0x0, 0x0,
//		 0x8, 0x0, 0x3, 0xE,
		 0x0, 0x0, 0x0, 0x0,
		 0x0, 0x0, 0x0,
	};
#else
    OPUS_HEAD_T opus_head;
    memset(&opus_head, 0x0, sizeof(OPUS_HEAD_T));
    opus_head.magic[0] = 'O';
    opus_head.magic[1] = 'p';
    opus_head.magic[2] = 'u';
    opus_head.magic[3] = 's';
    opus_head.magic[4] = 'H';
    opus_head.magic[5] = 'e';
    opus_head.magic[6] = 'a';
    opus_head.magic[7] = 'd';
    opus_head.version = 1;
    opus_head.channels = 1;
    opus_head.pre_skip = 0;
    opus_head.sample_rate = big_to_little_endian(sample_rate);
#endif

    memcpy(opus_extra_data, &opus_head, 19);
    return opus_extra_data;
}

#define AIDLK_SEI "AIDLK_SEI_MAGIC"
uint8_t *create_sei_nal_h264(unsigned long long timestamp, int *sei_size)
{
    int sei_data_len = sizeof(unsigned long long);
    *sei_size = strlen(AIDLK_SEI) + sei_data_len + 8;  // 4字节的起始码 + 1字节的SEI NAL header + 1字节payloadtype + 数据长度+ 数据+ End-of-payload(0x80)

    uint8_t *sei_nal = (uint8_t *)malloc(*sei_size);
    sei_nal[0] = 0x00;  // 起始码
    sei_nal[1] = 0x00;  // 起始码
    sei_nal[2] = 0x00;  // 起始码
    sei_nal[3] = 0x01;  // 起始码

    sei_nal[4] = 0x06;  // H.264 NAL header (type 06 表示 SEI)
    sei_nal[5] = 0x05;
    sei_nal[6] = sei_data_len + strlen(AIDLK_SEI);
    memcpy(&sei_nal[7], AIDLK_SEI, strlen(AIDLK_SEI));
    memcpy(&sei_nal[7 + strlen(AIDLK_SEI)], &timestamp, sei_data_len);
    sei_nal[*sei_size - 1] = 0x80;  // SEI end-of-payload 字节

    return sei_nal;
}

uint8_t *create_sei_nal_h265(unsigned long long timestamp, int *sei_size)
{
    int sei_data_len = sizeof(unsigned long long);
    *sei_size = strlen(AIDLK_SEI) + sei_data_len + 9;  // 4字节的起始码 + 2字节的SEI NAL header + 1字节payload_type +  1字节数据长度(magic + data) + 数据 + End-of-payload(0x80)

    uint8_t *sei_nal = (uint8_t *)malloc(*sei_size);
    sei_nal[0] = 0x00;  // 起始码
    sei_nal[1] = 0x00;  // 起始码
    sei_nal[2] = 0x00;  // 起始码
    sei_nal[3] = 0x01;  // 起始码

    sei_nal[4] = 0x4e;  // H.265 NAL header (type 39 表示 SEI)
    sei_nal[5] = 0x01;
    sei_nal[6] = 0x05;
    sei_nal[7] = sei_data_len + strlen(AIDLK_SEI);
    memcpy(&sei_nal[8], AIDLK_SEI, strlen(AIDLK_SEI));
    memcpy(&sei_nal[8 + strlen(AIDLK_SEI)], &timestamp, sei_data_len);
    sei_nal[*sei_size - 1] = 0x80;  // SEI end-of-payload 字节

    return sei_nal;
}

static uint8_t *aidlk_create_sei_nal(video_codec_id video_codec, unsigned long long timestamp, int *sei_size)
{
    uint8_t *aidlk_sei_nal = NULL;
    switch (video_codec) {
        case video_codec_h264: {
            aidlk_sei_nal = create_sei_nal_h264(timestamp, sei_size);
            break;
        }
        case video_codec_h265: {
            aidlk_sei_nal = create_sei_nal_h265(timestamp, sei_size);
            break;
        }
        default: {
            ffmpeg_err2str("unsupport video_codec:%d", video_codec);
            break;
        }
    }
    return aidlk_sei_nal;
}

int _create_file_inner(imimp4_ffmpeg_info_t handle,
                       const char *path,
                       container_format_id container,
                       timestamp_correct_id correct,
                       video_codec_id video,
                       audio_codec_id audio,
                       unsigned int vfps,
                       unsigned int vwidth,
                       unsigned int vheight,
                       unsigned int achannel,
                       unsigned int asamplerate,
                       unsigned int duration,
                       unsigned long cur_time) {
    LOGD("av_version_info = %s,avcodec_version = %d\n", av_version_info(), avcodec_version());
    const char *container_ext = NULL;
    int ret = IMIMEDIA_OK;
    time_t now_time = 0;
    struct tm *tm_time;
    char date[32] = {0};
    if (handle == 0) return IMIMEDIA_PARAMS_ERROR;//ASSERT WARNING
    //////////////////////////////////////////////////////////////////////////
    handle->_video_opts = NULL;
    LOGD("imi_mp4_create_file container_format_id default = %d\n", container);
    switch (container) {
        case container_format_mp4: {
            container_ext = "mp4";
        }
            break;
        case container_format_fragmented_mp4: {
            container_ext = "mp4";
            char latency[32] = {0};
            char muxdlay[16] = {0};
            char creation_time[64] = {0};
            snprintf(latency, sizeof(latency), "%d", 60000);
            time_t time_now = time(NULL);
            struct tm *tm_info = gmtime(&time_now);

            // 2015-12-25T12:34:56
            strftime(creation_time, sizeof(creation_time), "creation_time=\"%Y-%m-%dT%H:%M:%S\"",
                     tm_info);
            LOGD("creation_time = %s", creation_time);
            av_dict_set(&handle->_video_opts, "max_interleave_delta", latency, 0);
            //			sprintf(muxdlay, "%d", 1000000);
            //			av_dict_set(&handle->_video_opts, "muxdlay", muxdlay, 0);

            av_dict_set(&handle->_video_opts, "movflags", "frag_keyframe+empty_moov", 0);
            av_dict_set(&handle->_video_opts, "fflags", "flush_packets", 0);
            av_dict_set(&handle->_video_opts, "creation_time", creation_time, 0);
            av_dict_set(&handle->_video_opts, "metadata", creation_time, 0);
        }
            break;
        default:
            LOGD("imi_mp4_create_file container_format_id default = %d\n", container);
            return IMIMEDIA_PARAMS_ERROR;
    }
    LOGD("imi_mp4_create_file av_guess_format  container_ext = %s\n", container_ext);
    ret = avformat_alloc_output_context2(&handle->_format_context, NULL, container_ext, path);
    if (ret < 0) {
        LOGE("imi_mp4_create_file avformat_alloc_output_context2 error ret = %d\n, path = %s", ret,
             path);
        goto error;
    }
    av_strlcpy(handle->_format_context->url, path, sizeof(handle->_format_context->url));

    handle->_video_stream = avformat_new_stream(handle->_format_context, NULL);
    if (handle->_video_stream == NULL) {
        LOGE("imimp4_create_file avformat_new_stream video error\n");
        goto error;
    }
    handle->_video_stream->id = handle->_format_context->nb_streams - 1;
    handle->_video_stream->codecpar->codec_type = AVMEDIA_TYPE_VIDEO;
    switch (video) {
        case video_codec_h264:
            handle->_video_stream->codecpar->codec_id = AV_CODEC_ID_H264;
            handle->_video_stream->codecpar->level = 0x7F;
            break;
        case video_codec_h265:
            handle->_video_stream->codecpar->codec_id = AV_CODEC_ID_H265;
            handle->_video_stream->codecpar->codec_tag = MKTAG('h', 'v', 'c', '1');  // ios 必须要设置，不然不能播放
            break;
        default:
            LOGD("imi_mp4_create_file video_codec_id default = %d\n", video);
            goto error;
    }
    handle->_video_stream->codecpar->width = vwidth;
    handle->_video_stream->codecpar->height = vheight;
    handle->_video_stream->time_base = av_d2q(1.0 / vfps, 255);
    handle->_video_stream->r_frame_rate = av_d2q(vfps, 255);
    handle->_video_stream->time_base.num = 1;
    handle->_video_stream->time_base.den = IMI_VIDEO_TIME_BASE;
    handle->_video_fps = vfps;
    handle->_video_duration = ((duration / 1000) * handle->_video_stream->time_base.den);
    //////////////////////////////////////////////////////////////////////////
    handle->_audio_stream = avformat_new_stream(handle->_format_context, NULL);
    if (handle->_audio_stream == NULL) {
        LOGE("imi_mp4_create_file avformat_new_stream audio error\n");
        goto error;
    }
    handle->_audio_stream->id = handle->_format_context->nb_streams - 1;
    handle->_audio_stream->codecpar->codec_type = AVMEDIA_TYPE_AUDIO;
    switch (audio) {
        case audio_codec_g711a: {
            handle->_audio_stream->codecpar->codec_id = AV_CODEC_ID_PCM_ALAW;
            handle->_audio_stream->codecpar->sample_rate = asamplerate;
            handle->_audio_stream->codecpar->ch_layout.nb_channels = achannel;
        }
            break;
        case audio_codec_opus: {
            handle->_audio_stream->codecpar->codec_id = AV_CODEC_ID_OPUS;
            handle->_audio_stream->codecpar->sample_rate = asamplerate;
            handle->_audio_stream->codecpar->frame_size = 16;  // 400;
            //       handle->_audio_stream->codecpar->bits_per_raw_sample = 16;
            handle->_audio_stream->codecpar->ch_layout.nb_channels = achannel;
            //			handle->_audio_stream->codecpar->ch_layout.order = AV_CHANNEL_ORDER_NATIVE;
            handle->_audio_extradata = aidlk_make_opus_extra_data(asamplerate);
            handle->_audio_extradata_size = AIDLK_AUDIO_OPUS_HEAD_LEN;
            handle->_audio_stream->codecpar->extradata = handle->_audio_extradata;
            handle->_audio_stream->codecpar->extradata_size = handle->_audio_extradata_size;
        }
            break;
        case audio_codec_aac: {
            handle->_audio_stream->codecpar->codec_id = AV_CODEC_ID_AAC;
            handle->_audio_stream->codecpar->sample_rate = asamplerate;
            handle->_audio_stream->codecpar->ch_layout.nb_channels = achannel;
            handle->_audio_stream->codecpar->profile = FF_PROFILE_AAC_LOW;
            handle->_audio_stream->codecpar->level = 0x02;
            handle->_audio_extradata = imi_make_aac_track_configure(
                    handle->_audio_stream->codecpar->level,
                    imi_make_aac_header_sampling_frequency_index(
                            handle->_audio_stream->codecpar->sample_rate),
                    handle->_audio_stream->codecpar->ch_layout.nb_channels);
            handle->_audio_extradata_size = 2;
            handle->_audio_stream->codecpar->extradata = handle->_audio_extradata;
            handle->_audio_stream->codecpar->extradata_size = handle->_audio_extradata_size;
            handle->_audio_stream->codecpar->frame_size = 1024;
        }
            break;
        default:
            LOGD("imi_mp4_create_file audio_codec_id default = %d\n", audio);
            goto error;
    }
    handle->_audio_stream->time_base.num = 1;
    handle->_audio_stream->time_base.den = asamplerate;
    handle->_audio_duration = duration;
    //////////////////////////////////////////////////////////////////////////
    now_time = cur_time;
    tm_time = localtime(&now_time);
//    tm_time = gmtime(&now_time);
    strftime(date, sizeof(date), "%Y-%m-%d %H:%M:%S", tm_time);
    LOGD("imi_mp4_create_file metadata creation_time = %s now_time = %ld\n", date, now_time);
    av_dict_set(&handle->_format_context->metadata, "creation_time", date, 0);
    av_dict_set(&handle->_format_context->metadata, "truncate", "0", 0);  //设置这个标志位为0，文件不会被清空

    //////////////////////////////////////////////////////////////////////////
    av_dump_format(handle->_format_context, 0, path, 1);
    ret = avio_open(&handle->_format_context->pb, path, AVIO_FLAG_WRITE);
    if (ret < 0) {
        ffmpeg_err2str("imi_mp4_create_file avio_open", ret);
        goto error;
    }
    //////////////////////////////////////////////////////////////////////////
    handle->_video_codec = video;
    handle->_video_extradata_size = 0;
    handle->_video_extradata = (unsigned char *) malloc(4096);
    memset(handle->_video_extradata, 0, 4096);
    handle->_audio_codec = audio;
    handle->_correct = correct;
    LOGD("imi_mp4_create_file success\n");
    return IMIMEDIA_OK;

    error:
    if (handle->_video_stream) {
//        avcodec_close(handle->_video_stream);
        handle->_video_stream->codecpar->extradata = NULL;
        handle->_video_stream->codecpar->extradata_size = 0;
        handle->_video_stream = NULL;
    }
    if (handle->_audio_stream) {
//        avcodec_close(handle->_audio_stream->codec);
        handle->_audio_stream->codecpar->extradata = NULL;
        handle->_audio_stream->codecpar->extradata_size = 0;
        handle->_audio_stream = NULL;
    }
    avio_close(handle->_format_context->pb);
    handle->_format_context->pb = NULL;
    if (handle->_format_context->metadata) {
        av_dict_free(&handle->_format_context->metadata);
        handle->_format_context->metadata = NULL;
    }
    avformat_free_context(handle->_format_context);
    handle->_format_context = NULL;
    if (handle->_audio_extradata) {
        free(handle->_audio_extradata);
        handle->_audio_extradata = NULL;
    }
    return IMIMEDIA_CAPABILITY_ERROR;
}

int imi_mp4_create_file(const char *path,
                        container_format_id container,
                        timestamp_correct_id correct,
                        video_codec_id video,
                        audio_codec_id audio,
                        unsigned int vfps,
                        unsigned int vwidth,
                        unsigned int vheight,
                        unsigned int achannel,
                        unsigned int asamplerate,
                        unsigned int duration,
                        unsigned long cur_time,
                        /*out*/imimp4_ffmpeg_info_t *handle) {
    int ret = IMIMEDIA_OK;
    imimp4_ffmpeg_info_t handle_impl = NULL;
    handle_impl = (imimp4_ffmpeg_info_t) malloc(sizeof(imimp4_ffmpeg_info_s));
    if (handle_impl == NULL) {
        LOGE("imi_mp4_create_file malloc error\n");
        return IMIMEDIA_RESOURCE_ERROR;
    }
    memset(handle_impl, 0, sizeof(imimp4_ffmpeg_info_s));
    pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
    pthread_mutex_lock(&handle_impl->_lock_mutex);
    ret = _create_file_inner(handle_impl, path, container, correct, video, audio, vfps, vwidth,
                             vheight, achannel, asamplerate, duration, cur_time);
    if (ret != 0) {
        pthread_mutex_unlock(&handle_impl->_lock_mutex);
        pthread_mutex_destroy(&handle_impl->_lock_mutex);
        free(handle_impl);
        *handle = NULL;
        return ret;
    }
    *handle = handle_impl;
    LOGD("imi_mp4_create_file handle = %x\n", *handle);
    pthread_mutex_unlock(&handle_impl->_lock_mutex);
    return ret;
}

int _write_video_frame_inner(imimp4_ffmpeg_info_t handle,
                             unsigned char *data,
                             unsigned int data_len,
                             unsigned long long vtimestamp,
                             frame_type_id frametype,
                             unsigned long long timestamp_ms_utc) {
    int key = 0;
    unsigned int offset = 0;
    AVPacket pkt = {0};
    imi_nalu_array_t nalu_array = NULL;
    unsigned char *data_buff_src = data;
    if (handle->_video_stream == NULL) {
        LOGE("imi_mp4_write_video_frame video_stream is null\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    nalu_array = imi_get_nalu_array(data_buff_src, data_len);
    if (nalu_array == NULL) {
        LOGE("imi_mp4_write_video_frame get_nalu_info error data_buff_src = %s,data_len = %d\n",
             data_buff_src, data_len);
        return IMIMEDIA_STATUS_ERROR;
    }
    handle->_video_extradata_size = 0;
    switch (handle->_video_codec) {
        case video_codec_h264:
            key = ffmpeg_get_extradata_h264(nalu_array, handle->_video_extradata,
                                            &handle->_video_extradata_size, &offset);
            break;
        case video_codec_h265:
            key = ffmpeg_get_extradata_h265(nalu_array, handle->_video_extradata,
                                            &handle->_video_extradata_size, &offset);
            break;
        default:
            LOGD("imi_mp4_write_video_frame video_codec_id default = %d\n", handle->_video_codec);
            imi_free_nalu_array(nalu_array);
            return IMIMEDIA_PARAMS_ERROR;
    }
    imi_free_nalu_array(nalu_array);
    if (key == 0 && handle->_getfirstframe == 0) {
        LOGE("imi_mp4_write_video_frame getfirstframe is not key frame\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->_getfirstframe == 0) {
        int ret = IMIMEDIA_OK;
        handle->_video_stream->codecpar->extradata = handle->_video_extradata;
        handle->_video_stream->codecpar->extradata_size = handle->_video_extradata_size;
        ret = avformat_write_header(handle->_format_context, &handle->_video_opts);
        if (ret < 0) {
            ffmpeg_err2str("imi_mp4_write_video_frame avformat_write_header", ret);
            return IMIMEDIA_CAPABILITY_ERROR;
        } else {
#if 0
            int64_t write_header_len = avio_tell(handle->_format_context->pb);
            if (write_header_len > 0) {
                LOGD("write_header_len = %lld", write_header_len);
            } else {
                LOGD("failed write_header_len = %lld", write_header_len);
            }
#endif
        }
        handle->_getfirstframe = 1;
    }
//    av_init_packet(&pkt);
    av_packet_unref(&pkt);
    if (key == 1) {
        pkt.flags |= AV_PKT_FLAG_KEY;
    }

    uint8_t* keyframe_buf = NULL;
    //添加sei nalu到裸流里面
    if (key && timestamp_ms_utc != 0) {
        LOGD("key frame add sei after idr");
        int sei_nal_size = 0;
        uint8_t* sei_nal = aidlk_create_sei_nal(handle->_video_codec, timestamp_ms_utc, &sei_nal_size);
        keyframe_buf = malloc(data_len - offset + sei_nal_size);
        if (keyframe_buf == NULL) {
            LOGE("aidlk_mp4_write_video_frame malloc failed , size:%d\n", data_len - offset + sei_nal_size);
            if (sei_nal) {
                free(sei_nal);
                sei_nal = NULL;
            }
            return IMIMEDIA_STATUS_ERROR;
        }
        memcpy(keyframe_buf, sei_nal, sei_nal_size);
        memcpy(keyframe_buf + sei_nal_size, data_buff_src + offset, data_len - offset);
        pkt.data = keyframe_buf;
        pkt.size = data_len - offset + sei_nal_size;
        free(sei_nal);
    } else {
        pkt.data = (uint8_t*)data_buff_src + offset;
        pkt.size = data_len - offset;
    }
    if (vtimestamp == 0) {
        pkt.pts = av_rescale(handle->_video_frame_index++,
                             handle->_video_stream->time_base.den,
//                             handle->_video_stream->time_base.den);
                             AV_TIME_BASE);
        pkt.duration = handle->_video_stream->time_base.den / handle->_video_fps;
    } else {
        if (handle->_video_lasttimestamp == 0) {
            pkt.pts = av_rescale(handle->_video_frame_index++,
                                 handle->_video_stream->time_base.den,
//                                 handle->_video_stream->time_base.den);
                                 AV_TIME_BASE);
            pkt.duration = handle->_video_stream->time_base.den / handle->_video_fps;
        } else {
            int64_t time = IMI_PTS2TIME_SCALE(vtimestamp, handle->_video_lasttimestamp,
                                              handle->_video_stream->time_base.den);
            if (time >= handle->_video_stream->time_base.den &&
                handle->_correct == timestamp_correct_enable) {
                time = IMI_VIDEO_TIME_BASE / handle->_video_fps;
            }
            pkt.pts = handle->_video_lastpts + time;
            pkt.duration = time;
        }
        handle->_video_lasttimestamp = vtimestamp;
    }
#if 0
    if (handle->_video_duration != 0 && pkt.pts >= handle->_video_duration) {
        int64_t pts_diff = handle->_video_duration - handle->_video_lastpts;
        if (pts_diff > 0) {
            pkt.pts = handle->_video_duration;
            //			pkt.duration = pts_diff;
        } else {
            return IMIMEDIA_EOF;
        }
    }
#endif
    if (pkt.pts != 0 && pkt.pts <= handle->_video_lastpts) {
        LOGE("pts error, lastpts = %llu, pts = %llu", handle->_video_lastpts, pkt.pts);
        pkt.pts += 20;
    }
    handle->_video_lastpts = pkt.pts;
    pkt.stream_index = handle->_video_stream->index;
    pkt.dts = pkt.pts;
    pkt.pos = -1;

    LOGD("_write_video_frame_inner pkt.pts: %lld, pkt.dts %lld pkt.duration %lld\n", pkt.pts, pkt.dts, pkt.duration);
    int ret = av_interleaved_write_frame(handle->_format_context, &pkt);
	if (key && keyframe_buf) {
        free(keyframe_buf);
        keyframe_buf = NULL;
    }
    if (ret < 0) {
        ffmpeg_err2str("imi_mp4_write_video_frame av_interleaved_write_frame", ret);
        return IMIMEDIA_STATUS_ERROR;
    }
//    av_packet_unref(&pkt);
    return ret;
}

int imi_mp4_write_video_frame(imimp4_ffmpeg_info_t handle,
                              unsigned char *data,
                              unsigned int data_len,
                              unsigned long long vtimestamp,
                              frame_type_id frametype,
                              unsigned long long timestamp_ms_utc) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _write_video_frame_inner(handle, data, data_len, vtimestamp, frametype, timestamp_ms_utc);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _write_audio_frame_inner(imimp4_ffmpeg_info_t handle,
                             unsigned char *data,
                             unsigned int data_len,
                             unsigned long long atimestamp) {
    AVPacket pkt = {0};
    int one_frame_ms;
    unsigned char *data_buff_src = (unsigned char *) data;
    if (handle->_audio_stream == NULL) {
        LOGE("imi_mp4_write_audio_frame audio_stream is null\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->_getfirstframe == 0) {
        LOGE("imi_mp4_write_audio_frame doesn't' get first keyframe\n");
        return IMIMEDIA_STATUS_ERROR;
    }
//    av_init_packet(&pkt);
    av_packet_unref(&pkt);
    pkt.flags |= AV_PKT_FLAG_KEY;
    switch (handle->_audio_codec) {
        case audio_codec_aac: {
            int header_len = AAC_ADTS_HEADER;
            pkt.data = (uint8_t *) data_buff_src + header_len;
            pkt.size = data_len - header_len;
            pkt.duration = handle->_audio_stream->codecpar->frame_size;
            one_frame_ms = handle->_audio_stream->codecpar->frame_size * 1000 / handle->_audio_stream->codecpar->sample_rate;
        }
            break;
        case audio_codec_g711a: {
            pkt.data = (uint8_t *) data_buff_src;
            pkt.size = data_len;
            pkt.duration = data_len;
            one_frame_ms = data_len / handle->_audio_stream->codecpar->sample_rate * 1000;
        }
            break;
        case audio_codec_opus: {
            pkt.data = (uint8_t*)data_buff_src;
            pkt.size = data_len;
            //   pkt.duration = 40000; //40ms
            one_frame_ms = 40000;  // data_len / handle->_audio_stream->codecpar->sample_rate * 1000;
        }
            break;
        default:
            LOGE("imi_mp4_write_audio_frame audio_codec_id default = %d\n", handle->_audio_codec);
            return IMIMEDIA_PARAMS_ERROR;
    }
    if (handle->_audio_duration != 0 &&
        handle->_audio_lasttimems >= (unsigned long long) handle->_audio_duration) {
        return IMIMEDIA_EOF;
    }
    //	handle->_audio_lasttimems = handle->_audio_lasttimems + one_frame_ms;
    handle->_audio_frame_index++;
    pkt.stream_index = handle->_audio_stream->index;
    pkt.pos = -1;
#if 0
    pkt.pts = (handle->_audio_lasttimestamp + pkt.duration)*8;
    pkt.dts = pkt.pts;
    handle->_audio_lasttimestamp = (unsigned long long)pkt.pts/8;
#else
    pkt.pts = handle->_audio_lasttimestamp;
    pkt.dts = handle->_audio_lasttimestamp;
    handle->_audio_lasttimestamp +=
            one_frame_ms * handle->_audio_stream->codecpar->sample_rate / AV_TIME_BASE;
#endif
    LOGD("_write_audio_frame_inner pkt.pts: %lld, pkt.dts %lld\n", pkt.pts, pkt.dts);
    int ret = av_interleaved_write_frame(handle->_format_context, &pkt);
    if (ret < 0) {
        ffmpeg_err2str("_write_audio_frame_inner av_interleaved_write_frame", ret);
        return IMIMEDIA_STATUS_ERROR;
    }
//    av_packet_unref(&pkt);
    return ret;
}

int imi_mp4_write_audio_frame(imimp4_ffmpeg_info_t handle,
                              unsigned char *data,
                              unsigned int data_len,
                              unsigned long long atimestamp) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _write_audio_frame_inner(handle, data, data_len, atimestamp);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _close_file_for_create_inner(imimp4_ffmpeg_info_t handle) {
    int ret = IMIMEDIA_OK;
    if (handle->_getfirstframe == 1) {
        ret = av_write_trailer(handle->_format_context);
        if (ret != 0) {
            ffmpeg_err2str("imi_mp4_close_file_for_create av_write_trailer", ret);
        }
    }
    if (handle->_video_stream) {
        if (handle->_video_stream->codecpar) {
            handle->_video_stream->codecpar->extradata = NULL;
            handle->_video_stream->codecpar->extradata_size = 0;
        }
//        avcodec_close(handle->_video_stream->codec);
        handle->_video_stream = NULL;
    }
    if (handle->_audio_stream) {
        if (handle->_audio_stream->codecpar) {
            handle->_audio_stream->codecpar->extradata = NULL;
            handle->_audio_stream->codecpar->extradata_size = 0;
        }
//        avcodec_close(handle->_audio_stream->codec);
        handle->_audio_stream = NULL;
    }
    avio_close(handle->_format_context->pb);
    handle->_format_context->pb = NULL;
    if (handle->_format_context->metadata) {
        av_dict_free(&handle->_format_context->metadata);
        handle->_format_context->metadata = NULL;
    }
    if (handle->_video_opts) {
        av_dict_free(&handle->_video_opts);
        handle->_video_opts = NULL;
    }
    avformat_free_context(handle->_format_context);
    handle->_format_context = NULL;
    handle->_audio_lasttimestamp = 0;
    handle->_audio_frame_index = 0;
    handle->_audio_duration = 0;
    handle->_audio_lasttimems = 0;
    if (handle->_audio_extradata) {
        free(handle->_audio_extradata);
        handle->_audio_extradata = NULL;
    }
    handle->_audio_extradata_size = 0;
    handle->_video_frame_index = 0;
    handle->_video_fps = 0;
    handle->_video_lasttimestamp = 0;
    handle->_video_lastpts = 0;
    free(handle->_video_extradata);
    handle->_video_extradata = NULL;
    handle->_video_extradata_size = 0;
    handle->_getfirstframe = 0;
    printf("imi_mp4_close_file_for_create success\n");
    return ret;
}

int imi_mp4_close_file_for_create(imimp4_ffmpeg_info_t handle) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    LOGD("imi_mp4_close_file_for_create handle = %x\n", handle);
    ret = _close_file_for_create_inner(handle);
    pthread_mutex_unlock(&handle->_lock_mutex);
    pthread_mutex_destroy(&handle->_lock_mutex);
    free(handle);
    return ret;
}

int _open_file_inner(imimp4_ffmpeg_info_t handle,
                     const char *path,
                     container_format_id *container,
                     video_codec_id *video,
                     audio_codec_id *audio,
                     unsigned int *vfps,
                     unsigned int *vwidth,
                     unsigned int *vheight,
                     unsigned int *achannel,
                     unsigned int *asamplerate,
                     unsigned int *abitrate,
                     unsigned long long *duration) {
    unsigned int i = 0;
    int ret = avformat_open_input(&handle->_format_context, path, NULL, NULL);
    if (ret != 0) {
        ffmpeg_err2str("imi_mp4_open_file avformat_open_input", ret);
        goto error;
    }
    ret = avformat_find_stream_info(handle->_format_context, NULL);
    if (ret < 0) {
        ffmpeg_err2str("imi_mp4_open_file avformat_find_stream_info", ret);
        goto error;
    }
    if (strstr((char *) handle->_format_context->iformat->extensions, "mp4") == NULL) {
        LOGE("imi_mp4_open_file extensions error = %s\n",
             (char *) handle->_format_context->iformat->extensions);
        goto error;
    }
    *container = container_format_mp4;
    for (i = 0; i < handle->_format_context->nb_streams; i++) {
        AVStream *stream = handle->_format_context->streams[i];
        switch (stream->codecpar->codec_type) {
            case AVMEDIA_TYPE_VIDEO: {
                switch (stream->codecpar->codec_id) {
                    case AV_CODEC_ID_H264: {
                        *video = video_codec_h264;
                        av_bsf_alloc(handle->_video_bsfc, &handle->bsf_ctx);
                        av_bsf_init(handle->bsf_ctx);
                        if (handle->_video_bsfc == NULL) {
                            LOGE("imi_mp4_open_file av_bitstream_filter_init h264_mp4toannexb error\n");
                            goto error;
                        }
                        if (stream->codecpar->extradata_size >= 1024 * 2) {
                            LOGE("imi_mp4_open_file extradata_size = %d error\n",
                                 stream->codecpar->extradata_size);
                            goto error;
                        }
                    }
                        break;
                    case AV_CODEC_ID_H265: {
                        *video = video_codec_h265;
//                        handle->_video_bsfc = av_bitstream_filter_init("hevc_mp4toannexb");
                        av_bsf_alloc(handle->_video_bsfc, &handle->bsf_ctx);
                        av_bsf_init(handle->bsf_ctx);
                        if (handle->_video_bsfc == NULL) {
                            LOGE("imi_mp4_open_file av_bitstream_filter_init hevc_mp4toannexb error\n");
                            goto error;
                        }
                        if (stream->codecpar->extradata_size >= 1024 * 3) {
                            LOGE("imi_mp4_open_file extradata_size = %d error\n",
                                 stream->codecpar->extradata_size);
                            goto error;
                        }
                    }
                        break;
                    default:
                        LOGE("imi_mp4_open_file video stream type default = %d\n",
                             stream->codecpar->codec_id);
                        goto error;
                }
                *vfps = stream->avg_frame_rate.num / stream->avg_frame_rate.den;
                *vwidth = stream->codecpar->width;
                *vheight = stream->codecpar->height;
            }
                break;
            case AVMEDIA_TYPE_AUDIO: {
                switch (stream->codecpar->codec_id) {
                    case AV_CODEC_ID_AAC: {
                        *audio = audio_codec_aac;
                    }
                        break;
                    case AV_CODEC_ID_PCM_ALAW: {
                        *audio = audio_codec_g711a;
                    }
                        break;
                    default:
                        LOGE("imi_mp4_open_file audio stream type default = %d\n",
                             stream->codecpar->codec_id);
                        goto error;
                }
                *achannel = stream->codecpar->ch_layout.nb_channels;
                *asamplerate = stream->codecpar->sample_rate;
                *abitrate = (unsigned int) stream->codecpar->bit_rate;
            }
                break;
            default:
                continue;
        }
    }
    *duration = (unsigned long long) handle->_format_context->duration;
    LOGD("imi_mp4_open_file success\n");
    return IMIMEDIA_OK;

    error:
    if (handle->_video_bsfc) {
        av_bsf_free(&handle->bsf_ctx);
        handle->_video_bsfc = NULL;
    }
    if (handle->_format_context) {
        avformat_close_input(&handle->_format_context);
        handle->_format_context = NULL;
    }
    return IMIMEDIA_CAPABILITY_ERROR;
}

int imi_mp4_open_file(const char *path,
                      container_format_id *container,
                      video_codec_id *video,
                      audio_codec_id *audio,
                      unsigned int *vfps,
                      unsigned int *vwidth,
                      unsigned int *vheight,
                      unsigned int *achannel,
                      unsigned int *asamplerate,
                      unsigned int *abitrate,
                      unsigned long long *duration,
        /*out*/imimp4_ffmpeg_info_t *handle) {
    int ret = IMIMEDIA_OK;
    imimp4_ffmpeg_info_t handle_impl = NULL;
    handle_impl = (imimp4_ffmpeg_info_t) malloc(sizeof(imimp4_ffmpeg_info_s));
    if (handle_impl == NULL) {
        LOGE("imi_mp4_open_file malloc error\n");
        return IMIMEDIA_RESOURCE_ERROR;
    }
    memset(handle_impl, 0, sizeof(imimp4_ffmpeg_info_s));
    pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
    pthread_mutex_lock(&handle_impl->_lock_mutex);
    ret = _open_file_inner(handle_impl, path, container, video, audio, vfps, vwidth, vheight,
                           achannel, asamplerate, abitrate, duration);
    if (ret != 0) {
        pthread_mutex_unlock(&handle_impl->_lock_mutex);
        pthread_mutex_destroy(&handle_impl->_lock_mutex);
        free(handle_impl);
        *handle = NULL;
        return ret;
    }
    *handle = handle_impl;
    LOGD("imi_mp4_open_file handle = %x\n", *handle);
    pthread_mutex_unlock(&handle_impl->_lock_mutex);
    return ret;
}

int _get_frame_inner(imimp4_ffmpeg_info_t handle,
                     unsigned char *data,
                     unsigned int *data_len,
                     unsigned long long *timestamp,
                     frame_type_id *frametype,
                     int64_t *pts) {
    int ret = IMIMEDIA_OK;
    int64_t time = 0;
    AVStream *stream = NULL;
    AVPacket pkt = {0};
    if (handle->_format_context == NULL) {
        LOGE("imimp4_get_video_frame format_context is null\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    ret = av_read_frame(handle->_format_context, &pkt);
    if (ret < 0 || pkt.size == 0) {
        if (ret == AVERROR_EOF) {
            //this is correct error
            LOGE("imimp4_get_video_frame av_read_frame eof\n");
            return IMIMEDIA_EOF;
        } else {
            ffmpeg_err2str("imimp4_get_video_frame av_read_frame", ret);
            return ret;
        }
    }
    stream = handle->_format_context->streams[pkt.stream_index];
    switch (stream->codecpar->codec_type) {
        case AVMEDIA_TYPE_VIDEO: {
            if (pkt.flags & AV_PKT_FLAG_KEY) {
                *frametype = frame_type_i;
            } else {
                *frametype = frame_type_p;
            }
            *pts = pkt.pts;
            if (handle->_video_bsfc) {
                unsigned char *poutbuf = NULL;
                int poutbuf_size = 0;
                //                ret = av_bitstream_filter_filter(handle->_video_bsfc, stream, NULL, (uint8_t**)&poutbuf, &poutbuf_size, (uint8_t*)pkt.data, pkt.size, pkt.flags & AV_PKT_FLAG_KEY);
                if (av_bsf_send_packet(handle->bsf_ctx, &pkt) < 0) {
                    fprintf(stderr, "Error sending packet to bitstream filter\n");
                    break;
                }
                while (av_bsf_receive_packet(handle->bsf_ctx, &pkt) == 0) {
                    *data_len = pkt.size;
                    memcpy(data, (unsigned char *) pkt.data, pkt.size);
                }
            } else {
                *data_len = pkt.size;
                memcpy(data, (unsigned char *) pkt.data, pkt.size);
            }
        }
            break;
        case AVMEDIA_TYPE_AUDIO: {
            *frametype = frame_type_audio;
            if (stream->codecpar->codec_id == AV_CODEC_ID_AAC) {
                unsigned char *aac_header = imi_make_aac_header_net(stream->codecpar->sample_rate,
                                                                    stream->codecpar->ch_layout.nb_channels,
                                                                    pkt.size + AAC_ADTS_HEADER);
                memcpy(data, aac_header, AAC_ADTS_HEADER);
                free(aac_header);
                *data_len = pkt.size + AAC_ADTS_HEADER;
                memcpy(data + AAC_ADTS_HEADER, (unsigned char *) pkt.data, pkt.size);
            } else {
                *data_len = pkt.size;
                memcpy(data, (unsigned char *) pkt.data, pkt.size);
            }
        }
            break;
        default:
            break;
    }
    *timestamp = (unsigned long long) (((double) pkt.pts / (double) stream->time_base.den) * 1000);
    av_packet_unref(&pkt);
    return 0;
}

int imi_mp4_get_frame(imimp4_ffmpeg_info_t handle,
                      unsigned char *data,
                      unsigned int *data_len,
                      unsigned long long *timestamp,
                      frame_type_id *frametype) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    int64_t pts = 0;
    ret = _get_frame_inner(handle, data, data_len, timestamp, frametype, &pts);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _seek_file_inner(imimp4_ffmpeg_info_t handle, unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    int64_t time = 0;
    LOGD("imi_mp4_seek_file av_seek_frame timestamp = %lld\n", timestamp);
    time = (int64_t) (((double) (timestamp) / (double) 1000) * AV_TIME_BASE +
                      (double) handle->_format_context->start_time);
    ret = av_seek_frame(handle->_format_context, -1, time,
                        AVSEEK_FLAG_BACKWARD);//AVSEEK_FLAG_BACKWARD
    if (ret < 0) {
        ffmpeg_err2str("imi_mp4_seek_file av_seek_frame", ret);
        return ret;
    }
    return IMIMEDIA_OK;
}

int imi_mp4_seek_file(imimp4_ffmpeg_info_t handle, unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _seek_file_inner(handle, timestamp);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _close_file_for_open_inner(imimp4_ffmpeg_info_t handle) {
    if (handle->_video_bsfc) {
        av_bsf_free(&handle->bsf_ctx);
        handle->_video_bsfc = NULL;
    }
    if (handle->_format_context) {
        avformat_close_input(&handle->_format_context);
        handle->_format_context = NULL;
    }
    LOGD("imi_mp4_close_file_for_open success\n");
    return 0;
}

int imi_mp4_close_file_for_open(imimp4_ffmpeg_info_t handle) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    LOGD("imi_mp4_close_file_for_open handle = %x\n", handle);
    ret = _close_file_for_open_inner(handle);
    pthread_mutex_unlock(&handle->_lock_mutex);
    pthread_mutex_destroy(&handle->_lock_mutex);
    free(handle);
    return ret;
}