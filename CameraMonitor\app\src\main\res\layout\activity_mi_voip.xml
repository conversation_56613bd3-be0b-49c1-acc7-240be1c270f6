<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="com.xiaomi.mico.persistent.voip.MiVoipActivity">

    <TextureView
        android:id="@+id/preview_bg"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextureView
        android:id="@+id/preview_front"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/close_cam_small"
        android:layout_width="336dip"
        android:layout_height="210dip"
        android:layout_marginTop="60dip"
        android:layout_marginEnd="30dip"
        android:textSize="36sp"
        android:gravity="center"
        android:textColor="@color/text_color_ccc"
        android:text="@string/video_call_local_camera_closed"
        android:background="@drawable/rounded_corner_bg"
        android:visibility="gone"
        android:layout_alignParentEnd="true"/>

    <TextView
        android:id="@+id/close_cam_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="345dip"
        android:textSize="36sp"
        android:textColor="@color/text_color_ccc"
        android:text="@string/video_call_remote_camera_closed"
        android:background="@drawable/rounded_corner_bg"
        android:visibility="gone"
        android:layout_centerHorizontal="true"/>

    <TextView
        android:id="@+id/call_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dip"
        android:layout_marginStart="60dip"
        android:textSize="32sp"
        android:textColor="@color/text_color_ccc"
        android:visibility="gone"
        android:text="@string/call_time"/>

    <RelativeLayout
        android:id="@+id/calling_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="40dip"
        android:layout_marginTop="45dip"
        android:visibility="visible"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/calling_photo"
            android:layout_width="144dip"
            android:layout_height="144dip"/>

        <TextView
            android:id="@+id/calling_name_first"
            android:layout_width="144dip"
            android:layout_height="144dip"
            android:gravity="center"
            android:textColor="@color/avatar_view_name"
            android:textSize="90sp"
            android:visibility="gone"/>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dip"
            android:layout_centerVertical="true"
            android:layout_toEndOf="@id/calling_photo"
            android:orientation="vertical">
            <TextView
                android:id="@+id/calling_name"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/calling_name_text_size"
                android:textColor="@color/text_color_ccc"
                android:text="Messi"/>
            <TextView
                android:id="@+id/calling_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dip"
                android:textSize="@dimen/call_btn_text_size"
                android:textColor="@color/text_color_calling"
                android:text="@string/voip_calling"/>
        </LinearLayout>
    </RelativeLayout>

    <ImageView
        android:id="@+id/calling_loading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="360dip"/>

    <LinearLayout
        android:id="@+id/control_panel"
        android:layout_width="fill_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_marginBottom="60dip"
        android:gravity="center_horizontal"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/btn_mute"
            android:layout_width="@dimen/video_button_width"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/call_button_interval"
            android:layout_marginEnd="@dimen/call_button_interval"
            android:drawableTop="@drawable/btn_un_mute"
            android:drawablePadding="@dimen/call_btn_drawablePadding"
            android:gravity="center"
            android:text="@string/voip_mute"
            android:textColor="@color/text_color_ccc"
            android:textSize="@dimen/call_btn_text_size"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/calling_hang_up"
            android:layout_width="@dimen/video_button_width"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/call_button_interval"
            android:layout_marginEnd="@dimen/call_button_interval"
            android:drawableTop="@drawable/btn_end_call"
            android:drawablePadding="@dimen/call_btn_drawablePadding"
            android:gravity="center"
            android:text="@string/voip_hang_up"
            android:textColor="@color/text_color_ccc"
            android:textSize="@dimen/call_btn_text_size" />

        <TextView
            android:id="@+id/btn_call"
            android:layout_width="@dimen/video_button_width"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/call_button_interval"
            android:layout_marginEnd="@dimen/call_button_interval"
            android:drawableTop="@drawable/btn_call"
            android:drawablePadding="@dimen/call_btn_drawablePadding"
            android:gravity="center"
            android:text="@string/voip_accept"
            android:textColor="@color/text_color_ccc"
            android:textSize="@dimen/call_btn_text_size" />

        <TextView
            android:id="@+id/calling_switch_to_voice"
            android:layout_width="@dimen/video_button_width"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/call_button_interval"
            android:layout_marginEnd="@dimen/call_button_interval"
            android:drawableTop="@drawable/btn_switch_to_voice"
            android:drawablePadding="@dimen/call_btn_drawablePadding"
            android:gravity="center"
            android:text="@string/voip_switch_to_voice"
            android:textColor="@color/text_color_ccc"
            android:textSize="@dimen/call_btn_text_size"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/btn_camera_status"
            android:layout_width="@dimen/video_button_width"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/call_button_interval"
            android:layout_marginEnd="@dimen/call_button_interval"
            android:drawableTop="@drawable/btn_camera_enable"
            android:drawablePadding="@dimen/call_btn_drawablePadding"
            android:gravity="center"
            android:text="@string/voip_close_camera"
            android:textColor="@color/text_color_ccc"
            android:textSize="@dimen/call_btn_text_size"
            android:visibility="gone"/>
    </LinearLayout>

</RelativeLayout>