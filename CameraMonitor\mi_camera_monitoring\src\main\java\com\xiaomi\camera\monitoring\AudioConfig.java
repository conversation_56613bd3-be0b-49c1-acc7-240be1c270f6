package com.xiaomi.camera.monitoring;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.MediaRecorder;

/**
 * 注意AAC头需要对上
 * 需要关注ALStreamPushImpl
 */
public class AudioConfig {

    public static final int AUDIO_MIC_SOURCE = MediaRecorder.AudioSource.VOICE_COMMUNICATION;//门外猫眼MIC声音源
    public static final int AUDIO_SPEAKER_INDOOR = AudioManager.STREAM_MUSIC;//门内猫眼喇叭声音源
    public static final int AUDIO_SPEAKER_OUTDOOR = AudioManager.STREAM_VOICE_CALL;//门外喇叭
    public static final int AUDIO_SAMPLE_RATE = 16000;//采样频率
    public static final int AUDIO_CHANNEL = AudioFormat.CHANNEL_IN_MONO;//单通道
    public static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;

    public static final int OPUS_AUDIO_CHANNEL = 1; //单通道
    public static final int OPUS_AUDIO_COMPLEXITY = 0; //编码器的计算复杂度。支持的范围是 0-10（含），其中 10 表示最高复杂度。

}
