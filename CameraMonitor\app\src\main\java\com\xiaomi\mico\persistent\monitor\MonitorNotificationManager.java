package com.xiaomi.mico.persistent.monitor;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.widget.RemoteViews;

import androidx.core.app.NotificationCompat;

public class MonitorNotificationManager {

    private static final MonitorNotificationManager sInstance = new MonitorNotificationManager();

    // 常驻通知提醒
    private static final int PERSISTENT_GROUP_TAG = 0B100;
    private final String NOTIFICATION_CHANNEL_ID = "cameraMonitor";
    public static final int FOREGROUND_NOTIFY_ID_INIT_MISS = 1;
    private final int NOTIFICATION_MONITOR_ID = 2;

    public static MonitorNotificationManager getInstance() {
        return sInstance;
    }

    private MonitorNotificationManager() {
    }

    public void notifyMonitorNotification(Context context) {
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        NotificationChannel channel = new NotificationChannel(NOTIFICATION_CHANNEL_ID, "notification", NotificationManager.IMPORTANCE_HIGH);
        manager.createNotificationChannel(channel);

        // 暂时屏蔽需求通知时间格式，统一为系统通知时间格式
        //SimpleDateFormat format = new SimpleDateFormat(context.getString(R.string.camera_monitor_toast_clock_today_format), Locale.CHINA);

        Bundle extra = new Bundle();
        extra.putString("extra_button_text", context.getString(R.string.camera_monitor_toast_btn_exit));
        Intent intent = new Intent();
        intent.setAction(CameraMonitorService.STOP_SERVICE);
        intent.setPackage("com.xiaomi.mico.persistent.monitor");
        PendingIntent pendingIntent = PendingIntent.getService(context, 0,
                intent, PendingIntent.FLAG_UPDATE_CURRENT);

//        NotificationCompat.Builder notification = new NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID)
//                .setContentTitle(context.getString(R.string.camera_monitor_toast_msg))
//                .setWhen(System.currentTimeMillis())
//                .setSmallIcon(R.mipmap.camera_monitor_img)
//                .setGroup(PERSISTENT_GROUP_TAG + "")
//                .setExtras(extra)
//                .setContentIntent(pendingIntent)
//                .setLargeIcon(BitmapFactory.decodeResource(context.getResources(), R.mipmap.camera_monitor_img));

        // 1. 创建RemoteViews对象（应用层负责）
        RemoteViews remoteViews = new RemoteViews(context.getPackageName(), R.layout.notification_monitor_layout);

        // 2. 设置RemoteViews的内容（应用层负责）
        remoteViews.setTextViewText(R.id.notification_title, "自定义标题");
        remoteViews.setTextViewText(R.id.notification_subtitle, "自定义内容");
        remoteViews.setTextViewText(R.id.notification_button, "断开");
//        remoteViews.setImageViewResource(R.id.notification_icon, R.mipmap.camera_monitor_img);
        remoteViews.setOnClickPendingIntent(R.id.notification_button, pendingIntent);

        // 3. 创建通知并设置自定义内容视图
        NotificationCompat.Builder notification = new NotificationCompat.Builder(context, NOTIFICATION_CHANNEL_ID);
        notification.setSmallIcon(R.mipmap.camera_monitor_img)
                .setCustomContentView(remoteViews)
                .setGroup(PERSISTENT_GROUP_TAG + "");  // 设置为常驻通知

        manager.notify(NOTIFICATION_MONITOR_ID, notification.build());

    }

    public void cancelMonitorNotification(Context context) {
        NotificationManager manager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        manager.cancel(NOTIFICATION_MONITOR_ID);
    }
}
