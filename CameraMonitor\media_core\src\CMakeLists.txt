# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.
cmake_minimum_required(VERSION 3.4.1)
#set flags
#set(CMAKE_CXX_FLAGS "-Wno-error=deprecated-declarations -Wno-deprecated-declarations ")


# set src
set(c_src main/cpp/libmedia)

#log abi
message(>Android abi: ${ANDROID_ABI})

#load ffmpeg so
add_library(
        # Sets the name of the library.
        ffmpeg-org
        # Sets the library as a shared library.
        SHARED
        # Provides a relative path to your source file(s).
        IMPORTED)

#指明 so 库的路径
set_target_properties(
        # Specifies the target library.
        ffmpeg-org
        # Specifies the parameter you want to define.
        PROPERTIES IMPORTED_LOCATION
        # Provides the path to the library you want to import.
        ${CMAKE_SOURCE_DIR}/main/jniLibs/${ANDROID_ABI}/libffmpeg-org.so)


#Inclued ffmpeg .h file
set(FFMPEG_INCLUDE ${CMAKE_SOURCE_DIR}/main/cpp/include_ffmpeg6_1_1)
include_directories(${FFMPEG_INCLUDE})

message(-- > FFMPEG_INCLUDE: ${FFMPEG_INCLUDE})
message(-- > IMI_C_INCLUDE ${CMAKE_SOURCE_DIR}/${c_src}/common)

#Inclued ffmpeg .h file
include_directories(
        ${CMAKE_SOURCE_DIR}/${c_src}/common
        ${CMAKE_SOURCE_DIR}/${c_src}/mp4
        ${CMAKE_SOURCE_DIR}/${c_src}/decoder
)

# Declares and names the project.
project("media_core")

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# Gradle automatically packages shared libraries with your APK.

#finds .c file
#->##.externalNativeBuild->cmake>... find log

file(GLOB_RECURSE SRC_LIST_IMI_C "${c_src}/mp4/imimp4_ffmpeg.c" "${c_src}/decoder/imidecoder_ffmpeg.c")
message(">>> SET DEFAULT TO BEFORE C,SRC_LIST_IMI_C=${SRC_LIST_IMI_C}")
#add_executable(SRC_LIST_IMI_C libmedia/mp4/imimp4_ffmpeg.c)
#target_sources(runtest test.cpp)


message(">>> SET DEFAULT TO BEFORE H, SRC_LIST_IMI_H=${SRC_LIST_IMI_H}")

add_library(
        # Sets the name of the library.
        media_core
        # Sets the library as a shared library.
        SHARED
        # Provides a relative path to your source file(s).
        main/cpp/AKMp4MuxerFFmpeg.cpp
        main/cpp/AKDecodeFFmpeg.cpp
        ${SRC_LIST_IMI_C})

# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.

find_library( # Sets the name of the path variable.
        log-lib
        # Specifies the name of the NDK library that
        # you want CMake to locate.
        log)
# Specifies libraries CMake should link to your target library. You
# can link multiple libraries, such as libraries you define in this
# build script, prebuilt third-party libraries, or system libraries.

target_link_libraries(
        # Specifies the target library.
        media_core
        # Links the target library to the log library
        # included in the NDK.
        ${log-lib}
        ffmpeg-org
)


