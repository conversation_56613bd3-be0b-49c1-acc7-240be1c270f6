package com.xiaomi.mico.persistent.cloud;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

public class TcpClientManager {
    private static final String TAG = "TcpClientManager";

    public static final int ERROR_CONNECT = 0x1001;
    public static final int ERROR_DISCONNECT = 0x1002;
    public static final int ERROR_SEND_MSG = 0x1003;
    public static final int ERROR_REC_MSG = 0x1004;

    private static final int CONNECT_TIMEOUT = 5 * 1000;
    private static final int BUFFER_SIZE = 1024 * 32;

    private Socket socket;
    private OutputStream outputStream;
    private InputStream inputStream;
//    private BufferedReader inputReader;
    private Thread receiveThread;
    private OnMsgRecLis messageListener;
    private OnConnStatusLis connectionStatusListener;

    public interface OnMsgRecLis {
        void onMessageReceived(byte[] data);
    }

    public interface OnConnStatusLis {
        void onConnected();
        void onDisconnected();
        void onError(int code, String error);
    }

    public void setOnMsgRecLis(OnMsgRecLis listener) {
        this.messageListener = listener;
    }

    public void setOnConnStatusLis(OnConnStatusLis listener) {
        this.connectionStatusListener = listener;
    }

    public void connect(String serverIp, int serverPort) {
        new Thread(() -> {
            try {
                socket = new Socket();
                socket.connect(new InetSocketAddress(serverIp, serverPort), CONNECT_TIMEOUT);
                socket.setTcpNoDelay(true);
                outputStream = socket.getOutputStream();
                inputStream = socket.getInputStream();
//                inputReader = new BufferedReader(new InputStreamReader(socket.getInputStream()));

                if (connectionStatusListener != null) {
                    connectionStatusListener.onConnected();
                }

                startReceiving();
            } catch (IOException e) {
                if (connectionStatusListener != null) {
                    connectionStatusListener.onError(ERROR_CONNECT, "连接失败: " + e.getMessage());
                }
            }
        }).start();
    }

    private void startReceiving() {
        receiveThread = new Thread(() -> {
            byte[] buffer = new byte[BUFFER_SIZE];
            while (socket != null && !socket.isClosed()) {
                try {
//                    String message = inputReader.readLine(); // 线程阻塞的
//                    if (message != null && messageListener != null) {
//                        messageListener.onMessageReceived(message);
//                    }
                    if (socket.getInputStream().available() > 0) {
                        int bytesRead = socket.getInputStream().read(buffer);
                        if (bytesRead > 0) {
//                            String message = new String(buffer, 0, bytesRead, StandardCharsets.UTF_8);
                            byte[] byteData = new byte[bytesRead];
                            System.arraycopy(buffer, 0, byteData, 0, bytesRead);
                            if (messageListener != null) {
                                messageListener.onMessageReceived(byteData);
                            }
                        }
                    } else {
                        Thread.sleep(100); // 休眠一段时间，避免忙等待
                    }
                } catch (IOException | InterruptedException e) {
                    if (connectionStatusListener != null) {
                        connectionStatusListener.onError(ERROR_REC_MSG, "接收数据失败: " + e.getMessage());
                    }
                    break;
                }
            }
            if (connectionStatusListener != null) {
                connectionStatusListener.onDisconnected();
            }
        });
        receiveThread.start();
    }

    public void sendData(String data) {
        new Thread(() -> {
            try {
                if (outputStream != null) {
                    outputStream.write(data.getBytes());
                    outputStream.flush();
                }
            } catch (IOException e) {
                if (connectionStatusListener != null) {
                    connectionStatusListener.onError(ERROR_SEND_MSG, "发送数据失败: " + e.getMessage());
                }
            }
        }).start();
    }

    public void disconnect() {
        try {
            if (socket != null) {
                socket.close();
            }
            // 关闭输出流
            if (outputStream != null) {
                outputStream.close();
            }

            // 关闭输入流
            if (inputStream != null) {
                inputStream.close();
            }

            if (receiveThread != null) {
                receiveThread.interrupt();
            }

            // 通知连接断开
            if (connectionStatusListener != null) {
                connectionStatusListener.onDisconnected();
            }
        } catch (IOException e) {
            if (connectionStatusListener != null) {
                connectionStatusListener.onError(ERROR_DISCONNECT, "断开连接失败: " + e.getMessage());
            }
        } finally {
            // 清理资源
            socket = null;
            outputStream = null;
            inputStream = null;
            receiveThread = null;
            connectionStatusListener = null;
            messageListener = null;
        }
    }
}

