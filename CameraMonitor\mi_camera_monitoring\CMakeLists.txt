# For more information about using CMake with Android Studio, read the
# documentation: https://d.android.com/studio/projects/add-native-code.html

# Sets the minimum version of CMake required to build the native library.

cmake_minimum_required(VERSION 3.4.1)

# Creates and names a library, sets it as either STATIC
# or SHARED, and provides the relative paths to its source code.
# You can define multiple libraries, and CMake builds them for you.
# <PERSON>rad<PERSON> automatically packages shared libraries with your APK.

include_directories(src/main/jniLibs/include)

#注意这里的目录路径要是你自己的项目的
set(ARM_DIR ${PROJECT_SOURCE_DIR}/src/main/jniLibs)

add_library( # Sets the name of the library.
        missjni-lib

        # Sets the library as a shared library.
        SHARED

        # Provides a relative path to your source file(s).
        src/main/cpp/missJNI.c
        src/main/cpp/miss_porting.c
        )

# Searches for a specified prebuilt library and stores the path as a
# variable. Because CMake includes system libraries in the search path by
# default, you only need to specify the name of the public NDK library
# you want to add. CMake verifies that the library exists before
# completing its build.

find_library( # Sets the name of the path variable.
        log-lib

        # Specifies the name of the NDK library that
        # you want CMake to locate.
        log)

find_library( # Sets the name of the path variable.
        android-lib

        android)

## MISS LIBS
add_library(AVAPIs SHARED IMPORTED)
set_target_properties(AVAPIs PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libAVAPIs.so)

add_library(IOTCAPIs SHARED IMPORTED)
set_target_properties(IOTCAPIs PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libIOTCAPIs.so)

add_library(miss SHARED IMPORTED)
set_target_properties(miss PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libmiss.so)

add_library(PPCS_API SHARED IMPORTED)
set_target_properties(PPCS_API PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libPPCS_API.so)

add_library(RDTAPIs SHARED IMPORTED)
set_target_properties(RDTAPIs PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libRDTAPIs.so)

add_library(sCHL SHARED IMPORTED)
set_target_properties(sCHL PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libsCHL.so)

add_library(sodium SHARED IMPORTED)
set_target_properties(sodium PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libsodiumjni.so)

add_library(TUTKGlobalAPIs SHARED IMPORTED)
set_target_properties(TUTKGlobalAPIs PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libTUTKGlobalAPIs.so)

add_library(json-c SHARED IMPORTED)
set_target_properties(json-c PROPERTIES IMPORTED_LOCATION ${ARM_DIR}/${CMAKE_ANDROID_ARCH_ABI}/libjson-c.so)

target_link_libraries( # Specifies the target library.
        missjni-lib

        # miss libs
        miss TUTKGlobalAPIs RDTAPIs AVAPIs IOTCAPIs PPCS_API sCHL sodium json-c
        ${android-lib}
        ${log-lib})