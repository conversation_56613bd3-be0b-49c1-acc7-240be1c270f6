/*
 * Copyright (c) 2019 Xiaomi. All Rights Reserved.
 */

/**
 * @file miss_porting.h
 *
 * MISS (MI IoT Streaming SDK)
 *
 * The role of this SDK is trying to hide all the complexity of the streaming
 * stuff from the application developers. This includes but not limited to:
 *
 *   * Initiate the tunnel with the target, weather in P2P or Relay;
 *   * Transfer data contents, usually this is media stream, through the
 *     tunnel we created above;
 *   * Signalling through the tunnel we created above to control the media
 *     stream;
 *   * Tunnel information exchange encryption, this includes stream
 *     encryption ioctl commands encryption;
 *   * QoS and quality control;
 *
 * By adopting this SDK in your streaming application development, you do not
 * need to care details of how your contents are exchanged with the target,
 * you just focus on the functionality implementation of your product.
 * Hopefully this will easy the overall task, and eventually shorten the time
 * to market of your products.
 *
 * This SDK is brought to you by MI IoT edge computing and IPC team.
 * Any questions and suggestions, please contact them.
 *
 * This file contains MISS SDK callback functions.
 */

#ifndef MISS_PORTING_H
#define MISS_PORTING_H

#include "miss.h"
#ifdef BUILT_FOR_IOS_PLATFORM
#include "P2PCam/AVFRAMEINFO.h"
#endif //BUILT_FOR_IOS_PLATFORM

/**
 * miss_ack_send() - RPC Reply ACK function
 *
 * MISS SDK depends on this function to reply ack (to cloud). On
 * Android & IOS App, usually it uses HTTP(s) to talk with MIoT cloud. On
 * Device, it sends to OT agent (Linux or RTOS implementations).
 *
 * @param[in] buf: the ack buf send to cloud.
 * @param[in] length: the ack buf length.
 *
 * @retval 0: if send success
 * @retval <0: if send failed.
 *
 * Example:
 * @verbatim embed:rst:leading-asterisk
 * .. code-block:: c
 *
 *   ret = miss_ack_send(buf, length);
 *
 * @endverbatim
 */
#ifndef ANDROID_CLIENT
int miss_ack_send(const char *buf, int length);
#endif

/**
 * miss_rpc_send() - RPC send function
 *
 * MISS SDK depends on this function to send out RPC (to cloud). On
 * Android & IOS App, usually it uses HTTP(s) to talk with MIoT cloud. On
 * Device, it sends to OT agent (Linux or RTOS implementations).
 * don't call the miss_rpc_process function inside the function
 *
 * @param[in] rpc_id	used to identify each separate RPC.
 * @param[in] method	Json method, can be used to filter if needed.
 * @param[in] params	Json params.
 *
 * @retval 0: if send success
 * @retval <0: if send failed.
 *
 * Example:
 * @verbatim embed:rst:leading-asterisk
 * .. code-block:: c
 *
 *   ret = miss_rpc_send((void *)&rid,
 *		"_sync.miss_request_something",
 *		"{\"key\":\"value\"}");
 *
 * @endverbatim
 */
int miss_rpc_send(void *rpc_id, const char *method, const char *params);

/**
 * miss_statistics() - statistics function
 *
 * MISS SDK depends on this function to send out MISS SDK statistics.
 * Only host application knows the statistics channel details.
 *
 * MISS SDK does not expect ack for statistics reports, if upper layer's
 * implementation has a ack, just drop it.
 *
 * @param[in] session	MISS session
 * @param[in] data	statistics json data
 * @param[in] length 	data length
 *
 * @retval 0: if no error.
 * @retval <0: if error ocurrs.
 */
int miss_statistics(miss_session_t *session, void *data, int length);

/**
 * miss_on_connect() - MISS on connect callback
 */
int miss_on_connect(miss_session_t *session, void **user_data);

/**
 * miss_on_disconnect() - MISS on disconnect callback
 */
int miss_on_disconnect(miss_session_t *session, miss_error_e error, void *user_data);

#ifdef MIRTC_VENDOR
/**
 * @brief
 *
 * @param session
 * @param user_data [IN]
 * @param type miss_session_type_e
 * @param args
 * @return ** int
 */
int miss_on_connect_ex(miss_session_t *session, void **user_data, miss_session_type_e type, char* args);
int miss_on_disconnect_ex(miss_session_t *session, miss_error_e error, void *user_data, miss_session_type_e type,
                          char* args);
#endif

/**
 * miss_on_error() - MISS on error callback
 */
int miss_on_error(miss_session_t *session, miss_error_e error, void *user_data);

/**
 * miss_on_video_data() - MISS on video data callback
 */
void miss_on_video_data(miss_session_t *session, miss_frame_header_t *frame_header, void *data, void *user_data);

/**
 * miss_on_audio_data() - MISS on audio data callback
 */
void miss_on_audio_data(miss_session_t *session, miss_frame_header_t *frame_header, void *data, void *user_data);

#ifdef BUILT_FOR_IOS_PLATFORM
/**
 * miss_tutk_video_data() - Obsolete TUTK with MISS wrapper on video data callback
 */
void miss_tutk_video_data(miss_session_t *session, FRAMEINFO_t *frame_header, void *data,  uint32_t length, void *user_data);

/**
 * miss_tutk_video_data() - Obsolete TUTK with MISS wrapper on video data callback
 */
void miss_tutk_audio_data(miss_session_t *session, FRAMEINFO_t *frame_header, void *data, uint32_t length, void *user_data);
#endif //BUILT_FOR_IOS_PLATFORM
/**
 * miss_on_rdt_data() - MISS on rdt (reliable data tunnel) data callback
 */
void miss_on_rdt_data(miss_session_t *session, void *rdt_data, uint32_t length, void *user_data);

/**
 * miss_on_cmd() - MISS on command
 */
void miss_on_cmd(miss_session_t *session, miss_cmd_e cmd, void *params, unsigned int length, void *user_data);

#if !defined(MISS_CLIENT) && !defined(ANDROID_CLIENT) && !defined(BUILT_FOR_IOS_PLATFORM)
void miss_request_I_frame(miss_session_t *session);
#endif

/**
 * miss_on_server_ready() - Notify OT that MISS is ready.
 *
 * MISS SDK use this function to notify MIoT OT Agent when it is ready.
 *
 * @verbatim embed:rst:leading-asterisk
 * .. note::
 *
 *   For battery powered device only!
 * @endverbatim
 */
int miss_on_server_ready();

/**
 * miss_encrypt_data() - MISS  extern encrypt api
 */
void miss_encrypt_data (miss_encrypt_param_t *encrypt_param);

/**
 * miss_decrypt_data() - MISS  extern decrypt api
 */
void miss_decrypt_data  (miss_encrypt_param_t *decrypt_param);

/**
 * miss_mirtc_send() - Miss MiRTC message send function
 *
 * MISS SDK depends on this function to send out miss MiRTC rpc message by OT (to peer). On
 * Android & IOS App, usually it uses MQTT to talk with MIoT cloud. On
 * Device, it sends to OT agent (Linux or RTOS implementations).
 *
 * @param[in] mirtc_id      mirtc id string, MQTT pub topic string
 * @param[in] method        method
 * @param[in] param     	Json mirtc message.
 *
 * @retval 0: if send success
 * @retval <0: if send failed.
 *
 * Example:
 * @verbatim embed:rst:leading-asterisk
 * .. code-block:: c
 *
 *   ret = miss_mirtc_send(mirtc_id, method, param);
 *
 * @endverbatim
 */
int miss_mirtc_send(const char *mirtc_id, const char *method, const char *params);

// 网络接口过滤的回调函数,用于搜集candidate
int miss_mirtc_interface_filter(uint64_t ullCustomData, char* pcIfName);

#endif
