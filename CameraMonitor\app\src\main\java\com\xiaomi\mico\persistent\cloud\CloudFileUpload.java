package com.xiaomi.mico.persistent.cloud;

import android.text.TextUtils;

import com.google.gson.JsonObject;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.PreUploadEntry;
import com.xiaomi.mico.persistent.monitor.CameraMonitorManager;
import com.xiaomi.mico.persistent.utils.FileUtils;
import com.xiaomi.mico.persistent.utils.MiUploadUtils;
import com.xiaomi.mico.persistent.utils.MiotManager;

import org.apache.commons.codec.binary.Base64;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;

import javax.crypto.spec.IvParameterSpec;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class CloudFileUpload {
    private static String TAG = "CloudFileUpload";

    private static final MediaType MEDIA_TYPE_JPEG = MediaType.parse("image/jpeg");
    private static final MediaType MEDIA_TYPE_VIDEO = MediaType.parse("video/mp4");

    private UploadCallback mUploadCallback;
    private OkHttpClient mHttpClient;

    public CloudFileUpload(UploadCallback callback) {
        mHttpClient = MiUploadUtils.getOkHttpClient();
        this.mUploadCallback = callback;
    }

    //1.智能摄像机获得认证token
    public void getDomainUrl() {
//        String miotToken = MiotManager.getInstance().getMiotToken();
//        if (TextUtils.isEmpty(miotToken)) {
//            Log.e(TAG, "getDomainUrl error, token is null");
//            return;
//        }
        try {
            String cameraLoginResult = MiotManager.getInstance().cameraLogin(Constants.OT_TOKEN);
            if (!TextUtils.isEmpty(cameraLoginResult)) {
                JSONObject domainJson = new JSONObject(cameraLoginResult);
                JSONObject domainData = new JSONObject(domainJson.getString("data"));
                String bUrl = domainData.getString("bUrl");
                String urlToken = domainData.getString("token");
                String securityKey = domainData.getString("security");
                String uploadFileIv = Base64.encodeBase64URLSafeString(MiUploadUtils.generateIvParameterSpec().getIV());
                if (mUploadCallback != null) {
                    mUploadCallback.getDomainUrlSuccess(securityKey, uploadFileIv);
                }
                getPreUpload(bUrl, urlToken, securityKey, uploadFileIv);
            } else {
                if (mUploadCallback != null) {
                    mUploadCallback.getDomainUrlFailed();
                }
            }

        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    //2.获取待上传云存视频的fileId，开始上传
    private void getPreUpload(String url, String token, String securityKey, String iv) {
        String preUploadUrl = "/common/device/preUpload";
        //请求参数封装
        Request.Builder builder = new Request.Builder();
        HttpUrl.Builder urlBuild = HttpUrl.parse(url + preUploadUrl).newBuilder();
        IvParameterSpec preUploadIv =  MiUploadUtils.generateIvParameterSpec();
        urlBuild.addQueryParameter("iv", Base64.encodeBase64URLSafeString(preUploadIv.getIV()));
        builder.url(urlBuild.build());
        builder.addHeader("Cookie", "serviceToken=" + token);
        Request request = builder.build();

        //发送请求
        Call call = mHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    String preUploadCallback = response.body().string();
                    //将返回中的字段"data"+返回的字段iv作为输⼊，⽣成新的signature, 并与返回的“sign”⽐较，⽐较通过
                    //后，使⽤之前的服务器下发的secretKey和返回的“iv"，解密字段"data"
                    try {
                        String preUploadDecrypt = MiUploadUtils.decryptData(preUploadCallback, securityKey);
//                        L.monitor.v("%s preUploadDecrypt: %s", TAG, preUploadDecrypt);

                        JSONObject preUploadDecryptJson = new JSONObject(preUploadDecrypt);
                        JSONObject preUploadDecryptData = preUploadDecryptJson.getJSONObject("data");
                        String fileId = preUploadDecryptData.getString("fileId");
                        JSONArray storageUrlObj = preUploadDecryptData.getJSONArray("storageUrl");
                        String storageUrl = storageUrlObj.getString(0);
                        L.monitor.i("%s fileId: %s", TAG, fileId);
                        if (mUploadCallback != null) {
                            mUploadCallback.preUploadCallback(new PreUploadEntry(url, token, securityKey, fileId, storageUrl, iv));
                        }
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }else {
                    L.monitor.e("%s Get preUpload failed: %s", TAG, response.message());
                    if (mUploadCallback != null) {
                        mUploadCallback.uploadError(CloudErrorCode.ERROR_CODE_PRE_UPLOAD_ERROR, true);
                    }
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                // 文件上传失败
                L.monitor.e("%s Get preUpload failed:: %s", TAG, e.getMessage());
                if (mUploadCallback != null) {
                    mUploadCallback.uploadError(CloudErrorCode.ERROR_CODE_PRE_UPLOAD_ERROR, true);
                }
            }
        });
    }

    // 377端加密视频和缩略图
    public void uploadFile377(PreUploadEntry preUploadEntry, boolean isVip, byte[] imageEncryptFile, String imageSign,
                              byte[] videoEncryptFile, String videoSign, int index, boolean isEnd,
                              String eventType, boolean ignoreEvent) {
        //================================上传文件==================== start
        String securityKey = preUploadEntry.getSecurityKey();
        String fileId = preUploadEntry.getFileId();
        String uploadFileIv = preUploadEntry.getIv();
        //封装参数
        String cut = isVip ? "true" : "false"; //表示看家事件是切分或合并展示。⻔铃类设备该项填false。摄像机类按实际事件切分需求设置true或false(会员传true，⾮会员传false)
        String fieldIdBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(fileId.getBytes(), securityKey, uploadFileIv));
        String offsetBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(String.valueOf(index).getBytes(), securityKey, uploadFileIv));
        String modelBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(CameraMonitorManager.missDeviceInfo.device_model.getBytes(), securityKey, uploadFileIv));
        String videoSignEncryptBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(videoSign.getBytes(), securityKey, uploadFileIv));
        String imageSignEncryptBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(imageSign.getBytes(), securityKey, uploadFileIv));
        String eventTypeBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(eventType.getBytes(), securityKey, uploadFileIv));
        String cutBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(cut.getBytes(), securityKey, uploadFileIv));
        String isVipBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(Boolean.toString(isVip).getBytes(), securityKey, uploadFileIv));

        uploadFile(preUploadEntry, fieldIdBase64, offsetBase64, modelBase64,
                videoEncryptFile, videoSignEncryptBase64, imageEncryptFile, imageSignEncryptBase64,
                eventTypeBase64, cutBase64, isVipBase64, uploadFileIv, securityKey, isEnd, ignoreEvent);
        //=============================上传文件======================= end
    }

    // Android端加密视频和缩略图
    public void encryptFileAndUpload(PreUploadEntry preUploadEntry, boolean isVip, File imageFile, File videoFile,
                                      int index, boolean isEnd, String eventType, boolean ignoreEvent) {
        //================================上传文件==================== start
        String uploadFileIv = Base64.encodeBase64URLSafeString(MiUploadUtils.generateIvParameterSpec().getIV());

        String securityKey = preUploadEntry.getSecurityKey();
        String fileId = preUploadEntry.getFileId();
        //加密缩略图
        MiUploadUtils.EntryData imageEncryptData = MiUploadUtils.encryptFile(imageFile, securityKey, uploadFileIv);
        byte[] imageEncryptFile = imageEncryptData.encryptFile;
        String imageSign = imageEncryptData.encryptSign;
        if (Constants.DELETE_CLOUD_FILE) {
            FileUtils.delFile(imageFile.getPath()); //加密成功后删除原始图片文件
        }
//        Log.v(TAG,"imageEncryptFile:" + imageEncryptFile.getPath()+"-->imageSign:" + imageSign);

        //加密视频
        MiUploadUtils.EntryData videoEncryptData = MiUploadUtils.encryptFile(videoFile, securityKey, uploadFileIv);
        byte[] videoEncryptFile = videoEncryptData.encryptFile;
        String videoSign = videoEncryptData.encryptSign;
        if (Constants.DELETE_CLOUD_FILE) {
            FileUtils.delFile(videoFile.getPath()); //加密成功后删除原始视频文件
        }
//        Log.v(TAG,"videoEncryptFile:"+videoEncryptFile.getPath()+"-->videoSign:"+videoSign);
        //封装参数
        String cut = isVip ? "true" : "false"; //表示看家事件是切分或合并展示。⻔铃类设备该项填false。摄像机类按实际事件切分需求设置true或false(会员传true，⾮会员传false)
        String fieldIdBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(fileId.getBytes(), securityKey, uploadFileIv));
        String offsetBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(String.valueOf(index).getBytes(), securityKey, uploadFileIv));
        String modelBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(CameraMonitorManager.missDeviceInfo.device_model.getBytes(), securityKey, uploadFileIv));
        String videoSignEncryptBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(videoSign.getBytes(), securityKey, uploadFileIv));
        String imageSignEncryptBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(imageSign.getBytes(), securityKey, uploadFileIv));
        String eventTypeBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(eventType.getBytes(), securityKey, uploadFileIv));
        String cutBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(cut.getBytes(), securityKey, uploadFileIv));
        String isVipBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(Boolean.toString(isVip).getBytes(), securityKey, uploadFileIv));

        uploadFile(preUploadEntry, fieldIdBase64, offsetBase64, modelBase64,
                videoEncryptFile, videoSignEncryptBase64, imageEncryptFile, imageSignEncryptBase64,
                eventTypeBase64, cutBase64, isVipBase64, uploadFileIv, securityKey, isEnd, ignoreEvent);
        //=============================上传文件======================= end
    }

    //3.上传视频⽚段和图⽚(视频每⽚必须3秒，否则影响效果)
    private synchronized void uploadFile(PreUploadEntry preUploadEntry, String fileId, String offset, String model,
                                         byte[] videoFile, String videoSign, byte[] imgFile, String imgSign,
                                         String eventType, String cut, String isVip,
                                         String newIv, String securityKey, boolean isEnd,
                                         boolean ignoreEvent) {
        String uploadUrl = "/common/device/v2/upload";
        String params = "POST&" + uploadUrl + "&" + cut + "&" + eventType
                + "&" + fileId + "&" + imgSign + "&" + isVip
                + "&" + newIv + "&" + model+ "&" + offset + "&" + videoSign;
        String paramsSign = MiUploadUtils.generateSign(params.getBytes());

        String storageUrl = preUploadEntry.getStorageUrl();
        String token = preUploadEntry.getToken();
        HttpUrl.Builder urlBuild = HttpUrl.parse(storageUrl+uploadUrl).newBuilder();
        urlBuild.addQueryParameter("iv", newIv);
        urlBuild.addQueryParameter("signature", paramsSign);
        urlBuild.addQueryParameter("fileId", fileId);
        urlBuild.addQueryParameter("offset", offset);
        urlBuild.addQueryParameter("eventType", eventType);
        urlBuild.addQueryParameter("cut", cut);
        urlBuild.addQueryParameter("isVip", isVip);
        urlBuild.addQueryParameter("model", model);
        urlBuild.addQueryParameter("videoSign", videoSign);
        urlBuild.addQueryParameter("imgSign", imgSign);

        MultipartBody body = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("video", videoSign, RequestBody.create(MEDIA_TYPE_VIDEO, videoFile))
                .addFormDataPart("img", imgSign, RequestBody.create(MEDIA_TYPE_JPEG, imgFile))
                .build();

        Request.Builder builder = new Request.Builder()
                .header("Cookie","serviceToken=" + token)
                .url(urlBuild.build())
                .post(body);

        Request request = builder.build();
//        Log.v(TAG, "uploadFile!!!!!!!!:" + request.toString());

        Call call = mHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseString = response.body().string();

                if (response.isSuccessful()) {
                    L.monitor.i("%s Upload file success", TAG);
                    //============================上传Meta======================== start
                    try {
                        String decryptMetaData = MiUploadUtils.decryptData(responseString, securityKey);
//                        Log.i(TAG,"decryptMetaData:" + decryptMetaData);

                        JSONObject metaDecryptObj = new JSONObject(decryptMetaData);
                        int code = metaDecryptObj.getInt("code");
                        if (code != 0) {
                            if (mUploadCallback != null) {
                                mUploadCallback.uploadError(code, isEnd);
                            }
                            return;
                        }
                        JSONObject metaData = metaDecryptObj.getJSONObject("data");
                        String meta = metaData.getString("meta");

                        String newMetaIv = Base64.encodeBase64URLSafeString(MiUploadUtils.generateIvParameterSpec().getIV());
                        String metaEventType = MiUploadUtils.decrypt(eventType, securityKey, newIv);
                        String metaOffset = MiUploadUtils.decrypt(offset, securityKey, newIv);
                        String metaIsVip = MiUploadUtils.decrypt(isVip, securityKey, newIv);
                        //Log.i(TAG,"metaEventType:" + metaEventType + " metaOffset:" + metaOffset + " metaIsVip:" + metaIsVip);
//                        String extraInfo = "{\"ver\":\"1.0.0\",\"alarmStart\":" + alarmStart +",\"eventType\":\""+ metaEventType +"\"}";
                        JsonObject extraInfo = new JsonObject();
                        extraInfo.addProperty("ver", "1.0.0");
                        extraInfo.addProperty("alarmStart", Integer.parseInt(metaOffset) == 0 ? true : false);
                        extraInfo.addProperty("eventType", metaEventType);
//                        L.monitor.i("%s offset: %s, extraInfo: %s", TAG, metaOffset, extraInfo);
                        JsonObject additionalInfo = new JsonObject();
                        JsonObject gopInfo = new JsonObject();
                        gopInfo.addProperty("gop", 3);
                        gopInfo.addProperty("alarmOffsetLimit", 3);
                        gopInfo.addProperty("cloudOffsetLimit", Boolean.parseBoolean(metaIsVip) ? 600 : 3);
                        additionalInfo.add("gopInfo", gopInfo);
//                        L.monitor.i("%s additionalInfo: %s", TAG, additionalInfo);
                        String ignore = ignoreEvent ? "true" : "false"; //看家助⼿是否显示该事件。为true时，看家助⼿不显示新上传的事件
                        String operationType;
                        if (isEnd) {
                            operationType = "endBlock";
                        } else {
                            operationType = "midBlock";
                        }
                        String serverBabyCryCheck = "0";
                        String serverPeopleMotionCheck = "0";
                        String serverFaceCheck = Boolean.parseBoolean(metaIsVip) ? "1" : "0";
                        //Log.i(TAG,"serverBabyCryCheck:" + serverBabyCryCheck + " serverPeopleMotionCheck:" + serverPeopleMotionCheck + " serverFaceCheck:" + serverFaceCheck);

                        String extraInfoBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(extraInfo.toString().getBytes(), securityKey, newMetaIv));
                        String additionalInfoBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(additionalInfo.toString().getBytes(), securityKey, newMetaIv));
                        String ignoreEventBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(ignore.getBytes(), securityKey, newMetaIv));
                        String metaBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(meta.getBytes(), securityKey, newMetaIv));
                        String operationTypeBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(operationType.getBytes(), securityKey, newMetaIv));
                        String serverBabyCryCheckBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(serverBabyCryCheck.getBytes(), securityKey, newMetaIv));
                        String serverPeopleMotionCheckBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(serverPeopleMotionCheck.getBytes(), securityKey, newMetaIv));
                        String serverFaceCheckBase64 = Base64.encodeBase64URLSafeString(MiUploadUtils.encrypt(serverFaceCheck.getBytes(), securityKey, newMetaIv));
                        uploadMeta(preUploadEntry, extraInfoBase64, additionalInfoBase64, ignoreEventBase64, metaBase64, operationTypeBase64,
                                serverBabyCryCheckBase64, serverPeopleMotionCheckBase64, serverFaceCheckBase64,
                                newMetaIv, token, securityKey, isEnd);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                    //=============================上传Meta======================= end
                } else {
                    L.monitor.e("%s Upload file failed: %s", TAG, responseString);
                    if (mUploadCallback != null) {
                        mUploadCallback.uploadError(CloudErrorCode.ERROR_CODE_FILE_UPLOAD_ERROR, isEnd);
                    }
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                // 文件上传失败
                L.monitor.e("%s Upload file failed: %s", TAG, e.getMessage());
                if (mUploadCallback != null) {
                    mUploadCallback.uploadError(CloudErrorCode.ERROR_CODE_FILE_UPLOAD_ERROR, isEnd);
                }
            }
        });
    }

    //4.commit确认提交上传视频/图⽚的meta信息
    private void uploadMeta(PreUploadEntry preUploadEntry, String extraInfo, String additionalInfo, String ignoreEvent, String meta,
                           String operationType, String serverBabyCryCheck, String serverPeopleMotionCheck,
                           String serverFaceCheck, String iv,String token,String securityKey, boolean isEnd) {
        String uploadUrl = "/common/device/v2/meta";

        String params = "POST&" + uploadUrl + "&" + extraInfo + "&" + ignoreEvent
                + "&" + iv + "&" + meta + "&" + operationType
                + "&" + serverBabyCryCheck + "&" + serverFaceCheck+ "&" + serverPeopleMotionCheck;
        String paramsSign = MiUploadUtils.generateSign(params.getBytes());

        String baseUrl = preUploadEntry.getUrl();
        RequestBody requestBody = new FormBody.Builder()
                .add("iv", iv)
                .add("signature", paramsSign)
                .add("extraInfo", extraInfo)
                .add("additionalInfo", additionalInfo)
                .add("ignoreEvent", ignoreEvent)
                .add("meta", meta)
                .add("operationType", operationType)
                .add("serverBabyCryCheck", serverBabyCryCheck)
                .add("serverPeopleMotionCheck", serverPeopleMotionCheck)
                .add("serverFaceCheck", serverFaceCheck)
                .build();

        Request.Builder builder = new Request.Builder()
                .header("Cookie","serviceToken="+token)
                .url(baseUrl+uploadUrl)
                .post(requestBody);

        Request request = builder.build();
//        Log.v(TAG, "uploadFile!!!!!!!!:" + request.toString());

        Call call = mHttpClient.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                String responseString = response.body().string();
                if (response.isSuccessful()) {
//                    Log.i(TAG,"uploadMeta - responseString:" + responseString);
                    String metaDecryptData = MiUploadUtils.decryptData(responseString, securityKey);
                    // 前一个视频上传完成，继续上传下一个视频
                    if (mUploadCallback != null && !isEnd) {
                        mUploadCallback.doNextUpload(preUploadEntry);
                    }
                    L.monitor.i("%s Upload meta success", TAG);
                } else {
                    L.monitor.e("%s Upload meta failed: %s", TAG, responseString);
                    if (mUploadCallback != null) {
                        mUploadCallback.uploadError(CloudErrorCode.ERROR_CODE_META_UPLOAD_ERROR, isEnd);
                    }
                }
            }

            @Override
            public void onFailure(Call call, IOException e) {
                L.monitor.e("%s Upload meta failed: %s", TAG, e.getMessage());
                if (mUploadCallback != null) {
                    mUploadCallback.uploadError(CloudErrorCode.ERROR_CODE_META_UPLOAD_ERROR, isEnd);
                }
            }
        });
    }

    public interface UploadCallback {
        void getDomainUrlFailed();
        void getDomainUrlSuccess(String security, String iv);
        void preUploadCallback(PreUploadEntry preUploadEntry);
        void doNextUpload(PreUploadEntry preUploadEntry);
        void uploadError(int code, boolean isEnd);
    }
}
