#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include "imiplayer_core.h"
#include "imimusic_player.h"

#ifndef inline
#define inline __inline
#endif

#ifdef WIN32
#include <Windows.h>
HANDLE _thread_handle = NULL;
HANDLE _thread_signal = INVALID_HANDLE_VALUE;
FILE* _file = NULL;
#define log_error(x, ...)
#define log_info(x, ...)
#define pthread_mutex_lock(x)
#define pthread_mutex_unlock(x)
#else
#include <pthread.h>
#include <unistd.h>
#include <semaphore.h>
#include "tts/tts_pcm.h"    // need for: sdkPcm_play_forTTS
#include "elog.h"
pthread_t _thread_handle = 0;
static pthread_mutex_t _lists_mutex = PTHREAD_MUTEX_INITIALIZER;
sem_t _thread_signal;
#endif

imifiles_list* _files_list = NULL;
unsigned int _files_count = 0;
unsigned int _files_index = 0;
unsigned int _files_next = 0;
unsigned int _files_previous = 0;
unsigned int _achannel = 0;
unsigned int _asamplerate = 0;
unsigned int _abitrate = 0;
unsigned int _aduration = 0;
unsigned int _decode_continue = 0;
unsigned int _decode_pause = 0;
unsigned char* _decode_data = NULL;
unsigned int _decode_timestamp = 0;
unsigned int _seek_timestamp = 0;
unsigned int _decode_assign_resample = 0;
imiplayer_callback_event _user_fproc = NULL;
unsigned int _user_id = 0;
void* _user_data = NULL;
float _pcm_volume = 1.0f;

#define MODULE_IMIPLAYER_CORE "imiplayercore"

int _imiplayer_openfiles();
int _imiplayer_closefiles();
int _imiplayer_addfile();
int _imiplayer_removefile();
int _imiplayer_play();
int _imiplayer_stop();
int _imiplayer_pause();
int _imiplayer_resume();
int _imiplayer_seek();
int _imiplayer_get_duration();
int _imiplayer_get_postion();
int _imiplayer_next_file();
int _imiplayer_previous_file();
int _imiplayer_volume();

void imiplayer_init()
{
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_init");
	imimusic_init_ffmpeg();
}

int _imiplayer_openfiles(const imifiles_list* files, unsigned int files_count, imifiles_sequence files_seq)
{
	int len = 0, ret = 0;
	unsigned int i = 0;
	if (_files_list)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_openfiles files_list is exist");
		_imiplayer_closefiles();
		return -1;
	}
	_files_list = (imifiles_list*)malloc(sizeof(imifiles_list)*files_count);
	memset(_files_list, 0, sizeof(imifiles_list)*files_count);
	_files_count = files_count;
	for (i = 0; i < files_count; i++)
	{
		if (files[i].path != NULL)
		{
			len = strlen(files[i].path);
			_files_list[i].path = (char*)malloc(len+1);//\0
			memset(_files_list[i].path, 0, len+1);//\0
			memcpy(_files_list[i].path, files[i].path, len);
			log_info(MODULE_IMIPLAYER_CORE, "imiplayer_openfiles path = %s index = %d total = %d", _files_list[i].path, i, files_count);
		}
		else
		{
			log_error(MODULE_IMIPLAYER_CORE, "imiplayer_openfiles path is null index = %d total = %d", _files_list[i].path, i, files_count);
		}
	}
	_files_index = 0;
	_achannel = 0, _asamplerate = 0, _abitrate = 0, _aduration = 0;
	ret =  imimusic_open_file(_files_list[0].path, &_achannel, &_asamplerate, &_abitrate, &_aduration);
	if (ret != 0)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_openfiles imimusic_open_file error = %d", ret);
		imiplayer_closefiles();
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_openfiles success");
	return ret;
}

int imiplayer_openfiles(const imifiles_list* files, unsigned int files_count, imifiles_sequence files_seq)
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_openfiles(files, files_count, files_seq);
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_closefiles()
{
	unsigned int i = 0;
	int ret = 0;
	if (_files_list)
	{
		ret = _imiplayer_stop();
		ret = imimusic_close_file();
		for (i = 0; i < _files_count; i++)
		{
			free(_files_list[i].path);
			_files_list[i].path = NULL;
		}
		free(_files_list);
		_files_list = NULL;
		_files_count = 0;
		_files_index = 0;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_closefiles success");
	return ret;
}

int imiplayer_closefiles()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_closefiles();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_addfile(char* path)
{
	unsigned int i = 0;
	int len = 0;
	imifiles_list* new_list = NULL;
	unsigned int new_count = _files_count;
	new_count = new_count + 1;
	if (_files_list == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_addfile files_list is unexist");
		return -1;
	}
	if (path == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_addfile path is null");
		return -1;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_addfile path = %s", path);
	new_list = (imifiles_list*)malloc(sizeof(imifiles_list)*new_count);
	memset(new_list, 0, sizeof(imifiles_list)*new_count);
	for (i = 0; i < _files_count; i++)
	{
		if (_files_list[i].path != NULL)
		{
			len = strlen(_files_list[i].path);
			new_list[i].path = (char*)malloc(len+1);//\0
			memset(new_list[i].path, 0, len+1);//\0
			memcpy(new_list[i].path, _files_list[i].path, len);
			log_info(MODULE_IMIPLAYER_CORE, "imiplayer_addfile path = %s index = %d total = %d", new_list[i].path, i, new_count);
		}
		else
		{
			log_error(MODULE_IMIPLAYER_CORE, "imiplayer_addfile path is null index = %d total = %d", i, _files_count);
		}
	}
	len = strlen(path);
	new_list[new_count-1].path = (char*)malloc(len+1);//\0
	memset(new_list[new_count-1].path, 0, len+1);//\0
	memcpy(new_list[new_count-1].path, path, len);
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_addfile path = %s index = %d total = %d", new_list[new_count-1].path, new_count-1, new_count);
	for (i = 0; i < _files_count; i++)
	{
		free(_files_list[i].path);
		_files_list[i].path = NULL;
	}
	free(_files_list);
	_files_list = new_list;
	_files_count = new_count;
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_addfile success");
	return 0;
}

int imiplayer_addfile(char* path)
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_addfile(path);
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_removefile(char* path)
{
	unsigned int i = 0, j = 0;
	int len = 0;
	imifiles_list* new_list = NULL;
	unsigned int new_count = _files_count;
	new_count = new_count - 1;
	if (_files_list == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_removefile files_list is unexist");
		return -1;
	}
	if (path == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_removefile path is null");
		return -1;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_removefile path = %s", path);
	new_list = (imifiles_list*)malloc(sizeof(imifiles_list)*new_count);
	memset(new_list, 0, sizeof(imifiles_list)*new_count);
	for (i = 0; i < _files_count; i++)
	{
		if (_files_list[i].path != NULL)
		{
			if (strcmp(_files_list[i].path, path) == 0)
			{

			}
			else
			{
				len = strlen(_files_list[i].path);
				new_list[j].path = (char*)malloc(len+1);//\0
				memset(new_list[j].path, 0, len+1);//\0
				memcpy(new_list[j].path, _files_list[i].path, len);
				j++;
				log_info(MODULE_IMIPLAYER_CORE, "imiplayer_removefile path = %s index = %d total = %d", new_list[j].path, i, new_count);
			}
		}
		else
		{
			log_error(MODULE_IMIPLAYER_CORE, "imiplayer_removefile path is null index = %d total = %d", i, _files_count);
		}
	}
	for (i = 0; i < _files_count; i++)
	{
		free(_files_list[i].path);
		_files_list[i].path = NULL;
	}
	free(_files_list);
	_files_list = new_list;
	_files_count = new_count;
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_removefile success");
	return 0;
}

int imiplayer_removefile(char* path)
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_removefile(path);
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _play_file_by_next()
{
	int ret = -3;
	if (_files_index < _files_count && _files_index >= 0)
	{
		imimusic_close_file();
		imimusic_resample_fini();
		ret = imimusic_open_file(_files_list[_files_index].path, &_achannel, &_asamplerate, &_abitrate, &_aduration);
		if (ret != 0)
		{
			log_error(MODULE_IMIPLAYER_CORE, "play_file_by_next imimusic_open_file error = %d index = %d", ret, _files_index);
			if (_user_fproc) _user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_open_fail, _user_id, _user_data);
			_files_index++;
			return _play_file_by_next();
		}
		if (_decode_assign_resample != 0 && _decode_assign_resample != _asamplerate)
		{
			log_info(MODULE_IMIPLAYER_CORE, "play_file_by_next imimusic_resample_init asamplerate = %d assign_resample = %d index = %d", _asamplerate, _decode_assign_resample, _files_index);
			ret = imimusic_resample_init(1, _asamplerate, 1, _decode_assign_resample);
			if (ret != 0)
			{
				log_error(MODULE_IMIPLAYER_CORE, "play_file_by_next imimusic_resample_init error = %d index = %d", ret, _files_index);
				if (_user_fproc) _user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_open_fail, _user_id, _user_data);
				_files_index++;
				return _play_file_by_next();
			}
		}
		if (_user_fproc) _user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_begin_of_file, _user_id, _user_data);
		_decode_timestamp = 0;
		log_info(MODULE_IMIPLAYER_CORE, "play_file_by_next success index = %d", _files_index);
	}
	return ret;
}

int play_file_by_next()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _play_file_by_next();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

#ifdef WIN32
DWORD WINAPI thread_decode(void* p)
{
	int ret = 0;
	unsigned int data_len = 0;
	unsigned int nb_samples = 0;
	while (_decode_continue)
	{
		if (_decode_pause == 0)
		{
			if (_files_next == 1)
			{
				_files_next = 0;
				_files_index++;
				ret = play_file_by_next();
				if (ret != 0)
				{
					_files_index--;
					continue;
				}
			}
			if (_files_previous == 1)
			{
				_files_previous = 0;
				_files_index--;
				ret = play_file_by_next();
				if (ret != 0)
				{
					_files_index++;
					continue;
				}
			}
			ret = imimusic_get_one_frame(_decode_data, &data_len, &_seek_timestamp, &nb_samples, _pcm_volume);
			if (ret == 0)
			{
				_decode_timestamp = _seek_timestamp;
				_seek_timestamp = 0;
				if (_decode_assign_resample != 0 && _decode_assign_resample != _asamplerate)
				{
					ret = imimusic_resample_data(_decode_data, data_len, nb_samples, _decode_data, &data_len);
					if (ret != 0)
					{
						if (_user_fproc) _user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_data_fail, _user_id, _user_data);
						break;
					}
				}
				fwrite(_decode_data, data_len, 1, _file);
			}
			else if (ret == -2)
			{
				_decode_timestamp = _aduration;
				if (_user_fproc) _user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_end_of_file, _user_id, _user_data);
				_files_index++;
				ret = play_file_by_next();
				if (ret != 0)
				{
					//decode complete all
					_files_index = 0;
					play_file_by_next();
				}
			}
			else
			{

			}
		}
		else
		{
			WaitForSingleObject(_thread_signal, INFINITE);
		}
	}
	return ret;
}
#else
void* thread_decode(void* p)
{
	int ret = 0;
	unsigned int data_len = 0;
	while (_decode_continue)
	{
		if (_decode_pause == 0)
		{
			if (_files_next == 1)
			{
				_files_next = 0;
				_files_index++;
				ret = play_file_by_next();
				if (ret != 0)
				{
					_files_index--;
					log_error(MODULE_IMIPLAYER_CORE, "thread_decode files_next error = %d index = %d", ret, _files_index);
					continue;
				}
				tts_pcm_clear();
			}
			if (_files_previous == 1)
			{
				_files_previous = 0;
				_files_index--;
				ret = play_file_by_next();
				if (ret != 0)
				{
					_files_index++;
					log_error(MODULE_IMIPLAYER_CORE, "thread_decode files_previous error = %d index = %d", ret, _files_index);
					continue;
				}
				tts_pcm_clear();
			}
			ret = imimusic_get_one_frame(_decode_data, &data_len, &_decode_timestamp, &nb_samples, _pcm_volume);
			if (ret == 0)
			{
				_decode_timestamp = 0;
				if (_decode_assign_resample != 0 && _decode_assign_resample != _asamplerate)
				{
					ret = imimusic_resample_data(_decode_data, data_len, nb_samples, _decode_data, &data_len);
					if (ret != 0)
					{
						log_error(MODULE_IMIPLAYER_CORE, "thread_decode imimusic_resample_data error = %d index = %d", ret, _files_index);
						if (_user_fproc)
						{
							pthread_mutex_lock(&_lists_mutex);
							_user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_data_fail, _user_id, _user_data);
							pthread_mutex_unlock(&_lists_mutex);
						}
						break;
					}
				}
				ret = tts_pcm_play((const char*)_decode_data, data_len);
			}
			else if (ret == -2)
			{
				log_info(MODULE_IMIPLAYER_CORE, "thread_decode end of file index = %d", _files_index);
				_decode_timestamp = _aduration;
				if (_user_fproc)
				{
					pthread_mutex_lock(&_lists_mutex);
					_user_fproc(_files_list[_files_index].path, _files_index, imifiles_event_end_of_file, _user_id, _user_data);
					pthread_mutex_unlock(&_lists_mutex);
				}
				_files_index++;
				ret = play_file_by_next();
				if (ret != 0)
				{
					//decode complete all
					log_info(MODULE_IMIPLAYER_CORE, "thread_decode play_file_by_next end of list index = %d", _files_index);
					_files_index = 0;
					play_file_by_next();
				}
				tts_pcm_clear();
			}
			else
			{

			}
		}
		else
		{
			log_info(MODULE_IMIPLAYER_CORE, "thread_decode is pause waiting");
			sem_wait(&_thread_signal);
			log_info(MODULE_IMIPLAYER_CORE, "thread_decode is resume decoding");
		}
	}
	return NULL;
}
#endif

int _imiplayer_play(unsigned int assign_resample, float assign_volume, imiplayer_callback_event fproc, unsigned int id, void* userdata)
{
	unsigned long thread_id = 0;
	int ret = 0;
	if (_files_list == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_play files_list is unexist");
		return -1;
	}
	if (_thread_handle)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_play thread_handle is exist");
		_imiplayer_stop();
		return -1;
	}
	_files_next = 0;
	_files_previous = 0;

	_decode_continue = 1;
	_decode_pause = 0;
	_decode_data = (unsigned char*)malloc(100*1024);
	memset(_decode_data, 0, 100*1024);
	_decode_timestamp = 0;
	_seek_timestamp = 0;
	_decode_assign_resample = assign_resample;

	_user_fproc = fproc;
	_user_id = id;
	_user_data = userdata;

	if (_decode_assign_resample != 0 && _decode_assign_resample != _asamplerate)
	{
		log_info(MODULE_IMIPLAYER_CORE, "imiplayer_play imimusic_resample_init asamplerate = %d assign_resample = %d", _asamplerate, _decode_assign_resample);
		ret = imimusic_resample_init(1, _asamplerate, 1, _decode_assign_resample);
		if (ret != 0)
		{
			log_error(MODULE_IMIPLAYER_CORE, "imiplayer_play imimusic_resample_init error = %d", ret);
			return -1;
		}
	}

	_pcm_volume = assign_volume;

#ifdef WIN32
	_file = fopen("C:\\imiplayer_test_pcm.pcm", "wb+");
	_thread_signal = CreateEvent(NULL, FALSE, FALSE, NULL);
	_thread_handle = CreateThread(NULL, 0, (LPTHREAD_START_ROUTINE)thread_decode, (LPVOID)NULL, 0, &thread_id);
	if(thread_id == 0 || _thread_handle == NULL)
	{
		_thread_handle = NULL;
		return GetLastError();
	}
#else
	tts_pcm_clear();
	sem_init(&_thread_signal, 0, 0);
	ret = pthread_create(&_thread_handle, NULL, thread_decode, NULL);
	if (ret != 0)
	{
		_thread_handle = 0;
		return ret;
	}
#endif
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_play success");
	return 0;
}

int imiplayer_play(unsigned int assign_resample, float assign_volume, imiplayer_callback_event fproc, unsigned int id, void* userdata)
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_play(assign_resample, assign_volume, fproc, id, userdata);
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_stop()
{
	if (_files_list == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_stop files_list is unexist");
		return -1;
	}
	if (_thread_handle)
	{
		_files_next = 0;
		_files_previous = 0;

		_decode_continue = 0;
		_imiplayer_resume();

#ifdef WIN32
		fclose(_file);
		CloseHandle(_thread_signal);
		_thread_signal = INVALID_HANDLE_VALUE;
		WaitForSingleObject(_thread_handle, INFINITE);
		CloseHandle(_thread_handle);
		_thread_handle = NULL;
#else
		sem_destroy(&_thread_signal);
		pthread_join(_thread_handle, 0);
		_thread_handle = 0;
		tts_pcm_clear();
#endif

		imimusic_resample_fini();

		_user_fproc = NULL;
		_user_id = 0;
		_user_data = NULL;

		free(_decode_data);
		_decode_data = NULL;
		_decode_timestamp = 0;
		_seek_timestamp = 0;
		_decode_assign_resample = 0;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_stop success");
	return 0;
}

int imiplayer_stop()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_stop();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_pause()
{
	if (_files_list == NULL || _thread_handle == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_pause files_list thread_handle is unexist");
		return -1;
	}
	_decode_pause = 1;
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_pause success");
	return 0;
}

int imiplayer_pause()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_pause();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_resume()
{
	if (_files_list == NULL || _thread_handle == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_resume files_list thread_handle is unexist");
		return -1;
	}
	if (_decode_pause != 1)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_resume is not pause");
		return -1;
	}
	_decode_pause = 0;
#ifdef WIN32
	SetEvent(_thread_signal);
#else
	sem_post(&_thread_signal);
#endif
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_resume success");
	return 0;
}

int imiplayer_resume()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_resume();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_seek(unsigned int timestamp)
{
	if (_files_list == NULL || _thread_handle == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_seek files_list thread_handle is unexist");
		return -1;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_seek timestamp = %d", timestamp);
	_seek_timestamp = timestamp;
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_seek success");
	return 0;
}

int imiplayer_seek(unsigned int timestamp)
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_seek(timestamp);
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_get_duration()
{
	if (_files_list == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_get_duration files_list is unexist");
		return -1;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_get_duration success duration = %d", _aduration);
	return _aduration;
}

int imiplayer_get_duration()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_get_duration();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_get_postion()
{
	if (_files_list == NULL || _thread_handle == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_get_postion files_list thread_handle is unexist");
		return -1;
	}
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_get_duration success postion = %d", _decode_timestamp);
	return _decode_timestamp;
}

int imiplayer_get_postion()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_get_postion();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_next_file()
{
	if (_files_list == NULL || _thread_handle == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_next_file files_list thread_handle is unexist");
		return -1;
	}
	_files_next = 1;
	_imiplayer_resume();
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_next_file success");
	return 0;
}

int imiplayer_next_file()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_next_file();
	pthread_mutex_unlock(&_lists_mutex);
	return 0;
}

int _imiplayer_previous_file()
{
	if (_files_list == NULL || _thread_handle == NULL)
	{
		log_error(MODULE_IMIPLAYER_CORE, "imiplayer_previous_file files_list thread_handle is unexist");
		return -1;
	}
	_files_previous = 1;
	_imiplayer_resume();
	log_info(MODULE_IMIPLAYER_CORE, "imiplayer_previous_file success");
	return 0;
}

int imiplayer_previous_file()
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_previous_file();
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}

int _imiplayer_volume(float volume)
{
	_pcm_volume = volume
	log_info(MODULE_IMIPLAYER_CORE, "_imiplayer_volume success volume = %f", volume);
	return 0;
}

int imiplayer_volume(float volume)
{
	int ret = 0;
	pthread_mutex_lock(&_lists_mutex);
	ret = _imiplayer_volume(volume);
	pthread_mutex_unlock(&_lists_mutex);
	return ret;
}