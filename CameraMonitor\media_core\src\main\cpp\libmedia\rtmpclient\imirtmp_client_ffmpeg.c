#include <stdlib.h>
#include <string.h>
#ifndef WIN32
#include <pthread.h>
#else
#include <Windows.h>
#include "imimedia_win2linux.h"
#endif

#include "imirtmp_client_ffmpeg.h"
#include "imiffmpeg_common.h"
#include "imiparser_aac.h"

#if defined(WIN32) && !defined(__cplusplus)  
#define inline __inline  
#endif 

#ifdef __cplusplus
extern "C"{
#endif
#include <libavformat/avformat.h>
#include <libavformat/avio.h>
#include <libavutil/mem.h>
#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#endif

typedef struct imirtmp_client_ffmpeg_info_s {
#ifndef WIN32
	pthread_mutex_t _lock_mutex;
#endif
	int _getfirstframe;
	AVFormatContext* _format_context;
	video_codec_id _video_codec;
	audio_codec_id _audio_codec;
	AVStream* _video_stream;
	unsigned int _video_frame_index;
	unsigned int _video_fps;
	unsigned long long _video_lasttimestamp;
	unsigned int _video_lastpts;
	unsigned char* _video_extradata;
	unsigned int _video_extradata_size;
	AVStream* _audio_stream;
	unsigned char* _audio_extradata;
	unsigned int _audio_extradata_size;
	unsigned long long _audio_lasttimestamp;
} imirtmp_client_ffmpeg_info_s, *imirtmp_client_ffmpeg_info_t;

AVDictionary* rtmp_set_options_ffmpeg()
{
	AVDictionary* options = NULL;
	char latency[32] = { 0 };
	av_dict_set(&options, "buffer_size", "0", 0);
	av_dict_set(&options, "max_delay", "500000", 0);
	av_dict_set(&options, "fflags", "nobuffer", 0);
	sprintf(latency, "%d", 100000);
	av_dict_set(&options, "max_interleave_delta", latency, 0);
	return options;
}

void imirtmp_client_init_ffmpeg()
{
	printf("imirtmp_client_init_ffmpeg\n");
	av_register_all();
	avformat_network_init();
}

int _rtmp_open(imirtmp_client_ffmpeg_info_t handle,
	const char* url,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate)
{
	int ret = IMIMEDIA_OK;
	unsigned int i = 0;
	if (handle == 0) return IMIMEDIA_PARAMS_ERROR;//ASSERT WARNING
	//////////////////////////////////////////////////////////////////////////
	ret = avformat_alloc_output_context2(&handle->_format_context, NULL, "flv", url); //RTMP
	if (ret != 0 || handle->_format_context == NULL) {
		printf("imirtmp_client_open avformat_alloc_output_context2 error\n");
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	//////////////////////////////////////////////////////////////////////////
	switch (video)
	{
	case video_codec_h264:
		handle->_format_context->oformat->video_codec = AV_CODEC_ID_H264;
		break;
	case video_codec_h265:
		handle->_format_context->oformat->video_codec = AV_CODEC_ID_H265;
		break;
	default:
		printf("imirtmp_client_open video_codec_id default = %d\n", video);
		goto error;
	}
	switch (audio)
	{
	case audio_codec_g711a:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_PCM_ALAW;
		break;
	case audio_codec_g711u:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_PCM_MULAW;
		break;
	case audio_codec_aac:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_AAC;
		break;
	default:
		printf("imirtmp_client_open audio_codec_id default = %d\n", audio);
		goto error;
	}
	strncpy(handle->_format_context->filename, url, sizeof(handle->_format_context->filename));
	//////////////////////////////////////////////////////////////////////////
	handle->_video_stream = avformat_new_stream(handle->_format_context, NULL);
	if (handle->_video_stream == NULL) {
		printf("imirtsp_client_record avformat_new_stream video error\n");
		goto error;
	}
	handle->_video_stream->id = handle->_format_context->nb_streams - 1;
	handle->_video_stream->codecpar->codec_type = AVMEDIA_TYPE_VIDEO;
	switch (video)
	{
	case video_codec_h264:
		handle->_video_stream->codecpar->codec_id = AV_CODEC_ID_H264;
		handle->_video_stream->codecpar->level = 0x7F;
		break;
	case video_codec_h265:
		handle->_video_stream->codecpar->codec_id = AV_CODEC_ID_H265;
		break;
	default:
		printf("imirtsp_client_record video_codec_id default = %d\n", video);
		goto error;
	}
	handle->_video_stream->codecpar->width = vwidth;
	handle->_video_stream->codecpar->height = vheight;
	handle->_video_stream->codecpar->codec_tag = 0;
	handle->_video_stream->codec->time_base = av_d2q(1.0/vfps, 255);
	handle->_video_stream->r_frame_rate = av_d2q(vfps, 255);
	handle->_video_stream->time_base.num = 1;
	handle->_video_stream->time_base.den = IMI_VIDEO_RTMP_TIME_BASE;
	handle->_video_fps = vfps;
	handle->_video_codec = video;
	handle->_video_extradata_size = 0;
	handle->_video_extradata = (unsigned char*)malloc(4096);
	memset(handle->_video_extradata, 0, 4096);
	//////////////////////////////////////////////////////////////////////////
	handle->_audio_stream = avformat_new_stream(handle->_format_context, NULL);
	if (handle->_audio_stream == NULL) {
		printf("imirtsp_client_record avformat_new_stream audio error\n");
		goto error;
	}
	handle->_audio_stream->id = handle->_format_context->nb_streams - 1;
	handle->_audio_stream->codecpar->codec_type = AVMEDIA_TYPE_AUDIO;
	switch (audio)
	{
	case audio_codec_g711a:
		{
			handle->_audio_stream->codecpar->codec_id = AV_CODEC_ID_PCM_ALAW;
			handle->_audio_stream->codecpar->sample_rate = asamplerate;
			handle->_audio_stream->codecpar->channels = achannel;
		}
		break;
	case audio_codec_g711u:
		{
			handle->_audio_stream->codecpar->codec_id = AV_CODEC_ID_PCM_MULAW;
			handle->_audio_stream->codecpar->sample_rate = asamplerate;
			handle->_audio_stream->codecpar->channels = achannel;
		}
		break;
	case audio_codec_aac:
		{
			handle->_audio_stream->codecpar->codec_id = AV_CODEC_ID_AAC;
			handle->_audio_stream->codecpar->sample_rate = asamplerate;
			handle->_audio_stream->codecpar->channels = achannel;
			handle->_audio_stream->codecpar->profile = FF_PROFILE_AAC_LOW;
			handle->_audio_stream->codecpar->level = 0x02;
			handle->_audio_extradata = imi_make_aac_track_configure(0x02, imi_make_aac_header_sampling_frequency_index(asamplerate), achannel);
			handle->_audio_extradata_size = 2;
			handle->_audio_stream->codecpar->extradata = handle->_audio_extradata;
			handle->_audio_stream->codecpar->extradata_size = handle->_audio_extradata_size;
			handle->_audio_stream->codecpar->frame_size = (int)((double)IMI_AAC_TIME_BASE*(double)(handle->_audio_stream->codecpar->sample_rate/(double)8000));
		}
		break;
	default:
		printf("imirtsp_client_record audio_codec_id default = %d\n", audio);
		goto error;
	}
	handle->_audio_stream->codec->time_base.num = 1;
	handle->_audio_stream->codec->time_base.den = asamplerate;
	handle->_audio_stream->time_base.num = 1;
	handle->_audio_stream->time_base.den = asamplerate;
	handle->_audio_codec = audio;
	//////////////////////////////////////////////////////////////////////////
	av_dump_format(handle->_format_context, 0, url, 1);
	if (!(handle->_format_context->oformat->flags & AVFMT_NOFILE)) {
		ret = avio_open(&handle->_format_context->pb, url, AVIO_FLAG_WRITE);
		if (ret < 0) {
			printf("imirtsp_client_record avio_open url = %s error = %d\n", url, ret);
			goto error;
		}
	}
	//////////////////////////////////////////////////////////////////////////
	printf("imirtsp_client_record success\n");
	return IMIMEDIA_OK;

error:
	if (handle->_video_stream) {
		avcodec_close(handle->_video_stream->codec);
		handle->_video_stream = NULL;
	}
	if (handle->_video_extradata) {
		free(handle->_video_extradata);
		handle->_video_extradata = NULL;
	}
	if (handle->_audio_stream) {
		if (handle->_audio_stream->codec) {
			handle->_audio_stream->codec->extradata = NULL;
			handle->_audio_stream->codec->extradata_size = 0;
		}
		avcodec_close(handle->_audio_stream->codec);
		handle->_audio_stream = NULL;
	}
	if (handle->_audio_extradata) {
		free(handle->_audio_extradata);
		handle->_audio_extradata = NULL;
	}
	avformat_close_input(&handle->_format_context);
	handle->_format_context = NULL;
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imirtmp_client_open(const char* url,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	imirtmp_client_ffmpeg_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imirtmp_client_ffmpeg_info_t handle_impl = NULL;
	handle_impl = (imirtmp_client_ffmpeg_info_t)malloc(sizeof(imirtmp_client_ffmpeg_info_s));
	if (handle_impl == NULL) {
		printf("imirtsp_client_ffmpeg_info_t malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imirtmp_client_ffmpeg_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _rtmp_open(handle_impl, url, video, audio, vfps, vwidth, vheight, achannel, asamplerate);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imirtmp_client_ffmpeg_info_t handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _rtmp_write_video(imirtmp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp)
{
	int ret = IMIMEDIA_OK;
	int key = 0;
	unsigned int offset = 0;
	AVPacket pkt = { 0 };
	imi_nalu_array_t nalu_array = NULL;
	unsigned char* data_buff_src = (unsigned char*)data;
	if (handle->_video_stream == NULL) {
		printf("imirtmp_client_write_video_frame video_stream is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	nalu_array = imi_get_nalu_array(data_buff_src, data_len);
	if (nalu_array == NULL) {
		printf("imirtmp_client_write_video_frame get_nalu_info error\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	switch (handle->_video_codec)
	{
	case video_codec_h264:
		key = ffmpeg_get_extradata_h264(nalu_array, handle->_video_extradata, &handle->_video_extradata_size, &offset);
		break;
	case video_codec_h265:
		key = ffmpeg_get_extradata_h265(nalu_array, handle->_video_extradata, &handle->_video_extradata_size, &offset);
		break;
	default:
		printf("imirtmp_client_write_video_frame video_codec_id default = %d\n", handle->_video_codec);
		imi_free_nalu_array(nalu_array);
		return IMIMEDIA_PARAMS_ERROR;
	}
	imi_free_nalu_array(nalu_array);
	if (key == 0 && handle->_getfirstframe == 0) {
		printf("imirtmp_client_write_video_frame getfirstframe is not key frame\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	if (handle->_getfirstframe == 0) {
		int ret = IMIMEDIA_OK;
		AVDictionary* options = NULL;
		handle->_video_stream->codec->extradata = handle->_video_extradata;
		handle->_video_stream->codec->extradata_size = handle->_video_extradata_size;
		options = rtmp_set_options_ffmpeg();
		if (options == NULL) {
			printf("imirtmp_client_write_video_frame set_options_ffmpeg error\n");
			return IMIMEDIA_CAPABILITY_ERROR;
		}
		ret = avformat_write_header(handle->_format_context, &options);
		if (ret < 0) {
			ffmpeg_err2str("imi_mp4_write_video_frame avformat_write_header", ret);
			return IMIMEDIA_CAPABILITY_ERROR;
		}
		av_dict_free(&options);
		options = NULL;
		handle->_getfirstframe = 1;
	}
	av_init_packet(&pkt);
	if (key == 1) {
		pkt.flags |= AV_PKT_FLAG_KEY;
	}
	pkt.data = (uint8_t*)data_buff_src;
	pkt.size = data_len;
	if (vtimestamp == 0) {
		pkt.pts = av_rescale(handle->_video_frame_index++,
			handle->_video_stream->time_base.den,
			handle->_video_stream->codec->time_base.den);
		pkt.duration = handle->_video_stream->time_base.den/handle->_video_fps;
	} else {
		if (handle->_video_lasttimestamp == 0) {
			pkt.pts = av_rescale(handle->_video_frame_index++,
				handle->_video_stream->time_base.den,
				handle->_video_stream->codec->time_base.den);
			pkt.duration = handle->_video_stream->time_base.den/handle->_video_fps;
		} else {
			int64_t time = IMI_PTS2TIME_SCALE(vtimestamp, handle->_video_lasttimestamp, handle->_video_stream->time_base.den);
			pkt.pts = handle->_video_lastpts + time;
			pkt.duration = time;
		}
		handle->_video_lasttimestamp = vtimestamp;
	}
	handle->_video_lastpts = pkt.pts;
	pkt.stream_index = handle->_video_stream->index;
	pkt.dts = pkt.pts;
	pkt.pos = -1;
	ret = av_interleaved_write_frame(handle->_format_context, &pkt);
	if (ret != 0) {
 		ffmpeg_err2str("imirtmp_client_write_video_frame av_interleaved_write_frame", ret);
#ifdef WIN32
		Sleep(1);
#else
		usleep(1000);//1ms
#endif
	}
	return ret;
}

int imirtmp_client_write_video_frame(imirtmp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _rtmp_write_video(handle, data, data_len, vtimestamp);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _rtmp_write_audio(imirtmp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp)
{
	int ret = IMIMEDIA_OK;
	AVPacket pkt = { 0 };
	int one_frame_ms = 0;
	static unsigned int index = 0;
	unsigned char* data_buff_src = (unsigned char*)data;
	if (handle->_audio_stream == NULL) {
		printf("imirtmp_client_write_audio_frame audio_stream is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	if (handle->_getfirstframe == 0) {
		printf("imirtmp_client_write_audio_frame doesn't' get first keyframe\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	av_init_packet(&pkt);
	pkt.flags |= AV_PKT_FLAG_KEY;
	switch (handle->_audio_codec)
	{
	case audio_codec_aac:
		{
			int header_len = AAC_ADTS_HEADER;
			pkt.data = (uint8_t*)data_buff_src + header_len;
			pkt.size = data_len - header_len;
			pkt.duration = handle->_audio_stream->codecpar->frame_size;
			one_frame_ms = handle->_audio_stream->codecpar->frame_size * 1000 / handle->_audio_stream->codecpar->sample_rate;
		}
		break;
	case audio_codec_g711a:
		{
			pkt.data = (uint8_t*)data_buff_src;
			pkt.size = data_len;
			pkt.duration = data_len;
			one_frame_ms = data_len / handle->_audio_stream->codecpar->sample_rate * 1000;
		}
		break;
	default:
		printf("imirtmp_client_write_audio_frame audio_codec_id default = %d\n", handle->_audio_codec);
		return IMIMEDIA_PARAMS_ERROR;
	}
	index++;
	pkt.stream_index = handle->_audio_stream->index;
	pkt.pts = handle->_audio_lasttimestamp + one_frame_ms;
	pkt.dts = pkt.pts;
	handle->_audio_lasttimestamp = (unsigned long long)pkt.pts;
	pkt.pos = -1;
	ret = av_interleaved_write_frame(handle->_format_context, &pkt);
	if (ret != 0) {
		ffmpeg_err2str("imirtmp_client_write_audio_frame av_interleaved_write_frame", ret);
#ifdef WIN32
		Sleep(1);
#else
		usleep(1000);//1ms
#endif
	}
	return ret;
}

int imirtmp_client_write_audio_frame(imirtmp_client_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _rtmp_write_audio(handle, data, data_len, atimestamp);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _rtmp_close(imirtmp_client_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	if (handle->_video_stream) {
		if (handle->_video_stream->codec) {
			handle->_video_stream->codec->extradata = NULL;
			handle->_video_stream->codec->extradata_size = 0;
		}
		avcodec_close(handle->_video_stream->codec);
		handle->_video_stream = NULL;
	}
	if (handle->_video_extradata) {
		free(handle->_video_extradata);
		handle->_video_extradata = NULL;
	}
	if (handle->_audio_stream) {
		if (handle->_audio_stream->codec) {
			handle->_audio_stream->codec->extradata = NULL;
			handle->_audio_stream->codec->extradata_size = 0;
		}
		avcodec_close(handle->_audio_stream->codec);
		handle->_audio_stream = NULL;
	}
	if (handle->_audio_extradata) {
		free(handle->_audio_extradata);
		handle->_audio_extradata = NULL;
	}
	avformat_close_input(&handle->_format_context);
	handle->_format_context = NULL;
	handle->_video_frame_index = 0;
	handle->_video_fps = 0;
	handle->_video_lasttimestamp = 0;
	handle->_video_lastpts = 0;
	printf("imirtmp_client_close success\n");
	return ret;
}

int imirtmp_client_close(imirtmp_client_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	printf("imirtmp_client_close handle = %x\n", handle);
	ret = _rtmp_close(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	pthread_mutex_destroy(&handle->_lock_mutex);
	free(handle);
	return ret;
}