plugins {
    id 'com.android.application'
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        applicationId "com.xiaomi.mico.persistent.monitor"
        minSdkVersion 28
        targetSdkVersion 30
        versionCode 2
        versionName "1.1"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        debug {
            storeFile new File(file(".").getAbsolutePath() + "/../keystore/platform.keystore").getAbsoluteFile()
            storePassword "android"
            keyAlias "androiddebugkey"

            v1SigningEnabled true
            v2SigningEnabled true
        }

        release {
            storeFile new File(file(".").getAbsolutePath() + "/../keystore/platform.keystore").getAbsoluteFile()
            storePassword "android"
            keyAlias "androiddebugkey"
            keyPassword "android"

            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    android {
        lintOptions {
            abortOnError false
        }
    }

    this.project.afterEvaluate { project ->
        project.tasks.each { task ->
            if (task.toString().contains("build")) {
                task.doLast {
                    android.applicationVariants.all { variant ->
                        // 默认生成apk的文件夹
                        def workFolder = "${project.getProjectDir().path}/build/outputs/apk/release"
                        // 定义目标文件夹
                        def destFolder = new File("${project.getProjectDir().path}/../release/")
                        try {
                            if (!destFolder.exists()) {
                                destFolder.mkdir()
                            }
                            copy {
                                from "${workFolder}"
                                into "${destFolder}/"
                                rename {
                                    "MicoCameraMonitor.apk"
                                }
                            }
                        } catch (Exception e) {
                            print e
                        }
                    }
                }
            }
        }
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation project(path: ':mi_camera_monitoring')
    implementation project(path: ':media_core')
    api project(path: ':media_yuv')
    implementation 'com.google.code.gson:gson:2.11.0'
    implementation 'com.squareup.okhttp3:okhttp:3.12.8'
    implementation 'com.github.bumptech.glide:glide:4.16.0'
    implementation project(path: ':socket-client')

    implementation 'androidx.appcompat:appcompat:1.1.0'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    testImplementation 'junit:junit:4.+'
    androidTestImplementation 'androidx.test.ext:junit:1.1.3'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.4.0'
}