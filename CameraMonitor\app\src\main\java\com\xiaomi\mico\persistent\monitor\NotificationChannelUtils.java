package com.xiaomi.mico.persistent.monitor;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;

public class NotificationChannelUtils {
    public static final String CHANNEL_NAME = "Camera Monitor Channel";
    public static final String CHANNEL_ID = "com.xiaomi.mico.persistent.monitor";

    private static final Object lock = new Object();
    private static boolean isCreated = false;

    public static Notification createServiceNotification(Context context, String title) {
        createNotificationChannel(context);
        return new Notification.Builder(context, CHANNEL_ID)
                .setContentTitle(title)
                .setContentText("Service")
                .setSmallIcon(android.R.drawable.stat_notify_sync)
                .setTicker("Running")
                .build();
    }

    private static void createNotificationChannel(Context context) {
        synchronized (lock) {
            if (!isCreated) {
                NotificationChannel channel =
                        new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_HIGH);
                NotificationManager notificationManager =
                        (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
                notificationManager.createNotificationChannel(channel);
                isCreated = true;
            }
        }
    }
}
