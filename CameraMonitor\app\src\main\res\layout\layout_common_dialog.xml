<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/mico_dialog_common_bg"
    android:paddingLeft="@dimen/dialog_view_padding_start"
    android:paddingRight="@dimen/dialog_view_padding_start"
    android:paddingBottom="@dimen/dialog_view_padding_top"
    android:paddingTop="@dimen/dialog_view_padding_top"
    android:orientation="vertical">

    <TextView
        android:id="@+id/title_tv"
        android:layout_width="match_parent"
        android:layout_height="@dimen/dialog_view_title_height"
        tools:text="@string/ims_dialog_open_mic_content"
        android:textSize="@dimen/dialog_view_title_size"
        android:singleLine="true"
        android:textColor="@color/dialog_view_title_text" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:paddingTop="@dimen/dialog_view_content_padding_top"
        android:paddingBottom="@dimen/dialog_view_content_padding_bottom"
        android:layout_weight="1">

        <TextView
            android:id="@+id/content_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/dialog_view_content_text_size"
            android:text="@string/ims_dialog_open_mic_title"
            android:textColor="@color/dialog_view_content_text" />
    </ScrollView>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:gravity="right"
        android:layout_height="@dimen/dialog_view_negative_btn_height">

        <Button
            android:id="@+id/ok_btn"
            android:text="@string/ims_dialog_enter"
            android:paddingLeft="@dimen/dialog_view_negative_padding_start"
            android:paddingRight="@dimen/dialog_view_negative_padding_start"
            android:textSize="@dimen/dialog_view_negative_text_size"
            android:textColor="@color/dialog_view_ok_btn_text"
            android:background="@drawable/bg_dialog_btn"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />

        <Button
            android:id="@+id/cancel_btn"
            android:text="@string/ims_dialog_cancel"
            android:textSize="@dimen/dialog_view_negative_text_size"
            android:paddingLeft="@dimen/dialog_view_negative_cancel_padding_start"
            android:paddingRight="@dimen/dialog_view_negative_cancel_padding_start"
            android:layout_marginLeft="20dp"
            android:textColor="@color/dialog_view_content_text"
            android:background="#00000000"
            android:layout_width="wrap_content"
            android:layout_height="match_parent" />
    </LinearLayout>

</LinearLayout>