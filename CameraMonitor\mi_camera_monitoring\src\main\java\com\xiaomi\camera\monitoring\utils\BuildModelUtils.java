package com.xiaomi.camera.monitoring.utils;

import android.os.Build;

public class BuildModelUtils {

    private static String buildModel = null;

    public static boolean isX10A() {
        checkBuildModel();
        return "X10A".equals(buildModel);
    }

    public static boolean isX6A() {
        checkBuildModel();
        return "X6A".equals(buildModel);
    }

    public static boolean isX8F() {
        checkBuildModel();
        return "X8F".equals(buildModel);
    }

    public static boolean isOH8() {
        checkBuildModel();
        return "OH8".equals(buildModel);
    }

    private static synchronized void checkBuildModel() {
        if (buildModel == null) {
            buildModel = Build.MODEL;
        }
    }
}
