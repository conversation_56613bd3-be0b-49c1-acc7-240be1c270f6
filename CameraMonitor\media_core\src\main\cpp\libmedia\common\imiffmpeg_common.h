#ifndef __IMIFFMPEG_COMMON_H__
#define __IMIFFMPEG_COMMON_H__

#include <stdio.h>
#include "imimedia_include.h"
#include "imimedia_common.h"

#if defined(WIN32) && !defined(__cplusplus)  
#define inline __inline  
#endif

#ifdef __cplusplus
extern "C"{
#endif
#include <libavutil/opt.h>
#include <libavutil/error.h>
#ifdef __cplusplus
}
#endif

#define ffmpeg_err2str(info, errnum) \
{ \
	char errbuf[AV_ERROR_MAX_STRING_SIZE] = {0}; \
	av_make_error_string(errbuf, sizeof(errbuf), errnum); \
	printf("%s error = %d reason = %s\n", info, errnum, errbuf); \
}

/* AVPixelFormat */
static enum AVPixelFormat pixel_format2ffmpeg(pixel_format_id id)
{
	switch (id)
	{
	case pixel_format_yv12:
		return AV_PIX_FMT_YUV420P;
	case pixel_format_nv12:
		return AV_PIX_FMT_NV12;
	case pixel_format_rgb24:
		return AV_PIX_FMT_RGB24;
	case pixel_format_bgr24:
		return AV_PIX_FMT_BGR24;
	case pixel_format_rgb32:
		return AV_PIX_FMT_RGB32;
	case pixel_format_bgr32:
		return AV_PIX_FMT_BGR32;
	default:
		break;
	}
	return AV_PIX_FMT_NONE;
}

/* AVSampleFormat */
static enum AVSampleFormat sample_format2ffmpeg(sample_format_id id)
{
	switch (id)
	{
	case sample_format_u8:
		return AV_SAMPLE_FMT_U8;
	case sample_format_s16:
		return AV_SAMPLE_FMT_S16;
	case sample_format_s32:
		return AV_SAMPLE_FMT_S32;
	case sample_format_float:
		return AV_SAMPLE_FMT_FLT;
	case sample_format_double:
		return AV_SAMPLE_FMT_DBL;
	case sample_format_u8_p:
		return AV_SAMPLE_FMT_U8P;
	case sample_format_s16_p:
		return AV_SAMPLE_FMT_S16P;
	case sample_format_s32_p:
		return AV_SAMPLE_FMT_S32P;
	case sample_format_float_p:
		return AV_SAMPLE_FMT_FLTP;
	case sample_format_double_p:
		return AV_SAMPLE_FMT_DBLP;
	default:
		break;
	}
	return AV_SAMPLE_FMT_NONE;
}

static pixel_format_id ffmpeg2pixel_format(enum AVPixelFormat id)
{
	switch (id)
	{
	case AV_PIX_FMT_YUVJ420P:
	case AV_PIX_FMT_YUV420P:
		return pixel_format_yv12;
	case AV_PIX_FMT_NV12:
		return pixel_format_nv12;
	case AV_PIX_FMT_RGB24:
		return pixel_format_rgb24;
	case AV_PIX_FMT_RGB32:
		return pixel_format_rgb32;
	default:
		break;
	}
	return pixel_format_unknown;
}

static sample_format_id ffmpeg2sample_format(enum AVSampleFormat id)
{
	switch (id)
	{
	case AV_SAMPLE_FMT_U8:
		return sample_format_u8;
	case AV_SAMPLE_FMT_S16:
		return sample_format_s16;
	case AV_SAMPLE_FMT_S32:
		return sample_format_s32;
	case AV_SAMPLE_FMT_FLT:
		return sample_format_float;
	case AV_SAMPLE_FMT_DBL:
		return sample_format_double;
	case AV_SAMPLE_FMT_U8P:
		return sample_format_u8_p;
	case AV_SAMPLE_FMT_S16P:
		return sample_format_s16_p;
	case AV_SAMPLE_FMT_S32P:
		return sample_format_s32_p;
	case AV_SAMPLE_FMT_FLTP:
		return sample_format_float_p;
	case AV_SAMPLE_FMT_DBLP:
		return sample_format_double_p;
	default:
		break;
	}
	return sample_format_unknown;
}

static int ffmpeg_get_extradata_h264(imi_nalu_array_t nalu_array, unsigned char* extradata, unsigned int* extradata_size, unsigned int* offset)
{
	unsigned int i = 0;
	for (i = 0; i < nalu_array->_nalu_num; i++)
	{
		unsigned char* data = nalu_array->_nalu_index[i] + nalu_array->_nalu_startcode[i];
		int nalu_type = IMI_H264_NALU_TYPE((*(unsigned char*)(data)));
		switch (nalu_type)
		{
		case 0x07://SPS
		case 0x08://PPS
			{
				memcpy(extradata+(*extradata_size), nalu_array->_nalu_index[i], nalu_array->_nalu_len[i]);
				*extradata_size += nalu_array->_nalu_len[i];
			}
			break;
		case 0x05://IDR
			{
				return 1;
			}
			break;
		case 0x01://P
			{
				return 0;
			}
			break;
		default:
			break;
		}
		*offset += nalu_array->_nalu_len[i];
	}
	return 0;
}

static int ffmpeg_get_extradata_h265(imi_nalu_array_t nalu_array, unsigned char* extradata, unsigned int* extradata_size, unsigned int* offset)
{
	unsigned int i = 0;
	for (i = 0; i < nalu_array->_nalu_num; i++)
	{
		unsigned char* data = nalu_array->_nalu_index[i] + nalu_array->_nalu_startcode[i];
		int nalu_type = IMI_H265_NALU_TYPE((*(unsigned char*)(data)));
		switch (nalu_type)
		{
		case 0x20://VPS
		case 0x21://SPS
		case 0x22://PPS
			{
				memcpy(extradata+(*extradata_size), nalu_array->_nalu_index[i], nalu_array->_nalu_len[i]);
				*extradata_size += nalu_array->_nalu_len[i];
			}
			break;
		case 0x13://IDR
			{
				return 1;
			}
			break;
		case 0x01://P
			{
				return 0;
			}
			break;
		default:
			break;
		}
		*offset += nalu_array->_nalu_len[i];
	}
	return 0;
}

#endif // __IMIFFMPEG_COMMON_H__
