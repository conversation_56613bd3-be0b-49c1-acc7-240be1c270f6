#pragma once

#include "imimedia_include.h"

typedef struct imimp4_ffmpeg_info_s *imimp4_ffmpeg_info_t;

void imi_mp4_init_ffmpeg();

/**
 *创建mp4文件
 *
 * @param path  文件路劲
 * @param container 文件格式
 * @param correct   时间戳校准 0:校准 1:不校准
 * @param video  视频编码方式：h264 h265
 * @param audio  音频编码方式：aac
 * @param vfps     每秒传输帧数
 * @param vwidth  视频宽
 * @param vheight  视频高
 * @param achannel  音频通道数
 * @param asamplerate  音频采样率
 * @param duration   视频时长 0:一直录制
 * @param handle
 * @return 0 成功
 */
int imi_mp4_create_file(const char *path,
                        container_format_id container,
                        timestamp_correct_id correct,
                        video_codec_id video,
                        audio_codec_id audio,
                        unsigned int vfps,
                        unsigned int vwidth,
                        unsigned int vheight,
                        unsigned int achannel,
                        unsigned int asamplerate,
                        unsigned int duration,
                        unsigned long cur_time,
                        /*out*/imimp4_ffmpeg_info_t *handle);

/**
 * *写入视频流数据
 *
 * @param handle 创建文件时imimp4_ffmpeg_info_t结构体
 * @param data 视频流数据 h264 h265
 * @param data_len 视频数据长度
 * @param vtimestamp  视频流时间戳
 * @param frametype
 * @return 0 成功
 */
int imi_mp4_write_video_frame(imimp4_ffmpeg_info_t handle,
                              unsigned char *data,
                              unsigned int data_len,
                              unsigned long long vtimestamp,
                              frame_type_id frametype,
                              unsigned long long timestamp_ms_utc);

/**
 *写入音频流数据
 *
 * @param handle  创建文件时imimp4_ffmpeg_info_t结构体
 * @param data   音频流数据 aac
 * @param data_len   音频数据长度
 * @param atimestamp  音频流时间戳
 * @return 0 成功
 */
int imi_mp4_write_audio_frame(imimp4_ffmpeg_info_t handle,
                              unsigned char *data,
                              unsigned int data_len,
                              unsigned long long atimestamp);

/**
 *关闭创建的mp4文件
 *
 * @param handle  创建文件时imimp4_ffmpeg_info_t结构体
 * @return 0 成功
 */
int imi_mp4_close_file_for_create(imimp4_ffmpeg_info_t handle);

/**
 *打开mp4文件
 *
 * @param path  文件路劲
 * @param container 文件格式
 * @param video  视频编码方式：h264 h265
 * @param audio  音频编码方式：aac
 * @param vfps     每秒传输帧数
 * @param vwidth  视频宽
 * @param vheight  视频高
 * @param achannel  音频通道数
 * @param abitrate  比特率
 * @param asamplerate  音频采样率
 * @param duration   视频时长 0:一直录制
 * @param handle
 * @return 0 成功
 */
int imi_mp4_open_file(const char *path,
                      container_format_id *container,
                      video_codec_id *video,
                      audio_codec_id *audio,
                      unsigned int *vfps,
                      unsigned int *vwidth,
                      unsigned int *vheight,
                      unsigned int *achannel,
                      unsigned int *asamplerate,
                      unsigned int *abitrate,
                      unsigned long long *duration,
        /*out*/imimp4_ffmpeg_info_t *handle);

/**
 *获取音视频数据
 *
 * @param handle  打开文件时imimp4_ffmpeg_info_t结构体
 * @param data  音视频数据
 * @param data_len  音视频数据长度
 * @param timestamp  音视频流时间戳
 * @param frametype   音频/视频
 * @return  0 成功
 */
int imi_mp4_get_frame(imimp4_ffmpeg_info_t handle,
                      unsigned char *data,
                      unsigned int *data_len,
                      unsigned long long *timestamp,
                      frame_type_id *frametype);

/**
 *跳转到指定时间
 *
 * @param handle  创建文件时imimp4_ffmpeg_info_t结构体
 * @param timestamp  时间戳
 *  @return  0 成功
 * */
int imi_mp4_seek_file(imimp4_ffmpeg_info_t handle,
                      unsigned long long timestamp);

/**
 *关闭打开的mp4文件
 *
 * @param handle  创建文件时imimp4_ffmpeg_info_t结构体
 * @return 0 成功
 */
int imi_mp4_close_file_for_open(imimp4_ffmpeg_info_t handle);