package com.xiaomi.camera.monitoring;

import android.hardware.ipcamera.V1_0.IUVCCameraController;
import android.hardware.ipcamera.V1_0.IUVCEventCallback;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.RemoteException;

import com.xiaomi.camera.monitoring.utils.L;

/**
 * 通过UVC获取看家事件
 */
public class UVCEventManager {

    private final String TAG = "UVCEventManager";

    private volatile static UVCEventManager mInstance;

    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private VideoEventCallback mEventCallback;

    private IUVCCameraController mUVCameraController;

    public static UVCEventManager getInstance() {
        if (mInstance == null) {
            synchronized (UVCEventManager.class) {
                if (mInstance == null) {
                    mInstance = new UVCEventManager();
                }
            }
        }
        return mInstance;
    }

    private UVCEventManager() {
    }

    public void init(VideoEventCallback eventCallback) {
//        L.monitor.d("%s =====init=====", TAG);
        mEventCallback = eventCallback;
        try {
            mUVCameraController =  IUVCCameraController.getService(Constants.IPCAMERA_SERVICE_NAME);
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    Runnable recordRunnable = () -> {
        if (mEventCallback == null || mUVCameraController == null) {
            L.monitor.e("%s Please init first!!!!!!", TAG);
            return;
        }
        createEvent();
    };

    public synchronized void startEvent() {
//        L.monitor.d("%s =====startEvent=====", TAG);
        mHandlerThread = new HandlerThread("EventBackground");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        mBackgroundHandler.post(recordRunnable);
        try {
            if (mUVCameraController != null) {
                mUVCameraController.startEventCApplication();
            } else {
                L.monitor.e("%s mUVCameraController is NULL！！！！！！", TAG);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    public synchronized void stopEvent() {
        L.monitor.d("%s =====stopEvent=====", TAG);
        try {
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
            if (mUVCameraController != null) {
                mUVCameraController.stopEventApplication();
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mHandlerThread = null;
            mBackgroundHandler = null;
            mUVCameraController = null;
        }
    }

    private void createEvent() {
//        L.monitor.d("%s =====createEvent=====", TAG);
        try {
            mUVCameraController.setEventCallBack(new IUVCEventCallback.Stub() {
                @Override
                public void onEventCallback(byte event) {
                    mBackgroundHandler.post(() -> {
                        if (mEventCallback != null) {
                            mEventCallback.onEvent(event);
                        }
                    });
                }
            });
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }
}
