package com.xiaomi.mico.persistent.entry;

public class CloudImgEntry {
    private byte[] imageName;
    private String imageSign;

    public CloudImgEntry(byte[] imageName, String imageSign) {
        this.imageName = imageName;
        this.imageSign = imageSign;
    }

    public byte[] getImageName() {
        return imageName;
    }

    public void setImageName(byte[] imageName) {
        this.imageName = imageName;
    }

    public String getImageSign() {
        return imageSign;
    }

    public void setImageSign(String imageSign) {
        this.imageSign = imageSign;
    }
}
