#include <stdlib.h>
#include <string.h>

#include "imiffmpeg_common.h"
#include "imiconvert_ffmpeg.h"

#if defined(WIN32) && !defined(__cplusplus)
#define inline __inline
#endif

#ifdef __cplusplus
extern "C"{
#endif
#include <libavcodec/avcodec.h>
#include <libavutil/opt.h>
#include <libswscale/swscale.h>
#include <libswresample/swresample.h>
#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "swscale.lib")
#pragma comment(lib, "swresample.lib")
#endif

int imiconvert_video(unsigned int in_width,
	unsigned int in_height,
	pixel_format_id src_pix,
	unsigned char *src_data,
	unsigned int out_width,
	unsigned int out_height,
	pixel_format_id dst_pix,
	/*out*/unsigned char *dst_data)
{
	AVPicture src_picture = { 0 };
	AVPicture dst_picture = { 0 };
	enum AVPixelFormat src_pix_fmt, dst_pix_fmt;
	struct SwsContext *sws_ctx = NULL;
	src_pix_fmt = pixel_format2ffmpeg(src_pix);
	dst_pix_fmt = pixel_format2ffmpeg(dst_pix);
	avpicture_fill(&src_picture, src_data, src_pix_fmt, in_width, in_height);
	avpicture_fill(&dst_picture, dst_data, dst_pix_fmt, out_width, out_height);
	sws_ctx = sws_getContext(
		in_width, in_height, src_pix_fmt,
		out_width, out_height, dst_pix_fmt,
		SWS_BILINEAR, NULL, NULL, NULL);
	if (sws_ctx == NULL) {
		printf("imiconvert_video sws_getContext error\n");
		return IMIMEDIA_PARAMS_ERROR;
	}
    sws_scale(sws_ctx, src_picture.data, src_picture.linesize, 0, in_height, dst_picture.data, dst_picture.linesize);
    sws_freeContext(sws_ctx);
	return IMIMEDIA_OK;
}

int imiconvert_audio(unsigned int channels,
	unsigned int sample_rate,
	unsigned int nb_samples,
	sample_format_id src_smp,
	unsigned char *src_data,
	sample_format_id dst_smp,
	/*out*/unsigned char *dst_data,
	/*out*/unsigned int *dst_len)
{
	int ret = IMIMEDIA_OK;
	int templen = 0, len = 0;
	SwrContext* swrcontext = NULL;
	enum AVSampleFormat src_pix_fmt, dst_pix_fmt;
	src_pix_fmt = sample_format2ffmpeg(src_smp);
	dst_pix_fmt = sample_format2ffmpeg(dst_smp);
	swrcontext = swr_alloc_set_opts(swrcontext,
		av_get_default_channel_layout(channels), dst_pix_fmt, sample_rate,
		av_get_default_channel_layout(channels), src_pix_fmt, sample_rate,
		0, NULL);
	if (swrcontext == NULL) {
		printf("imiconvert_audio swr_alloc_set_opts error\n");
		return IMIMEDIA_PARAMS_ERROR;
	}
	ret = swr_init(swrcontext);
	if (ret < 0) {
		swr_free(&swrcontext);
		printf("imiconvert_audio swr_init error\n");
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	templen = av_samples_get_buffer_size(NULL, channels, nb_samples, dst_pix_fmt, 1);
	len = swr_convert(swrcontext, (uint8_t**)&dst_data, templen, (const uint8_t**)&src_data, nb_samples);
	swr_free(&swrcontext);
	if (len <= 0){
		ffmpeg_err2str("imiconvert_audio swr_convert audio", len);
		return IMIMEDIA_FORMAT_ERROR;
	}
	*dst_len = templen;
	return IMIMEDIA_OK;
}