package com.xiaomi.mico.persistent.monitor;

import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.provider.Settings;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.xiaomi.camera.monitoring.UVCCameraController;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.camera.monitoring.utils.LogSetup;
import com.xiaomi.mico.persistent.cloud.MotionDetection;
import com.xiaomi.mico.persistent.func.CameraRotateManager;
import com.xiaomi.mico.persistent.func.CameraScenesManager;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.UVCCamera0VideoManager;
import com.xiaomi.camera.monitoring.UVCCamera1VideoManager;
import com.xiaomi.mico.persistent.func.CameraUvcVersion;
import com.xiaomi.mico.persistent.func.FamilyCareManager;
import com.xiaomi.mico.persistent.spec.SpecPropertyManager;
import com.xiaomi.mico.persistent.utils.MiotManager;
import com.xiaomi.mico.persistent.voip.MiVoipUtil;

public class CameraMonitorApplication extends Application {
    private final int CAMERA_GET_OT_TOKEN_DELAY = 10 * 1000;
    private final int CAMERA_GET_OT_TOKEN = 0x01;
    private Handler mHandler = new Handler(new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            int what = msg.what;
            switch (what) {
                case CAMERA_GET_OT_TOKEN:
                    String token = MiotManager.getInstance().getMiotToken();
                    L.monitor.d("Get miot token: %s", token);
                    if (TextUtils.isEmpty(token)) {
                        mHandler.sendEmptyMessageDelayed(CAMERA_GET_OT_TOKEN, CAMERA_GET_OT_TOKEN_DELAY);
                    } else {
                        // 更新时间水印时间
                        UVCCameraController.getInstance().updateWaterMarkTS();
                        // 初始化移动侦测
                        Constants.OT_TOKEN = token;
                        Constants.BOOT_TIME_GAP = System.currentTimeMillis() - SystemClock.elapsedRealtime();
                        MotionDetection.getInstance().initStatus();
                    }
                    break;
            }
            return false;
        }
    });

    @Override
    public void onCreate() {
        super.onCreate();
        setupLog();
        L.monitor.d("camera monitor application create.");
        CommonApiUtils.setMissServerStopped(getApplicationContext());
        // MiVoipActivity遇到问题，调用VOIP_STOP通知Launcher重置状态
        String pkgName = CommonApiUtils.getVoipRunning(this);
        if (!TextUtils.isEmpty(pkgName) && pkgName.equals(getPackageName())) {
            L.monitor.i("MiVoipActivity encountered some exceptions.");
            MiVoipUtil.sendVoipBroadcast(this, MiVoipUtil.MSG_VOIP_CLIENT_VOIP_STOP, null);
        }
        CommonApiUtils.setVoipRunning(getApplicationContext(), "");
        // 恢复预存录制状态
        CommonApiUtils.setCloudRunning(getApplicationContext(), false);

        // 如果小爱唤醒是关闭状态，启动算法
        int status = CommonApiUtils.getSoundBoxStatus(getApplicationContext());
        if (status == Constants.SOUND_BOX_ALGO_CLOSE) {
            CommonApiUtils.setSoundBoxStatus(getApplicationContext(), Constants.SOUND_BOX_ALGO_OPEN);
        }

        MonitorNotificationManager.getInstance().cancelMonitorNotification(getApplicationContext());
        Settings.Global.putInt(getContentResolver(), BaseMissPorting.CAMERA_MONITOR_RUNNING, 0);
        getContentResolver().registerContentObserver(Settings.Global.getUriFor(BaseMissPorting.CAMERA_MONITOR_RUNNING),
                false,
                new ContentObserver(new Handler(Looper.getMainLooper())) {
                    @Override
                    public void onChange(boolean selfChange) {
                        super.onChange(selfChange);
                        // 如果拔电灭屏状态，停用miss服务
                        if (!CommonApiUtils.isMonitorRunning(getApplicationContext())) {
                            if (!CommonApiUtils.isBatteryCharging(getApplicationContext())
                                    && !CommonApiUtils.isScreenOn(getApplicationContext())) {
                                CameraMonitorManager.getInstance().finishMissServer();
                            }
                        }
                    }
                });


        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_SCREEN_OFF);
        registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                String action = intent.getAction();
                // 如果拔电灭屏状态，停用miss服务
                if (Intent.ACTION_SCREEN_OFF.equals(action)) {
                    if (!CommonApiUtils.isBatteryCharging(context)
                            && !CommonApiUtils.isMonitorRunning(context)) {
                        CameraMonitorManager.getInstance().finishMissServer();
                    }
                }
            }
        }, intentFilter);
        initCamera();
    }

    private void initCamera() {
        //初始化相机回调
        UVCCameraController.getInstance().init();
        UVCCamera0VideoManager.getInstance().init();
        UVCCamera1VideoManager.getInstance().init();
        //初始化MiotManager
        MiotManager.getInstance().initMiotManager(getApplicationContext());
        //初始化移动侦测
        MotionDetection.getInstance().initMotionDetection(getApplicationContext());
        //电机校准
        CameraRotateManager.getInstance().initRotate(getApplicationContext());
        //夜视初始化
        CameraScenesManager.getInstance().init();
        //家人守护状态初始化
        FamilyCareManager.getInstance().initFamilyCare(getApplicationContext());
        //Spec状态监听初始化
        SpecPropertyManager.getInstance().initProperty(getApplicationContext());
        //初始化获取OT token，根据绑定状态确定是否要获取token
        int connectStatus = CommonApiUtils.getMiotConnected(getApplicationContext());
        L.monitor.i("initCamera connectStatus: %d", connectStatus);
        if (connectStatus == 1) {
            mHandler.sendEmptyMessage(CAMERA_GET_OT_TOKEN);
        } else {
            getContentResolver().registerContentObserver(Settings.Global.getUriFor(CommonApiUtils.MICO_MIOT_CONNECTED),
                    false,
                    new ContentObserver(new Handler(Looper.getMainLooper())) {
                        @Override
                        public void onChange(boolean selfChange) {
                            if (CommonApiUtils.getMiotConnected(getApplicationContext()) == 1) {
                                mHandler.sendEmptyMessage(CAMERA_GET_OT_TOKEN);
                            }
                        }
                    });
        }
        // 设置377软件版本号
        CameraUvcVersion.getInstance().init(getApplicationContext());
        // 初始化音箱相关控制
//        SoundBoxManager.getInstance().init(getApplicationContext());
    }

    private void setupLog() {
        new LogSetup().setup(this);
    }
}
