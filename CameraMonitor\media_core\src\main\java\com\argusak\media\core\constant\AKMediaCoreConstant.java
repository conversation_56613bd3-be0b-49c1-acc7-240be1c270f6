package com.argusak.media.core.constant;

/**
 * @Author: zhy
 * @Date: 2024/5/29
 * @Desc: AKMediaCoreConstant
 */
public final class AKMediaCoreConstant {

    // 音频视频格式
    public static final class VideoCodecId {
        public static final int VIDEO_CODEC_H264 = 0x01;
        public static final int VIDEO_CODEC_H265 = 0x02;
        public static final int VIDEO_CODEC_MJPEG = 0x03;
        public static final int VIDEO_CODEC_MPEG4 = 0x04;
    }

    public static final class AudioCodecId {
        public static final int AUDIO_CODEC_AAC = 0xA;
        public static final int AUDIO_CODEC_G711A = 0xB;
        public static final int AUDIO_CODEC_G711U = 0xC;
        public static final int AUDIO_CODEC_OPUS = 0xD;
    }

    public static final class ContainerFormatId {
        public static final int CONTAINER_FORMAT_MP4 = 0;
        public static final int CONTAINER_FORMAT_FRAGMENTED_MP4 = 1;
        public static final int CONTAINER_FORMAT_TS = 2;
        public static final int CONTAINER_FORMAT_PS3 = 3;
        public static final int CONTAINER_FORMAT_IMI_CLOUD = 4;
    }

    public static final int V_FPS = 20;
    public static final int V_WIDTH = 2960;
    public static final int V_HEIGHT = 1666;
    public static final int A_CHANNEL = 1;  // 1：单声道 2: 立体声

    // 音频采样率
    public static final int A_SAMPLE_RATE = 16 * 1000;

    // 比特率（音频码率）
    public static final int A_BITRATE = 20 * 10000;

    private AKMediaCoreConstant() {
        // 防止实例化
    }
}
