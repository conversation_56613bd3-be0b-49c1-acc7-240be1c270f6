#pragma once

#include "imimedia_include.h"

typedef struct imicontainer_info_s *imicontainer_info_t;

int imicontainer_create_file(const char* path,
	container_format_id container,
	/*out*/imicontainer_info_t* handle);

int imicontainer_write_frame(imicontainer_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	video_codec_id video,
	audio_codec_id audio,
	frame_type_id frametype,
	unsigned int vwidth_or_achannel,
	unsigned int vheight_or_asamplerate,
	unsigned int vfps_or_abit,
	unsigned int timestamp_ms,
	unsigned int timestamp_s,
	unsigned int index);

int imicontainer_open_file(const char* path,
	container_format_id* container,
	/*out*/imicontainer_info_t* handle);

int imicontainer_get_frame(imicontainer_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	video_codec_id* video,
	audio_codec_id* audio,
	frame_type_id* frametype,
	unsigned int* vwidth_or_achannel,
	unsigned int* vheight_or_asamplerate,
	unsigned int* vfps_or_abit,
	unsigned int* timestamp_ms,
	unsigned int* timestamp_s,
	unsigned int* index);

int imicontainer_close_file(imicontainer_info_t handle);