package com.xiaomi.mico.persistent.func;

import android.content.Context;
import android.content.Intent;
import android.database.ContentObserver;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.provider.Settings;
import android.text.TextUtils;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.cloud.MotionDetection;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;
import com.xiaomi.mico.persistent.monitor.R;
import com.xiaomi.mico.persistent.utils.VDecUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 实现方案文档
 * https://wayawbott0.f.mioffice.cn/docx/doxk4oR5Iy1OD91KurgimVZ0Zkf
 */

public class SoundBoxManager {
    private static final String TAG = "SoundBoxManager";

    private static final long SOUND_BOX_TOAST_DELAY_MS =  30 * 60 * 1000;

    private Context mContext;
    private Intent mFloadIntent;
    private volatile static SoundBoxManager mInstance;
    private AudioManager mAudioManager;
    private Handler mBgCloudHandler;
    private HandlerThread mCloudThread;
    private boolean isInCalling = false;

    private List<String> mStopBoxAudioList = new ArrayList<String>() {{
        add(VDecUtils.PACKAGE_NAME_LAUNCHER_MUSIC); // Launcher音乐
        add(VDecUtils.PACKAGE_NAME_CLOUD_MUSIC); // 网易云
    }};

    private List<String> mVoipList = new ArrayList<String>() {{
        add(VDecUtils.PACKAGE_NAME_MICO_VOIP_CLASS1); // Voip通话
        add(VDecUtils.PACKAGE_NAME_MICO_VOIP_CLASS2); // 微信通话
        add(VDecUtils.PACKAGE_NAME_MONITOR_CLASS); // 插件通话
    }};

    private static final long TIME_CHECK_BOX_DELAY = 3000;
    private static final int MSG_CHECK_BOX_STATUS = 0x10001;

    private Handler.Callback mCloudCallback = new Handler.Callback() {
        @Override
        public boolean handleMessage(@NonNull Message msg) {
            String topActivity = VDecUtils.getTopActivity(mContext);
            L.monitor.d("%s CloudCallback topActivity: %s, msg.what: %d", TAG, topActivity, msg.what);
            switch (msg.what) {
                case MSG_CHECK_BOX_STATUS:
                    if (mStopBoxAudioList.contains(topActivity)) { // 如果包含在音频列表中
                        boolean isMusicPlay = mAudioManager.isMusicActive();
                        if (isMusicPlay) {
                            L.monitor.d("%s Package: %s is play", TAG, topActivity);
                            cloudStopTip();
                            stopSoundBox();
                        } else {
                            mBgCloudHandler.sendEmptyMessageDelayed(MSG_CHECK_BOX_STATUS, TIME_CHECK_BOX_DELAY);
                        }
                    } else if (mVoipList.contains(topActivity)) {
                        mBgCloudHandler.sendEmptyMessageDelayed(MSG_CHECK_BOX_STATUS, TIME_CHECK_BOX_DELAY);
                    } else { // 检查是否有解码器正在解码
                        String vDecInfo = VDecUtils.executeShellCommand(VDecUtils.CAT_VIDEO_DEC_INFO);
                        // 获取当前正在解码的个数
                        int vDecNum = VDecUtils.checkVDECStatus(vDecInfo);
                        L.monitor.d("%s vDecNum: %d", TAG, vDecNum);
                        // 正在云存录像，会占用一个解码器，所以需要减1
                        if (vDecNum - 1 >= 1) {
                            L.monitor.d("%s Package: %s is decode, DecNum %s", TAG, topActivity, vDecNum);
                            cloudStopTip();
                            stopSoundBox();
                            mContext.startForegroundService(mFloadIntent);
                        } else {
                            if (CommonApiUtils.getSoundBoxStatus(mContext) == Constants.SOUND_BOX_ALGO_CLOSE) {
                                startSoundBox();
                            }
                            mBgCloudHandler.sendEmptyMessageDelayed(MSG_CHECK_BOX_STATUS, TIME_CHECK_BOX_DELAY);
                        }
                    }
                    break;
            }
            return false;
        }
    };

    private final ContentObserver resumeActivityObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            String topResumeActivity = CommonApiUtils.getTopResumeActivity(mContext);
            L.monitor.d("%s Resume Activity onChange: %s", TAG, topResumeActivity);
            if (TextUtils.isEmpty(topResumeActivity)) {
                return;
            }
            try {
//                String topPkg = topResumeActivity.split("/")[0];
                String topActivity = topResumeActivity.split("/")[1];
                if (isInCalling && !mVoipList.contains(topActivity)) { // 挂断电话
                    isInCalling = false;
                    if (MotionDetection.getInstance().isRecording()) {
                        // 挂断电话延迟判断，立即判断获取解码数量可能不对
                        startCloudControl(3000);
                    } else {
                        if (CommonApiUtils.getSoundBoxStatus(mContext) == Constants.SOUND_BOX_ALGO_CLOSE) {
                            startSoundBox();
                        }
                    }
                } else if (!isInCalling && mVoipList.contains(topActivity)) { // 开始通话
                    isInCalling = true;
                    Toast.makeText(mContext, R.string.voip_sound_box_close, Toast.LENGTH_LONG).show();
                    if (mBgCloudHandler.hasMessages(MSG_CHECK_BOX_STATUS)) {
                        mBgCloudHandler.removeMessages(MSG_CHECK_BOX_STATUS);
                    }
                    if (CommonApiUtils.getSoundBoxStatus(mContext) == Constants.SOUND_BOX_ALGO_CLOSE) {
                        mContext.stopService(mFloadIntent);
                    } else {
                        stopSoundBox();
                    }
                }
            } catch (Exception e) {
                L.monitor.e("%s resumeActivityObserver error: %s", TAG, e.getMessage());
            }
        }
    };

    public void init(Context context) {
        this.mContext = context;
        mFloadIntent = new Intent(mContext, FloatSoundBoxService.class);
        mAudioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        mCloudThread = new HandlerThread("SoundBoxCloudBg");
        mCloudThread.start();
        mBgCloudHandler = new Handler(mCloudThread.getLooper(), mCloudCallback);
        mBgCloudHandler.post(() -> {
            if (CommonApiUtils.getSoundBoxStatus(mContext) == Constants.SOUND_BOX_ALGO_CLOSE) {
                startSoundBox();
            }
        });
        // 监听当前是否在通话页面
        Uri resumeActivityUri = Settings.Global.getUriFor(Constants.GLOBAL_TOP_RESUMED_ACTIVITY);
        context.getContentResolver().registerContentObserver(resumeActivityUri, false, resumeActivityObserver);
    }

    // 开始云存控制
    public void startCloudControl(long timeout) {
        // 如果当前在通话中不进行唤醒控制
        if (isInCalling) { // 通话中不进行唤醒恢复控制
            L.monitor.d("%s In calling... Do not start control.", TAG);
            return;
        }
        mBgCloudHandler.sendEmptyMessageDelayed(MSG_CHECK_BOX_STATUS, timeout);
    }

    // 结束云存控制
    public void stopCloudControl() {
        if (mBgCloudHandler.hasMessages(MSG_CHECK_BOX_STATUS)) {
            mBgCloudHandler.removeMessages(MSG_CHECK_BOX_STATUS);
        }
        mContext.stopService(mFloadIntent);
        // 如果当前在通话中不进行唤醒控制
        if (isInCalling) { // 通话中不进行唤醒恢复控制
            L.monitor.d("%s In calling... Do not stop control.", TAG);
            return;
        }
        if (CommonApiUtils.getSoundBoxStatus(mContext) == Constants.SOUND_BOX_ALGO_CLOSE) {
            startSoundBox();
        }
    }

    // 恢复小爱唤醒
    private void startSoundBox() {
        L.monitor.d("%s startSoundBox", TAG);
        CommonApiUtils.setSoundBoxStatus(mContext, Constants.SOUND_BOX_ALGO_OPEN);
    }

    // 停止小爱唤醒
    private void stopSoundBox() {
        L.monitor.d("%s stopSoundBox", TAG);
        CommonApiUtils.setSoundBoxStatus(mContext, Constants.SOUND_BOX_ALGO_CLOSE);
    }

    /*
    高负载场景小爱唤醒策略: 考虑非VIP的云存录制两次之间最短间隔只有3分钟，可能存在反复给用户关闭和打开唤醒的情况，
    频繁提示非常打扰用户，所以toast提示逻辑设计为：一次toast提醒后，后续30分钟内不再toast
     */
    private void cloudStopTip() {
        if (MotionDetection.getInstance().isVip()) {
            Toast.makeText(mContext, R.string.ai_sound_box_close, Toast.LENGTH_LONG).show();
        } else {
            long currentTime = System.currentTimeMillis();
            String preTime = CommonApiUtils.getSpfConfig(mContext, Constants.KEY_SOUND_BOX_STOP_TIME);
            if (TextUtils.isEmpty(preTime)) {
                Toast.makeText(mContext, R.string.ai_sound_box_close, Toast.LENGTH_LONG).show();
                CommonApiUtils.setSpfConfig(mContext, Constants.KEY_SOUND_BOX_STOP_TIME, String.valueOf(currentTime));
            } else if(currentTime - Long.parseLong(preTime) > SOUND_BOX_TOAST_DELAY_MS) {
                Toast.makeText(mContext, R.string.ai_sound_box_close, Toast.LENGTH_LONG).show();
                CommonApiUtils.setSpfConfig(mContext, Constants.KEY_SOUND_BOX_STOP_TIME, String.valueOf(currentTime));
            }
        }
    }

    public static SoundBoxManager getInstance() {
        if (mInstance == null) {
            synchronized (SoundBoxManager.class) {
                if (mInstance == null) {
                    mInstance = new SoundBoxManager();
                }
            }
        }
        return mInstance;
    }
}
