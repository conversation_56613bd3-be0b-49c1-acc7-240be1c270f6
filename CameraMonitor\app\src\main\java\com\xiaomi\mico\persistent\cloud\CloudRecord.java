package com.xiaomi.mico.persistent.cloud;

import android.media.MediaFormat;
import android.os.Process;
import android.text.TextUtils;

import com.argusak.media.core.AKMp4Muxer;
import com.argusak.media.core.constant.AKMediaCoreConstant;
import com.imilab.yuv.MediaYUV;
import com.xiaomi.camera.monitoring.CloudYUVData;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.DecodeCallback;
import com.xiaomi.camera.monitoring.DecodeOneFrame;
import com.xiaomi.camera.monitoring.entity.AudioFrameData;
import com.xiaomi.camera.monitoring.entity.VideoFrameData;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.CloudUploadEntry;
import com.xiaomi.mico.persistent.spec.SpecConstants;
import com.xiaomi.mico.persistent.utils.FileUtils;
import com.xiaomi.mico.persistent.utils.MDUtils;

import java.io.File;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

public class CloudRecord {

    private static final String TAG = "CloudRecord";

    private Thread workThreadVideo; //生产线程
    private boolean isRecording; //是否正在录制

    //视频当前1s 20帧数据，缓存180为9s数据
    private static final int MAX_CACHE_VIDEO_SIZE = 180;//最大生产存储缓存
    private final LinkedBlockingQueue<VideoFrameData> dataQueueVideo = new LinkedBlockingQueue<>(MAX_CACHE_VIDEO_SIZE); //存放生产出的数据
    //音频当前1s 25帧数据，缓存225为9s数据
    private static final int ONE_MIN_AUDIO_SIZE = 75;
    private static final int MAX_CACHE_AUDIO_SIZE = 225;//最大生产存储缓存
    private final LinkedBlockingQueue<AudioFrameData> dataQueueAudio = new LinkedBlockingQueue<>(MAX_CACHE_AUDIO_SIZE); //存放生产出的数据

    private String mRecordPath = "";
    private String mImageName = "";
    private String mVideoName = "";
    private StringBuffer mEventType = new StringBuffer();
    //Vip用户，检测到事件后，延长录制30秒视频，上传到云端，如无事件继续触发停止上传
    //非Vip用户，检测到事件后，录制9秒视频，上传到云端，录像间隔受看家助手间隔控制
    private final int RECORD_OFFSET = 80;
    private final int MAX_END_INDEX = (9 * 1000 + RECORD_OFFSET) * 1000; // 9080000
    private final int MAX_END_INDEX_VIP = (30 * 1000 + RECORD_OFFSET) * 1000; //30080000
    // Vip用户最多录制30分钟,然后进行下个fieldId存储
    private final int MAX_RECORD_VIP = (30 * 60 * 1000 + RECORD_OFFSET) * 1000; //1800080000
    private final int IFRAME_MIN_INTERVAL = 1000_000; // 连续两个I帧最小时间间隔
    private int mLeftTime = MAX_END_INDEX;
    private boolean misVip = false;
    private int mOffset = 0;
    private volatile long mFrameTimestamp = 0;
    private volatile long mIFrameTimestamp = 0;
    private long mFrameSeq = 0;
    private long mRecordTotalTime = 0;
    private int mAudioWriteCount = 0;

    protected ExecutorService mExecutorService = Executors.newFixedThreadPool(5);
    private CloudUploadEntry mCloudUploadEntry;
    private boolean mStartWriteVideo = false;
    private final LinkedBlockingQueue<CloudUploadEntry> mUploadVideoFiles = new LinkedBlockingQueue<>(MAX_CACHE_VIDEO_SIZE); // 待上传的视频列表
    private final ConcurrentLinkedQueue<String> mUploadImgFiles = new ConcurrentLinkedQueue<>(); // 待上传的图片列表
    private DecodeOneFrame mDecodeOneFrame;
    private boolean isInSaveImage = false;

    public LinkedBlockingQueue<CloudUploadEntry> getUploadVideoFiles() {
        return mUploadVideoFiles;
    }

    public ConcurrentLinkedQueue<String> getUploadImgFiles() {
        return mUploadImgFiles;
    }

    public void setEventType(String eventType) {
        String mCurType = mEventType.toString();
        if (TextUtils.isEmpty(mCurType)) {
            mEventType.append(eventType);
        } else {
            if (!mCurType.contains(eventType)) {
                mEventType.append(":");
                mEventType.append(eventType);
            }
        }
//        L.monitor.i("%s setEventType: %s", TAG, mEventType.toString());
        if (misVip) {
            mLeftTime = MAX_END_INDEX_VIP;
        } else {
            // 非VIP用户，最多录制9秒
            if (!isRecording) {
                mLeftTime = MAX_END_INDEX;
            }
        }
    }

    public void setIsVip(boolean isVip) {
        this.misVip = isVip;
    }

    public void startRecord(long startTime) {
        L.monitor.d("%s startRecord isRecording: %s, startTime: %d", TAG, isRecording, startTime);
        if (isRecording) {
            return;
        }
        //创建存储当前云存视频的文件路径
        File file = new File(FileUtils.getCloudRecordPath() + File.separator + startTime);
        if (!file.exists()) {
            file.mkdirs();
        }

        //云存图片解码创建
        mDecodeOneFrame = new DecodeOneFrame(mDecodeCallback);
        mRecordPath = file.getPath();
        dataQueueVideo.clear();
        dataQueueAudio.clear();
        mUploadVideoFiles.clear();
        // 清空队列
        cleanUploadImage();
        mOffset = 0;
        isRecording = true;
        workThreadVideo = new Thread(mVideoRunnable);
        workThreadVideo.start();
    }

    private void cleanUploadImage() {
        while (!mUploadImgFiles.isEmpty()) {
            mUploadImgFiles.poll();
        }
    }

    public void stopRecord(long endTime) {
        L.monitor.d("%s stopRecord isRecording: %s, endTime: %d", TAG, isRecording, endTime);
        isRecording = false;
        mUploadVideoFiles.clear();
        cleanUploadImage();
//        CloudYUVData.getInstance().stopPreview();
        if (workThreadVideo != null) {
            try {
                workThreadVideo.join(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        mDecodeOneFrame.release();
        mDecodeOneFrame = null;
        mEventType.setLength(0);
        mOffset = 0;
        mStartWriteVideo = false;
        mFrameTimestamp = 0;
        mIFrameTimestamp = 0;
        mFrameSeq = 0;
        mRecordTotalTime = 0;
        if (Constants.DELETE_CLOUD_FILE) {
            FileUtils.deleteDir(mRecordPath);
        }
    }

    public void offerMainVideoData(VideoFrameData data) {
        if (!isRecording) {
            return;
        }
        boolean success = dataQueueVideo.offer(data);
        if (!success) {
            L.monitor.e("%s offerMainVideoData failed, size: %d", TAG, dataQueueVideo.size());
        }
    }

    public void offerSubVideoData(VideoFrameData data) {
        if (mDecodeOneFrame != null && isRecording) {
            mDecodeOneFrame.decodeOneFrame(data.dataBytes);
        }
    }

    private DecodeCallback mDecodeCallback = data -> {
        if (!TextUtils.isEmpty(mImageName) && !isInSaveImage) {
            isInSaveImage = true;
            mExecutorService.execute(() -> saveImage(mImageName, data));
        }
    };

    public void offerAudioData(AudioFrameData audioFrameData) {
        if (mStartWriteVideo && isRecording) {
            boolean success = dataQueueAudio.offer(audioFrameData);
            if (!success) {
                L.monitor.e("%s offerAudioData failed, size: %d", TAG, dataQueueAudio.size());
            }
        }
    }

    private void saveAudioData(long curVideoTime) {
        while (!dataQueueAudio.isEmpty()) {
            AudioFrameData audioFrameData = dataQueueAudio.peek();
            if (audioFrameData != null) {
                if (audioFrameData.timeStamp <= curVideoTime) {
                    dataQueueAudio.poll();
                    if (mAudioWriteCount <= ONE_MIN_AUDIO_SIZE) {
//                        L.monitor.e("%s saveAudioData seq: %d, timeStamp: %d, size: %d", TAG, audioFrameData.index, audioFrameData.timeStamp, dataQueueAudio.size());
                        AKMp4Muxer.writeAudioFrameFileJava(audioFrameData.dataBytes, audioFrameData.timeStamp);
                    }
                    mAudioWriteCount++;
                } else {
                    break;
                }
            } else {
                break;
            }
        }
    }

    private Runnable mVideoRunnable = new Runnable() {
        @Override
        public void run() {
            android.os.Process.setThreadPriority(Process.THREAD_PRIORITY_VIDEO);
            while (isRecording) {
                if (dataQueueVideo.isEmpty()) {
                    try {
                        Thread.sleep(20);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    continue;
                }
                try {
                    VideoFrameData videoFrameData = dataQueueVideo.take();
//                    Log.i(TAG, "isIFrame:" + videoFrameData.isIFrame);
                    // 过滤掉非I帧数据
                    if (!mStartWriteVideo && !videoFrameData.isIFrame) {
                        continue;
                    }
                    // UVC当前配置20fps 60gop
                    if (videoFrameData.isIFrame) {
                        // 如果连续两个I帧的最小时间间隔小于1秒钟,继续录制,不生成视频
                        boolean shouldSaveVideo = true;
                        if (mIFrameTimestamp != 0) {
                            long iFrameInterval = videoFrameData.timeStamp - mIFrameTimestamp;
                            if (iFrameInterval < IFRAME_MIN_INTERVAL && mLeftTime > IFRAME_MIN_INTERVAL) {
                                L.monitor.e("%s iFrameInterval less than IFRAME_MIN_INTERVAL: %d", TAG, iFrameInterval);
                                shouldSaveVideo = false;
                            }
                        }
                        mIFrameTimestamp = videoFrameData.timeStamp;
                        if (shouldSaveVideo) {
                            // 连续两个I帧, 生成视频文件
                            if (mStartWriteVideo) {
                                L.monitor.d("%s Left_time: %d", TAG, mLeftTime);
                                mAudioWriteCount = 0;
                                AKMp4Muxer.closeFileJava();
                                mCloudUploadEntry.setOffset(mOffset); // 设置上传列表的下标0,1,2,3...
                                mCloudUploadEntry.setEventType(getCurEventType());
                                mEventType.setLength(0);
                                // Vip用户最多录制30分钟,然后进行下个fieldId存储
                                if (mRecordTotalTime >= MAX_RECORD_VIP) {
                                    L.monitor.d("%s Record time >= 30min", TAG);
                                    mCloudUploadEntry.setEnd(true);
                                    mCloudUploadEntry.setNextFieldId(true);
                                    mOffset = 0;
                                    mRecordTotalTime = 0;
                                } else {
                                    mOffset++;
                                }
                                mUploadVideoFiles.offer(mCloudUploadEntry);
                            }
                            // UVC当前配置20fps 60gop
                            long timeStamp = System.currentTimeMillis();
                            // 创建缩略图文件名称,在mDecodeCallback中保持
                            mImageName = mRecordPath + File.separator + timeStamp + Constants.JPEG_PREFIX;
                            CloudYUVData.getInstance().startCallback();
                            // 创建录制的视频文件
                            mVideoName = mRecordPath + File.separator + timeStamp + Constants.MP4_PREFIX;
                            boolean shouldReportMd = MDUtils.shouldReportMd(timeStamp);
                            mCloudUploadEntry = new CloudUploadEntry(mVideoName, !shouldReportMd);
                            AKMp4Muxer.createFileJava(mVideoName, getVideoCodecId(Constants.CAMERA_ENCODE_TYPE),
                                    (videoFrameData.timeStamp / 1000 + Constants.BOOT_TIME_GAP) / 1000);
                            mStartWriteVideo = true;
                        }
                    }
                    // 统计两帧之前的时间
                    if (mFrameTimestamp != 0) {
                        long recordTime = videoFrameData.timeStamp - mFrameTimestamp;
                        mLeftTime -= recordTime;
                        mRecordTotalTime += recordTime;
                        long recordSeq = videoFrameData.index - mFrameSeq;
                        if (recordSeq != 1) {
                            L.monitor.e("%s Frame lost %d, preIndex %d, curIndex %d", TAG, recordSeq, mFrameSeq, videoFrameData.index);
                        }
                    } else {
//                        L.monitor.d("%s writeVideoFrameFileJava: %d, seq: %d", TAG, videoFrameData.timeStamp / 1000 + Constants.BOOT_TIME_GAP, videoFrameData.index);
                    }
                    // 根据视频时间戳计算当前实际系统时间
                    long curRealTime = videoFrameData.timeStamp / 1000 + Constants.BOOT_TIME_GAP;
                    if (videoFrameData.isIFrame) {
                        L.monitor.d("%s IFrame: %d, seq: %d", TAG, curRealTime, videoFrameData.index);
                    }
                    AKMp4Muxer.writeVideoFrameFileJava(videoFrameData.dataBytes, videoFrameData.timeStamp * 1000,
                            videoFrameData.isIFrame ? curRealTime : 0);
                    // 根据视频时间戳,保存音频数据
                    saveAudioData(videoFrameData.timeStamp);
                    mFrameTimestamp = videoFrameData.timeStamp;
                    mFrameSeq = videoFrameData.index;

                    // 录制时间结束,停止录制
                    if (mLeftTime <= 0) {
                        L.monitor.d("%s Left_time: %d", TAG, mLeftTime);
                        isRecording = false;
                        mAudioWriteCount = 0;
                        AKMp4Muxer.closeFileJava();
                        mCloudUploadEntry.setEnd(true); // 设置是否最后一个上传
                        mCloudUploadEntry.setOffset(mOffset); // 设置上传列表的下标0,1,2,3...
                        mCloudUploadEntry.setEventType(getCurEventType());
                        mEventType.setLength(0);
                        mUploadVideoFiles.offer(mCloudUploadEntry);
                        mOffset = 0;
                        mStartWriteVideo = false;
                        break;
                    }
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    };

    private String getCurEventType() {
        String mCurEventType = mEventType.toString();
        if (mCurEventType.contains(SpecConstants.EVENT_TYPE_AI)) {
            return SpecConstants.EVENT_TYPE_AI;
        }
        return mCurEventType;
    }

    // 创建缩略图
    private void saveImage(String imageName, byte[] imageData) {
        byte[] imageNV21 = MediaYUV.getInstance().NV12ToNV21(imageData,
                Constants.CLOUD_IMAGE_WIDTH, Constants.CLOUD_IMAGE_HEIGHT);
        boolean mImageSaved = FileUtils.saveImage(imageNV21, imageName);
        if (mImageSaved) {
            mUploadImgFiles.offer(mImageName);
            mImageName = "";
        }
        isInSaveImage = false;
    }

    private int getVideoCodecId(String encodeType) {
        if (encodeType.equals(MediaFormat.MIMETYPE_VIDEO_HEVC)) {
            return AKMediaCoreConstant.VideoCodecId.VIDEO_CODEC_H265;
        } else {
            return AKMediaCoreConstant.VideoCodecId.VIDEO_CODEC_H264;
        }
    }
}
