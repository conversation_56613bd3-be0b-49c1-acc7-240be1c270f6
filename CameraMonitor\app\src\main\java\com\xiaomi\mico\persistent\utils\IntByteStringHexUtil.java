package com.xiaomi.mico.persistent.utils;

import java.util.Arrays;

public class IntByteStringHexUtil {

    /**
     * 将一个整形化为十六进制，并以字符串的形式返回
     */
    private final static String[] hexArray = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"};

    public static String intToHexStr(int n) {
        if (n < 0) {
            n = n + 256;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexArray[d1] + hexArray[d2];
    }

    //单个字符转成十六进制
    public static String oneStrToHexStr(String string) {
        if(string.equals("")){
            return "00";
        }

        int n = Integer.valueOf(string);

        if (n < 0) {
            n = n + 256;
        }
        int d1 = n / 16;
        int d2 = n % 16;
        return hexArray[d1] + hexArray[d2];
    }

    //将字符串中的每个字符转成十六进制
    public static String strToHexStr(String str){

        String hexStr = "";

        char[] chars = str.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            hexStr += oneStrToHexStr(String.valueOf(chars[i]));
        }

        return hexStr;
    }

    public static byte[] hexStrToByteArray(String str) {
        if (str == null) {
            return null;
        }
        if (str.length() == 0) {
            return new byte[0];
        }
        byte[] byteArray = new byte[str.length() / 2];
        for (int i = 0; i < byteArray.length; i++){
            String subStr = str.substring(2 * i, 2 * i + 2);
            byteArray[i] = ((byte)Integer.parseInt(subStr, 16));
        }
        return byteArray;
    }

    public static String byteArrayToHexStr(byte[] byteArray) {
        if (byteArray == null){
            return null;
        }
        char[] hexArray = "0123456789abcdef".toCharArray();
        char[] hexChars = new char[byteArray.length * 2];
        for (int j = 0; j < byteArray.length; j++) {
            int v = byteArray[j] & 0xFF;
            hexChars[j * 2] = hexArray[v >>> 4];
            hexChars[j * 2 + 1] = hexArray[v & 0x0F];
        }
        return new String(hexChars);
    }

    public static String StrToAddHexStr(String[] strings){
        long all=Long.parseLong("0",16);
        for (int i=0;i<strings.length;i++){
            long one=Long.parseLong(strings[i],16);
            all=all+one;
        }
        return Long.toHexString(256-(all%256));
    }

    public static void main(String[] args){
        System.out.println(Arrays.toString(hexStrToByteArray("55AA0100010001")));
    }

}