package com.xiaomi.mico.persistent.entry;

public class PreUploadEntry {
    private String url;
    private String token;
    private String securityKey;
    private String fileId;
    private String storageUrl;
    private String iv;

    public PreUploadEntry(String url, String token, String securityKey, String fileId, String storageUrl, String iv) {
        this.url = url;
        this.token = token;
        this.securityKey = securityKey;
        this.fileId = fileId;
        this.storageUrl = storageUrl;
        this.iv = iv;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSecurityKey() {
        return securityKey;
    }

    public void setSecurityKey(String securityKey) {
        this.securityKey = securityKey;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getStorageUrl() {
        return storageUrl;
    }

    public void setStorageUrl(String storageUrl) {
        this.storageUrl = storageUrl;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    @Override
    public String toString() {
        return "PreUploadEntry{" +
                "url='" + url + '\'' +
                ", token='" + token + '\'' +
                ", securityKey='" + securityKey + '\'' +
                ", fileId='" + fileId + '\'' +
                ", storageUrl='" + storageUrl + '\'' +
                ", iv='" + iv + '\'' +
                '}';
    }
}
