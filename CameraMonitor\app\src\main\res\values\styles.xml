<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>
    <style name="Theme.CameraMonitor" parent="AppTheme">
        <!-- 这里的trans自己写一个#00000000即可-->
        <item name="android:windowBackground">@color/colorTrans</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="MicoActivityTheme" parent="@style/MicoAppTheme">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <style name="MicoAppTheme" parent="@style/Theme.AppCompat.Light.NoActionBar">
        <item name="android:screenOrientation">landscape</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:editTextStyle">@style/AppEditText</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowExitTransition">@null</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="editTextStyle">@style/AppEditText</item>
    </style>

    <style name="Theme.Design.Light" parent="@style/Theme.AppCompat.Light" />

    <style name="AppEditText" parent="@style/Widget.AppCompat.EditText">
        <item name="android:imeOptions">flagNoExtractUi|flagNoFullscreen</item>
    </style>
</resources>