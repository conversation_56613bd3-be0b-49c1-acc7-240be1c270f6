#pragma once

#include "imimedia_include.h"

typedef struct imips_ffmpeg_info_s *imips_ffmpeg_info_t;

void imips_init_ffmpeg();

int imips_create_file(const char* path,
	container_format_id container,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	/*out*/imips_ffmpeg_info_t* handle);

int imips_write_video_frame(imips_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype);

int imips_write_audio_frame(imips_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp);

int imips_close_file_for_create(imips_ffmpeg_info_t handle);

int imips_open_file(const char* path,
	container_format_id* container,
	video_codec_id* video,
	audio_codec_id* audio,
	unsigned int* vfps,
	unsigned int* vwidth,
	unsigned int* vheight,
	unsigned int* achannel,
	unsigned int* asamplerate,
	unsigned int* abitrate,
	unsigned long long* duration,
	/*out*/imips_ffmpeg_info_t* handle);

int imips_get_frame(imips_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype);

int imips_seek_file(imips_ffmpeg_info_t handle,
	unsigned long long timestamp);

int imips_close_file_for_open(imips_ffmpeg_info_t handle);