<resources>
    <string name="app_name">MicoCameraMonitor</string>

    <string name="camera_monitor_toast_msg">正在远程看家</string>
    <string name="camera_monitor_toast_btn_exit">断开</string>
    <string name="camera_monitor_toast_clock_today_format">今天 H:m</string>
    <string name="camera_monitor_toast_clock_others_format">MM月dd号 H:m</string>
    <string name="voip_mute">静音</string>
    <string name="voip_unmute">取消静音</string>
    <string name="voip_hang_up">挂断</string>
    <string name="voip_accept">接听</string>
    <string name="voip_switch_to_voice">切到语音通话</string>
    <string name="voip_open_camera">打开摄像头</string>
    <string name="voip_close_camera">关闭摄像头</string>
    <string name="voip_calling">邀请你视频通话</string>
    <string name="video_call_local_camera_closed">摄像头已关闭</string>
    <string name="video_call_remote_camera_closed">对方摄像头已关闭</string>
    <string name="call_time">通话中&#160;%1$s</string>
    <string name="ims_dialog_open_mic_content">来电需要打开麦克风</string>
    <string name="ims_dialog_open_mic_title">打开麦克风</string>
    <string name="ims_dialog_enter">确认</string>
    <string name="ims_dialog_cancel">取消</string>
    <string name="hang_up_by_remote">对方已挂断</string>
    <string name="ai_sound_box_close">AI监控中，小爱语音唤醒会暂时关闭</string>
    <string name="voip_sound_box_close">通话占用中，小爱暂时无法响应，请挂断后再唤醒小爱</string>
</resources>
