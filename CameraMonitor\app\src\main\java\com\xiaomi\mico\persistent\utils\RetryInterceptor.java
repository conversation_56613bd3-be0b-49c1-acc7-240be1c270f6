package com.xiaomi.mico.persistent.utils;

import com.xiaomi.camera.monitoring.utils.L;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.internal.http2.ConnectionShutdownException;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import java.util.HashSet;
import java.util.Set;

public class RetryInterceptor implements Interceptor {
    private static final String TAG = "RetryInterceptor";
    private final int maxRetries; // 最大重试次数
    private final Set<Integer> retryStatusCodes; // 需要重试的 HTTP 状态码

    public RetryInterceptor(int maxRetries) {
        this.maxRetries = maxRetries;
        this.retryStatusCodes = new HashSet<>();
        // 默认重试的状态码：5xx 服务器错误和 429 Too Many Requests
        this.retryStatusCodes.add(429); // Too Many Requests
        this.retryStatusCodes.add(500); // Internal Server Error
        this.retryStatusCodes.add(502); // Bad Gateway
        this.retryStatusCodes.add(503); // Service Unavailable
        this.retryStatusCodes.add(504); // Gateway Timeout
    }

    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        Response response = null;
        int retryCount = 0; // 每次请求开始时重置 retryCount

        while (true) {
            try {
                response = chain.proceed(request);

                // 检查 HTTP 状态码是否需要重试
                if (retryStatusCodes.contains(response.code()) && retryCount < maxRetries) {
                    retryCount++;
                    L.monitor.e("%s Retrying due to status code: %d", TAG, response.code());
                    continue; // 继续重试
                }

                // 如果不需要重试，返回响应
                return response;

            } catch (IOException e) {
                // 捕获网络异常或超时异常
                if (shouldRetry(e) && retryCount < maxRetries) {
                    retryCount++;
                    L.monitor.e("%s Retrying due to exception: %s", TAG, e.getClass().getSimpleName());
                } else {
                    throw e; // 超过重试次数，抛出异常
                }
            }
        }
    }

    /**
     * 判断是否需要重试
     *
     * @param e 捕获的异常
     * @return 是否需要重试
     */
    private boolean shouldRetry(IOException e) {
        // 超时异常
        if (e instanceof SocketTimeoutException) {
            return true;
        }
        // 连接关闭异常
        if (e instanceof ConnectionShutdownException) {
            return true;
        }
        // 未知主机异常（DNS 解析失败）
        if (e instanceof UnknownHostException) {
            return true;
        }
        // 其他网络异常
        return e.getMessage() != null && e.getMessage().contains("Failed to connect");
    }
}
