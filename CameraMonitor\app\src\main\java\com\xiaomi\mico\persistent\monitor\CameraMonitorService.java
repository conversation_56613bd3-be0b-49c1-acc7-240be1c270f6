package com.xiaomi.mico.persistent.monitor;

import android.app.IntentService;
import android.app.Notification;
import android.content.Intent;
import android.text.TextUtils;

import com.xiaomi.camera.monitoring.utils.L;

public class CameraMonitorService extends IntentService {

    private static final String TAG = "CameraMonitorService";

    public static final String START_SERVICE = "com.xiaomi.mico.persistent.monitor.START_SERVICE";
    public static final String STOP_SERVICE = "com.xiaomi.mico.persistent.monitor.STOP_SERVICE";
    public static final String CLOSE_SERVICE = "com.xiaomi.mico.persistent.monitor.CLOSE_SERVICE";
    private final String KEY_ENFORCE_START_MISS_SERVER = "enforce_start_miss_server";

    public CameraMonitorService() {
        super(TAG);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        Notification notification = NotificationChannelUtils.createServiceNotification(this, "");
        startForeground(MonitorNotificationManager.FOREGROUND_NOTIFY_ID_INIT_MISS, notification);
    }

    @Override
    protected void onHandleIntent(Intent intent) {
        String action = intent.getAction();
        L.monitor.d("%s handle monitor service, action is %s", TAG, action);
        switch (action) {
            case START_SERVICE:
                boolean enforceStartMissServer = intent.getBooleanExtra(KEY_ENFORCE_START_MISS_SERVER, true);
                L.monitor.d("%s start monitor service, enforce is %s, status is %d",
                        TAG, enforceStartMissServer, CommonApiUtils.getMissServerStatus(getApplicationContext()));
                if (enforceStartMissServer) {
                    CameraMonitorManager.getInstance().initMiss(getApplicationContext());
                } else if (CommonApiUtils.isMissServerStopped(getApplicationContext())) {
                    CameraMonitorManager.getInstance().initMiss(getApplicationContext());
                }
                CheckMonitorRunningService.start(getApplicationContext());
                break;
            case STOP_SERVICE:
                // 如果当前CameraMonitor正在通话，不关闭Miss服务
                String pkgName = CommonApiUtils.getVoipRunning(this);
                if (!TextUtils.isEmpty(pkgName) && pkgName.equals(getPackageName())) {
                    L.monitor.i("%s CameraMonitor is calling, do not stop miss server", TAG);
                    break;
                }
                CameraMonitorManager.getInstance().disconnectMiss();
                break;
            case CLOSE_SERVICE:
                CameraMonitorManager.getInstance().disconnectMiss();
                CameraMonitorManager.getInstance().finishMissServer(true);
                break;
        }
    }
}
