package com.xiaomi.camera.monitoring.miss;

public class MissConstants {
    public static final int DEFAULT_SUCCESS_CODE = 0;

    public static final int MISS_CMD_AUTHENTICATE_REQ = 256;
    public static final int MISS_CMD_AUTHENTICATE_RESP = 257;
    public static final int MISS_CMD_VIDEO_START = 258; /**< C->S, video start */
    public static final int MISS_CMD_VIDEO_STOP = 259; /**< C->S, video stop */
    public static final int MISS_CMD_AUDIO_START = 260; /**< C->S, audio start */
    public static final int MISS_CMD_AUDIO_STOP = 261; /**< C->S, audio stop */
    public static final int MISS_CMD_SPEAKER_START_REQ = 262; /**< C->S, speaker start req */
    public static final int MISS_CMD_SPEAKER_START_RESP = 263; /**< C->S, speaker start resp */
    public static final int MISS_CMD_SPEAKER_STOP = 264; /**< C->S, speaker stop */
    public static final int MISS_CMD_STREAM_CTRL_REQ = 265; /**< C->S, video quality req */
    public static final int MISS_CMD_STREAM_CTRL_RESP = 266; /**< S->C, video quality response */
    public static final int MISS_CMD_GET_AUDIO_FORMAT_REQ = 267; /**< C->S, get audio format */
    public static final int MISS_CMD_GET_AUDIO_FORMAT_RESP = 268; /**< S->C, audio format response */
    public static final int MISS_CMD_PLAYBACK_REQ = 269; /**< C->S, playback request */
    public static final int MISS_CMD_PLAYBACK_RESP = 270; /**< S->C, playback response */
    public static final int MISS_CMD_PLAYBACK_SET_SPEED = 271; /**< C->S, playback speed */
    public static final int MISS_CMD_DEVINFO_REQ = 272; /**< C->S, device info request */
    public static final int MISS_CMD_DEVINFO_RESP = 273; /**< S->C, device info response */
    public static final int MISS_CMD_MOTOR_REQ = 274; /**< C->S, device motor control */
    public static final int MISS_CMD_MOTOR_RESP = 275; /**< S->C, device motor control response */
    public static final int MISS_CMD_NETWORK_STATUS = 276; /**< 0x114/276, S->C, device network status */
    public static final int MISS_CMD_SERVICE_ERROR_NOTIFY = 277; /**< 0x115/277, S->C, device session error notify */
    public static final int MISS_CMD_LOCK_REQ = 278; /**< 0x116/278, C->S, lock request */
    public static final int MISS_CMD_LOCK_RESP = 279; /**< 0x117/279, S->C, lock response */
    public static final int MISS_CMD_VIDEOCALL_START_REQ = 280; /**< 0x118/280, C->S, videocall start req */
    public static final int MISS_CMD_VIDEOCALL_START_RESP = 281; /**< 0x119/281, C->S, videocall start resp */
    public static final int MISS_CMD_VIDEOCALL_STOP = 282; /**< 0x120/282, C->S, videocall stop */

    public static final int MISS_CMD_CRUISE_STATE_REQ = 512; /**< 0x200/512, C->S, 查询巡航状态 */
    public static final int MISS_CMD_CRUISE_STATE_RESP = 513; /**< 0x201/513, S->C, 响应查询巡航状态 */
    public static final int MISS_CMD_CALL_STATUS_RESP = 769; /**< 0x301/769, S->C, 呼叫响应 */
    public static final int MISS_CMD_CLIENT_INFO = 284; /**< 0x11C/284, C->S, 客户的用户信息 */

    public static final int MISS_CMD_MAX = 4097;

    // 应用自己定义的CMD值，必须大于MISS_CMD_MAX
    public static final int MISS_CMD_CAMERA_BOX_QUIT = 5000;
    public static final int MISS_CMD_CAMERA_OPENED = 5001;
    public static final int MISS_CMD_CAMERA_CLOSED = 5002;
    public static final int MISS_CMD_CAMERA_COVERED = 5003;
    public static final int MISS_CMD_MICROPHONE_NORMAL = 5004;
    public static final int MISS_CMD_MICROPHONE_MUTE = 5005;
    public static final int MISS_CMD_VOIP_START = 5006; // 通话接通
    public static final int MISS_CMD_OPEN_CAMERA_REMOTE = 5007; // 通话过程中插件打开相机
    public static final int MISS_CMD_CLOSE_CAMERA_REMOTE = 5008; // 通话过程中插件关闭相机
    public static final int MISS_CMD_OPEN_CAMERA_LOCAL = 5009; // 通话过程中本地打开相机
    public static final int MISS_CMD_CLOSE_CAMERA_LOCAL = 5010; // 通话过程中本地关闭相机

    public static final String MISS_CMD_VIDEO_START_STR = "startLiveStream"; //开始推流
    public static final String MISS_CMD_VIDEO_STOP_STR = "stopLiveStream"; //结束推流
    public static final String MISS_CMD_AUDIO_START_STR = "startSpeakerClient"; //开始推流音频
    public static final String MISS_CMD_AUDIO_STOP_STR = "stopSpeakerClient"; //结束推流音频
    public static final String MISS_CMD_SPEAKER_START_REQ_STR = "startSpeaker"; //开始对讲
    public static final String MISS_CMD_SPEAKER_STOP_STR = "stopSpeaker"; //结束对讲
    public static final String MISS_CMD_SPEAKER_STOP_CALLBACK = "cb_miss_on_audio_data"; //对讲数据
    public static final String MISS_CMD_STREAM_CTRL_REQ_STR = "stream_quality_req"; //分辨率发生变化

}
