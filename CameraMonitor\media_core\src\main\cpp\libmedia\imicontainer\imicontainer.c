#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <assert.h>
#ifndef WIN32
#include <pthread.h>
#else
#include "imimedia_win2linux.h"
#endif

#include "imicontainer_include.h"
#include "imicontainer.h"

typedef struct imicontainer_info_s {
#ifndef WIN32
	pthread_mutex_t _lock_mutex;
#endif
	FILE* _file;
} imicontainer_info_s, *imicontainer_info_t;

int _create_container_inner(imicontainer_info_t handle,
	const char* path,
	container_format_id container)
{
	imicontainer_header_t header = NULL;
	header = imicontainer_header_make_in_params(container);
	if (header == NULL) {
		printf("imicontainer_create_file imicontainer_header_t make error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	handle->_file = fopen(path, "wb");
	if (handle->_file == NULL) {
		printf("imicontainer_create_file fopen error path = %s\n", path);
		goto error;
	}
	if (fwrite(header, sizeof(imicontainer_header_s), 1, handle->_file) != 1) {
		printf("imicontainer_create_file fwrite error\n");
		goto error;
	}
	printf("imicontainer_create_file success\n");
	free(header);
	return IMIMEDIA_OK;

error:
	if (handle->_file) {
		fclose(handle->_file);
		handle->_file = NULL;
	}
	if (header) {
		free(header);
		header = NULL;
	}
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imicontainer_create_file(const char* path,
	container_format_id container,
	/*out*/imicontainer_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imicontainer_info_t handle_impl = NULL;
	handle_impl = (imicontainer_info_t)malloc(sizeof(imicontainer_info_s));
	if (handle_impl == NULL) {
		printf("imicontainer_create_file malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imicontainer_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _create_container_inner(handle_impl, path, container);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imicontainer_create_file handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _write_imicontainer_frame_inner(imicontainer_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	video_codec_id video,
	audio_codec_id audio,
	frame_type_id frametype,
	unsigned int vwidth_or_achannel,
	unsigned int vheight_or_asamplerate,
	unsigned int vfps_or_abit,
	unsigned int timestamp_ms,
	unsigned int timestamp_s,
	unsigned int index)
{
	imicontainer_frame_t frame = NULL;
	frame = imicontainer_frame_make_in_params(data_len,
		video,
		audio,
		frametype,
		vwidth_or_achannel,
		vheight_or_asamplerate,
		vfps_or_abit,
		timestamp_ms,
		timestamp_s,
		index);
	if (frame == NULL) {
		printf("imicontainer_create_file imicontainer_frame_t make error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	assert(handle != NULL);
	if (fwrite(frame, sizeof(imicontainer_frame_s), 1, handle->_file) != 1) {
		printf("imicontainer_write_frame fwrite error\n");
		free(frame);
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	if (fwrite(data, data_len, 1, handle->_file) != 1) {
		printf("imicontainer_write_frame fwrite error\n");
		free(frame);
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	free(frame);
	return IMIMEDIA_OK;
}

int imicontainer_write_frame(imicontainer_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	video_codec_id video,
	audio_codec_id audio,
	frame_type_id frametype,
	unsigned int vwidth_or_achannel,
	unsigned int vheight_or_abit,
	unsigned int vfps_or_asamplerate,
	unsigned int timestamp_s,
	unsigned int timestamp_ms,
	unsigned int index)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _write_imicontainer_frame_inner(handle,
		data,
		data_len,
		video,
		audio,
		frametype,
		vwidth_or_achannel,
		vheight_or_abit,
		vfps_or_asamplerate,
		timestamp_s,
		timestamp_ms,
		index);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _open_container_inner(imicontainer_info_t handle,
	const char* path,
	container_format_id* container)
{
	unsigned char buffer[sizeof(imicontainer_header_s)];
	imicontainer_header_t header = NULL;
	assert(handle != NULL);
	handle->_file = fopen(path, "rb");
	if (handle->_file == NULL) {
		printf("imicontainer_open_file fopen error path = %s\n", path);
		goto error;
	}
	if (fread(buffer, sizeof(buffer), 1, handle->_file) != 1) {
		printf("imicontainer_open_file fread error\n");
		goto error;
	}
	header = imicontainer_header_make_in_buffer(buffer);
	if (header == NULL) {
		printf("imicontainer_open_file imicontainer_header_t make error\n");
		goto error;
	}
	if (header->header != IMICONTAINER_HEADER) {
		printf("imicontainer_open_file header error = %d\n", header->header);
		goto error;
	}
	printf("imicontainer_open_file success\n");
	free(header);
	return IMIMEDIA_OK;

error:
	if (handle->_file) {
		fclose(handle->_file);
		handle->_file = NULL;
	}
	if (header) {
		free(header);
		header = NULL;
	}
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imicontainer_open_file(const char* path,
	container_format_id* container,
	/*out*/imicontainer_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imicontainer_info_t handle_impl = NULL;
	handle_impl = (imicontainer_info_t)malloc(sizeof(imicontainer_info_s));
	if (handle_impl == NULL) {
		printf("imicontainer_open_file malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imicontainer_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _open_container_inner(handle_impl, path, container);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imicontainer_open_file handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _get_imicontainer_frame_inner(imicontainer_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	video_codec_id* video,
	audio_codec_id* audio,
	frame_type_id* frametype,
	unsigned int* vwidth_or_achannel,
	unsigned int* vheight_or_asamplerate,
	unsigned int* vfps_or_abit,
	unsigned int* timestamp_ms,
	unsigned int* timestamp_s,
	unsigned int* index)
{
	unsigned char buffer[sizeof(imicontainer_frame_s)];
	imicontainer_frame_t frame = NULL;
	if (fread(buffer, sizeof(buffer), 1, handle->_file) != 1) {
		free(frame);
		if (feof(handle->_file)) {
			printf("imicontainer_get_frame eof\n");
			return IMIMEDIA_EOF;
		} else {
			printf("imicontainer_get_frame fread error\n");
			return IMIMEDIA_CAPABILITY_ERROR;
		}
	}
	frame = imicontainer_frame_make_in_buffer(buffer);
	if (frame == NULL) {
		printf("imicontainer_get_frame imicontainer_frame_t make error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	assert(frame->header == IMICONTAINER_FRAME);
	switch (frame->type)
	{
	case frame_type_i:
	case frame_type_p:
	case frame_type_b:
	{
		*video = (video_codec_id)frame->codec;
	}
		break;
	case frame_type_audio:
	{
		*audio = (audio_codec_id)frame->codec;
	}
		break;
	default:
		break;
	}
	*frametype = (frame_type_id)frame->type;
	*vwidth_or_achannel = frame->vwidth_or_achannel;
	*vheight_or_asamplerate = frame->vheight_or_asample;
	*vfps_or_abit = frame->vfps_or_abit;
	*timestamp_ms = frame->timestamp_ms;
	*timestamp_s = frame->timestamp_s;
	*index = frame->index;
	*data_len = frame->framelen;
	if (fread(data, *data_len, 1, handle->_file) != 1) {
		printf("imicontainer_get_frame fread error\n");
		free(frame);
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	free(frame);
	return IMIMEDIA_OK;
}

int imicontainer_get_frame(imicontainer_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	video_codec_id* video,
	audio_codec_id* audio,
	frame_type_id* frametype,
	unsigned int* vwidth_or_achannel,
	unsigned int* vheight_or_abit,
	unsigned int* vfps_or_asamplerate,
	unsigned int* timestamp_ms,
	unsigned int* timestamp_s,
	unsigned int* index)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _get_imicontainer_frame_inner(handle,
		data,
		data_len,
		video,
		audio,
		frametype,
		vwidth_or_achannel,
		vheight_or_abit,
		vfps_or_asamplerate,
		timestamp_ms,
		timestamp_s,
		index);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _close_imicontainer_file_inner(imicontainer_info_t handle)
{
	if (handle->_file) {
		fflush(handle->_file);
		fclose(handle->_file);
		handle->_file = NULL;
	}
	printf("imicontainer_close_file success\n");
	return IMIMEDIA_OK;
}

int imicontainer_close_file(imicontainer_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	printf("imicontainer_close_file handle = %x\n", handle);
	ret = _close_imicontainer_file_inner(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	pthread_mutex_destroy(&handle->_lock_mutex);
	free(handle);
	return ret;
}