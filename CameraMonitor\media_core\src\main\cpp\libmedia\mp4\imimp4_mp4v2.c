#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <mp4v2/mp4v2.h>

#ifndef WIN32

#include <stdbool.h>
#include <pthread.h>
#include <arpa/inet.h>

#else
#define strcasecmp _stricmp
#include <WinSock2.h>
#include "imimedia_win2linux.h"
#endif

#include "imimp4_mp4v2.h"
#include "imimedia_common.h"
#include "imiparser_aac.h"

#define IMI_TRACK_IS_H264_TYPE(type) (!strcasecmp(type, "avc1"))
#define IMI_TRACK_IS_H265_TYPE(type) (!strcasecmp(type, "hvc1") || !strcasecmp(type, "hev1"))
#define IMI_TRACK_IS_AAC_TYPE(type) (!strcasecmp(type, "mp4a"))
#define IMI_TRACK_IS_G711A_TYPE(type) (!strcasecmp(type, "alaw"))

typedef struct imimp4v2_info_s {
#ifndef WIN32
    pthread_mutex_t _lock_mutex;
#endif
    MP4FileHandle mp4_handle;
    timestamp_correct_id correct;
    video_codec_id video_codec;
    audio_codec_id audio_codec;
    MP4TrackId video_track_id;
    unsigned int video_width;
    unsigned int video_height;
    unsigned int video_fps;
    unsigned long long video_lasttimestamp;
    unsigned long long video_total_timestamp;
    unsigned long long video_duration;
    unsigned int video_index;
    unsigned int video_index_total;
    bool video_eof;
    char *video_header_buff;
    unsigned int video_header_size;
    unsigned int video_scale;
    MP4TrackId audio_track_id;
    unsigned int audio_channel;
    unsigned int audio_samplerate;
    unsigned long long audio_lasttimestamp;
    unsigned long long audio_total_timestamp;
    unsigned long long audio_duration;
    unsigned int audio_index;
    unsigned int audio_index_total;
    bool audio_eof;
    unsigned int audio_scale;
} imimp4v2_info_s, *imimp4v2_info_t;

int _imimp4v2_create_file_inner(imimp4v2_info_t handle,
                                const char *path,
                                timestamp_correct_id correct,
                                video_codec_id video,
                                audio_codec_id audio,
                                unsigned int vfps,
                                unsigned int vwidth,
                                unsigned int vheight,
                                unsigned int achannel,
                                unsigned int asamplerate,
                                unsigned long long duration) {
    switch (video) {
        case video_codec_h264:
        case video_codec_h265:
            handle->video_codec = video;
            break;
        default:
            return IMIMEDIA_PARAMS_ERROR;
    }
    switch (audio) {
        case audio_codec_aac:
        case audio_codec_g711a:
            handle->audio_codec = audio;
            break;
        default:
            return IMIMEDIA_PARAMS_ERROR;
    }
    handle->mp4_handle = MP4Create(path, 0);
    if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
        remove(path);
        return IMIMEDIA_RESOURCE_ERROR;
    }
    handle->correct = correct;
    handle->video_track_id = MP4_INVALID_TRACK_ID;
    handle->video_width = vwidth;
    handle->video_height = vheight;
    handle->video_fps = vfps;
    handle->video_duration = (((double) duration / (double) 1000) * IMI_VIDEO_TIME_BASE);
    handle->video_scale = IMI_VIDEO_TIME_BASE;
    handle->audio_track_id = MP4_INVALID_TRACK_ID;
    handle->audio_channel = achannel;
    handle->audio_samplerate = asamplerate;
    handle->audio_duration = duration;
    MP4SetTimeScale(handle->mp4_handle, handle->video_scale);
    MP4SetAllCreateTime(2082844800 + time(NULL));
    return IMIMEDIA_OK;
}

int imimp4v2_create_file(const char *path,
                         timestamp_correct_id correct,
                         video_codec_id video,
                         audio_codec_id audio,
                         unsigned int vfps,
                         unsigned int vwidth,
                         unsigned int vheight,
                         unsigned int achannel,
                         unsigned int asamplerate,
                         unsigned long long duration,
        /*out*/imimp4v2_info_t *handle) {
    int ret = IMIMEDIA_OK;
    imimp4v2_info_t handle_impl = NULL;
    handle_impl = (imimp4v2_info_t) malloc(sizeof(imimp4v2_info_s));
    if (handle_impl == NULL) {
        printf("imimp4v2_create_file malloc error\n");
        return IMIMEDIA_RESOURCE_ERROR;
    }
    memset(handle_impl, 0, sizeof(imimp4v2_info_s));
    pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
    pthread_mutex_lock(&handle_impl->_lock_mutex);
    ret = _imimp4v2_create_file_inner(handle_impl, path, correct, video, audio, vfps, vwidth,
                                      vheight, achannel, asamplerate, duration);
    if (ret != IMIMEDIA_OK) {
        pthread_mutex_unlock(&handle_impl->_lock_mutex);
        pthread_mutex_destroy(&handle_impl->_lock_mutex);
        free(handle_impl);
        *handle = NULL;
        return ret;
    }
    *handle = handle_impl;
    printf("imimp4v2_create_file handle = %x\n", *handle);
    pthread_mutex_unlock(&handle_impl->_lock_mutex);
    return ret;
}

int _imimp4v2_get_video_duation(imimp4v2_info_t handle,
                                unsigned long long vtimestamp,
                                MP4Duration *duration) {
    if (vtimestamp == 0) {
        *duration = handle->video_scale / handle->video_fps;
    } else {
        if (handle->video_lasttimestamp == 0) {
            *duration = handle->video_scale / handle->video_fps;
        } else {
            unsigned long long time = IMI_PTS2TIME_SCALE(vtimestamp, handle->video_lasttimestamp,
                                                         handle->video_scale);
            if (time >= handle->video_scale && handle->correct == timestamp_correct_enable) {
                time = handle->video_scale / handle->video_fps;
            }
            *duration = time;
        }
        handle->video_lasttimestamp = vtimestamp;
    }
    if (handle->video_duration != 0 &&
        handle->video_total_timestamp + *duration >= handle->video_duration) {
        long long diff = handle->video_duration - handle->video_total_timestamp;
        if (diff > 0) {
            *duration = diff;
        } else {
            return IMIMEDIA_EOF;
        }
    }
    handle->video_total_timestamp += *duration;
    return IMIMEDIA_OK;
}

int _imimp4v2_write_video_sample(imimp4v2_info_t handle,
                                 unsigned char *nalu_data,
                                 unsigned int nalu_len,
                                 unsigned int nalu_startcode,
                                 unsigned long long timestamp,
                                 bool keyframe) {
    int ret = IMIMEDIA_OK;
    MP4Duration duration = MP4_INVALID_DURATION;
    char *frame_data = NULL;
    unsigned int frame_len = 0;
    if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
        printf("video track is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    ret = _imimp4v2_get_video_duation(handle, timestamp, &duration);
    if (ret != IMIMEDIA_OK) {
        printf("imimp4v2_get_duation error %d\n", ret);
        return ret;
    }
    if (nalu_startcode == sizeof(imi_nalu_startcode4)) {
        frame_data = (char *) malloc(nalu_len);
        if (frame_data == NULL) {
            printf("imimp4v2_write_video_sample malloc error\n");
            return ret;
        }
        memset(frame_data, 0, nalu_len);
        memcpy(frame_data, nalu_data, nalu_len);
        frame_len = nalu_len;
        *(uint32_t * )(frame_data) = htonl(nalu_len - nalu_startcode);
    } else if (nalu_startcode == sizeof(imi_nalu_startcode3)) {
        frame_data = (char *) malloc(nalu_len + 1);
        if (frame_data == NULL) {
            printf("imimp4v2_write_video_sample malloc error\n");
            return ret;
        }
        memset(frame_data, 0, nalu_len + 1);
        memcpy(frame_data + 1, nalu_data, nalu_len);
        frame_len = nalu_len + 1;
        *(uint32_t * )(frame_data) = htonl(nalu_len - sizeof(imi_nalu_startcode4));
    } else {
        printf("imimp4v2_write_video_sample startcode %d\n", nalu_startcode);
        return IMIMEDIA_UNKNOWN_ERROR;
    }
    if (MP4WriteSample(handle->mp4_handle,
                       handle->video_track_id,
                       (const uint8_t *) frame_data,
                       (uint32_t) frame_len,
                       duration,
                       0,
                       keyframe) == false) {
        printf("MP4WriteSample error\n");
        if (frame_data)
            free(frame_data);
        return IMIMEDIA_PARAMS_ERROR;
    }
    if (frame_data)
        free(frame_data);
    return IMIMEDIA_OK;
}

int _imimp4v2_write_h264_video_frame(imimp4v2_info_t handle,
                                     unsigned char *nalu_data,
                                     unsigned int nalu_len,
                                     unsigned int nalu_startcode,
                                     unsigned long long timestamp) {
    imi_h264_nalu_type nalu_type = (imi_h264_nalu_type) IMI_H264_NALU_TYPE(
            (*(unsigned char *) (nalu_data + nalu_startcode)));
    switch (nalu_type) {
        case h264_nalu_type_sps: {
            if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
                    printf("mp4 file handle is invalid\n");
                    return IMIMEDIA_STATUS_ERROR;
                }
                handle->video_track_id = MP4AddH264VideoTrack(handle->mp4_handle,
                                                              handle->video_scale,
                                                              MP4_INVALID_DURATION,
                                                              handle->video_width,
                                                              handle->video_height,
                                                              nalu_data[nalu_startcode + 1],
                                                              nalu_data[nalu_startcode + 2],
                                                              nalu_data[nalu_startcode + 3],
                                                              nalu_startcode - 1);
                if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                    printf("MP4AddH264VideoTrack error\n");
                    return IMIMEDIA_PARAMS_ERROR;
                }
                MP4SetVideoProfileLevel(handle->mp4_handle, 0x7F);
            }
            MP4AddH264SequenceParameterSet(handle->mp4_handle,
                                           handle->video_track_id,
                                           nalu_data + nalu_startcode,
                                           nalu_len - nalu_startcode);
            return IMIMEDIA_OK;
        }
            break;
        case h264_nalu_type_pps: {
            if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                printf("video track is invalid\n");
                return IMIMEDIA_STATUS_ERROR;
            }
            MP4AddH264PictureParameterSet(handle->mp4_handle,
                                          handle->video_track_id,
                                          nalu_data + nalu_startcode,
                                          nalu_len - nalu_startcode);
            return IMIMEDIA_OK;
        }
            break;
        case h264_nalu_type_idr: {
            return _imimp4v2_write_video_sample(handle, nalu_data, nalu_len, nalu_startcode,
                                                timestamp, true);
        }
            break;
        case h264_nalu_type_slice: {
            return _imimp4v2_write_video_sample(handle, nalu_data, nalu_len, nalu_startcode,
                                                timestamp, false);
        }
            break;
        default:
            printf("imimp4v2_write_h264_video_frame nalu type %d\n", nalu_type);
            break;
    }
    return IMIMEDIA_PARAMS_ERROR;
}

int _imimp4v2_write_h265_video_frame(imimp4v2_info_t handle,
                                     unsigned char *nalu_data,
                                     unsigned int nalu_len,
                                     unsigned int nalu_startcode,
                                     unsigned long long timestamp) {
    imi_h265_nalu_type nalu_type = (imi_h265_nalu_type) IMI_H265_NALU_TYPE(
            (*(unsigned char *) (nalu_data + nalu_startcode)));
    switch (nalu_type) {
        case h265_nalu_type_vps: {
            if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
                    printf("mp4 file handle is invalid\n");
                    return IMIMEDIA_STATUS_ERROR;
                }
                handle->video_track_id = MP4AddH265VideoTrack(handle->mp4_handle,
                                                              IMI_VIDEO_TIME_BASE,
                                                              MP4_INVALID_DURATION,
                                                              handle->video_width,
                                                              handle->video_height,
                                                              nalu_data[nalu_startcode + 1],
                                                              nalu_data[nalu_startcode + 2],
                                                              nalu_data[nalu_startcode + 3],
                                                              nalu_startcode - 1);
                if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                    printf("MP4AddH265VideoTrack error\n");
                    return IMIMEDIA_PARAMS_ERROR;
                }
                MP4SetVideoProfileLevel(handle->mp4_handle, 0x7F);
                MP4SetTrackDurationPerChunk(handle->mp4_handle,
                                            handle->video_track_id,
                                            MP4_INVALID_DURATION / handle->video_fps);
            }
            MP4AddH265VideoParameterSet(handle->mp4_handle,
                                        handle->video_track_id,
                                        nalu_data + nalu_startcode,
                                        nalu_len - nalu_startcode,
                                        0);
            return IMIMEDIA_OK;
        }
            break;
        case h265_nalu_type_sps: {
            if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                printf("video track is invalid\n");
                return IMIMEDIA_STATUS_ERROR;
            }
            MP4AddH265SequenceParameterSet(handle->mp4_handle,
                                           handle->video_track_id,
                                           nalu_data + nalu_startcode,
                                           nalu_len - nalu_startcode,
                                           1);
        }
            break;
        case h265_nalu_type_pps: {
            if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
                printf("video track is invalid\n");
                return IMIMEDIA_STATUS_ERROR;
            }
            MP4AddH265PictureParameterSet(handle->mp4_handle,
                                          handle->video_track_id,
                                          nalu_data + nalu_startcode,
                                          nalu_len - nalu_startcode,
                                          1);
            return IMIMEDIA_OK;
        }
            break;
        case h265_nalu_type_idr_w_radl: {
            return _imimp4v2_write_video_sample(handle, nalu_data, nalu_len, nalu_startcode,
                                                timestamp, 1);
        }
            break;
        case h265_nalu_type_trail_r: {
            return _imimp4v2_write_video_sample(handle, nalu_data, nalu_len, nalu_startcode,
                                                timestamp, 0);
        }
            break;
        default:
            printf("imimp4v2_write_h265_video_frame nalu type %d\n", nalu_type);
            break;
    }
    return IMIMEDIA_PARAMS_ERROR;
}

int _imimp4v2_write_video_frame_inner(imimp4v2_info_t handle,
                                      unsigned char *data,
                                      unsigned int data_len,
                                      unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    unsigned int i = 0;
    imi_nalu_array_t nalu_array = imi_get_nalu_array(data, data_len);
    if (nalu_array == NULL) {
        return IMIMEDIA_PARSER_ERROR;
    }
    for (i = 0; i < nalu_array->_nalu_num; i++) {
        unsigned char *nalu_data = nalu_array->_nalu_index[i];
        unsigned int nalu_len = nalu_array->_nalu_len[i];
        unsigned int nalu_startcode = nalu_array->_nalu_startcode[i];
        switch (handle->video_codec) {
            case video_codec_h264:
                ret = _imimp4v2_write_h264_video_frame(handle, nalu_data, nalu_len, nalu_startcode,
                                                       timestamp);
                break;
            case video_codec_h265:
                ret = _imimp4v2_write_h265_video_frame(handle, nalu_data, nalu_len, nalu_startcode,
                                                       timestamp);
                break;
            default:
                ret = IMIMEDIA_PARAMS_ERROR;
                goto exit;
        }
    }
    exit:
    imi_free_nalu_array(nalu_array);
    return ret;
}

int imimp4v2_write_video_frame(imimp4v2_info_t handle,
                               unsigned char *data,
                               unsigned int data_len,
                               unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _imimp4v2_write_video_frame_inner(handle, data, data_len, timestamp);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _imimp4v2_write_aac_audio_frame(imimp4v2_info_t handle,
                                    unsigned char *data,
                                    unsigned int data_len,
                                    unsigned long long timestamp) {
    unsigned long long time = 0;
    if (handle->audio_track_id == MP4_INVALID_TRACK_ID) {
        aac_adts_fixed_header *aac_header = NULL;
        unsigned char *track_config = NULL;
        int time_scale = 0;
        if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
            printf("mp4 file handle is invalid\n");
            return IMIMEDIA_STATUS_ERROR;
        }
        aac_header = imi_parser_aac_header_net(data);
        if (aac_header == NULL) {
            printf("imi_parser_aac_header_net error\n");
            return IMIMEDIA_RESOURCE_ERROR;
        }
        track_config = imi_make_aac_track_configure(aac_header->_profile + 1,
                                                    aac_header->_sampling_frequency_index,
                                                    aac_header->_channel_configuration);
        if (track_config == NULL) {
            printf("imi_make_aac_track_configure error\n");
            free(aac_header);
            return IMIMEDIA_RESOURCE_ERROR;
        }
        time_scale = imi_parser_aac_header_sampling_frequency_index(
                aac_header->_sampling_frequency_index);
        handle->audio_track_id = MP4AddAudioTrack(handle->mp4_handle, (uint32_t) time_scale,
                                                  IMI_AAC_TIME_BASE, MP4_MPEG4_AUDIO_TYPE);
        if (handle->audio_track_id == MP4_INVALID_TRACK_ID) {
            printf("MP4AddAudioTrack error\n");
            return IMIMEDIA_PARAMS_ERROR;
        }
        MP4SetAudioProfileLevel(handle->mp4_handle, 0x2);
        MP4SetTrackESConfiguration(handle->mp4_handle, handle->audio_track_id,
                                   (const uint8_t *) track_config, 2);
        free(track_config);
        free(aac_header);
    }
    if (handle->audio_duration != 0 && handle->audio_total_timestamp >= handle->audio_duration) {
        return IMIMEDIA_EOF;
    }
    time = IMI_AAC_TIME_BASE * 1000 / handle->audio_samplerate;
    handle->audio_lasttimestamp = time;
    handle->audio_total_timestamp += time;
    if (MP4WriteSample(handle->mp4_handle,
                       handle->audio_track_id,
                       (const uint8_t *) (data + AAC_ADTS_HEADER),
                       (uint32_t)(data_len - AAC_ADTS_HEADER),
                       MP4_INVALID_DURATION,
                       0,
                       0) == false) {
        printf("MP4WriteSample error\n");
        return IMIMEDIA_PARAMS_ERROR;
    }
    return IMIMEDIA_OK;
}

int _imimp4v2_write_g711a_audio_frame(imimp4v2_info_t handle,
                                      unsigned char *data,
                                      unsigned int data_len,
                                      unsigned long long timestamp) {
    unsigned long long time = 0;
    if (handle->audio_track_id == MP4_INVALID_TRACK_ID) {
        if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
            printf("mp4 file handle is invalid\n");
            return IMIMEDIA_STATUS_ERROR;
        }
        handle->audio_track_id = MP4AddALawAudioTrack(handle->mp4_handle, handle->audio_samplerate);
        if (handle->audio_track_id == MP4_INVALID_TRACK_ID) {
            printf("MP4AddALawAudioTrack error\n");
            return IMIMEDIA_PARAMS_ERROR;
        }
        MP4SetAudioProfileLevel(handle->mp4_handle, 0x2);
        MP4SetTrackIntegerProperty(handle->mp4_handle,
                                   handle->audio_track_id,
                                   "mdia.minf.stbl.stsd.alaw.channels",
                                   handle->audio_channel);
    }
    if (handle->audio_duration != 0 && handle->audio_total_timestamp >= handle->audio_duration) {
        return IMIMEDIA_EOF;
    }
    time = data_len * 1000 / handle->audio_samplerate;
    handle->audio_lasttimestamp = time;
    handle->audio_total_timestamp += time;
    if (MP4WriteSample(handle->mp4_handle,
                       handle->audio_track_id,
                       (const uint8_t *) data,
                       (uint32_t) data_len,
                       (MP4Duration) data_len,
                       0,
                       0) == false) {
        printf("MP4WriteSample error\n");
        return IMIMEDIA_PARAMS_ERROR;
    }
    return IMIMEDIA_OK;
}

int _imimp4v2_write_audio_frame_inner(imimp4v2_info_t handle,
                                      unsigned char *data,
                                      unsigned int data_len,
                                      unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    switch (handle->audio_codec) {
        case audio_codec_aac:
            ret = _imimp4v2_write_aac_audio_frame(handle, data, data_len, timestamp);
            break;
        case audio_codec_g711a:
            ret = _imimp4v2_write_g711a_audio_frame(handle, data, data_len, timestamp);
            break;
        default:
            return IMIMEDIA_PARAMS_ERROR;
    }
    return ret;
}

int imimp4v2_write_audio_frame(imimp4v2_info_t handle,
                               unsigned char *data,
                               unsigned int data_len,
                               unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _imimp4v2_write_audio_frame_inner(handle, data, data_len, timestamp);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _imimp4v2_merge_h2645_nalu(char *buf, unsigned int buf_size,
                               unsigned int *size,
                               uint8_t **vps, uint32_t *vps_size,
                               uint8_t **sps, uint32_t *sps_size,
                               uint8_t **pps, uint32_t *pps_size) {
    int index = 0;
    int offset = 0;
    if (buf == NULL || buf_size == 0 || size == NULL) {
        return IMIMEDIA_PARAMS_ERROR;
    }
    index = 0;
    if (vps && vps_size) {
        memcpy((unsigned char *) buf + offset, imi_nalu_startcode4, sizeof(imi_nalu_startcode4));
        offset += sizeof(imi_nalu_startcode4);
        for (index = 0; vps_size[index] != 0; index++) {
            if (offset + vps_size[index] > buf_size) {
                printf("vps len is invalid: %d\n", vps_size[index]);
                continue;
            }
            if (vps[index] && vps_size[index]) {
                memcpy((unsigned char *) buf + offset, vps[index], vps_size[index]);
                offset += vps_size[index];
            }
        }
    }
    index = 0;
    if (sps && sps_size) {
        memcpy((unsigned char *) buf + offset, imi_nalu_startcode4, sizeof(imi_nalu_startcode4));
        offset += sizeof(imi_nalu_startcode4);
        for (index = 0; sps_size[index] != 0; index++) {
            if (offset + sps_size[index] > buf_size) {
                printf("sps len is invalid: %d\n", sps_size[index]);
                continue;
            }
            if (sps[index] && sps_size[index]) {
                memcpy((unsigned char *) buf + offset, sps[index], sps_size[index]);
                offset += sps_size[index];
            }
        }
    }
    index = 0;
    if (pps && pps_size) {
        memcpy((unsigned char *) buf + offset, imi_nalu_startcode4, sizeof(imi_nalu_startcode4));
        offset += sizeof(imi_nalu_startcode4);
        for (index = 0; pps_size[index] != 0; index++) {
            if (offset + pps_size[index] > buf_size) {
                printf("sps len is invalid: %d\n", pps_size[index]);
                continue;
            }
            if (pps[index] && pps_size[index]) {
                memcpy((unsigned char *) buf + offset, pps[index], pps_size[index]);
                offset += pps_size[index];
            }
        }
    }
    *size = offset;
    return IMIMEDIA_OK;
}

int _imimp4v2_get_h2645_headers(imimp4v2_info_t handle) {
    int ret = IMIMEDIA_OK;
    uint8_t **vps = NULL, **sps = NULL, **pps = NULL;
    uint32_t *vps_size = NULL, *sps_size = NULL, *pps_size = NULL;
    uint64_t temp = 0;
    if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
        printf("mp4 file handle is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
        printf("video track is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    switch (handle->video_codec) {
        case video_codec_h264: {
            if (MP4GetTrackH264SeqPictHeaders(handle->mp4_handle,
                                              handle->video_track_id,
                                              &vps, &vps_size,
                                              &sps, &sps_size) == false) {
                return IMIMEDIA_FORMAT_ERROR;
            }
        }
            break;
        case video_codec_h265: {
            if (MP4GetTrackH265SeqPictHeaders(handle->mp4_handle,
                                              handle->video_track_id,
                                              &vps, &vps_size,
                                              &sps, &sps_size,
                                              &pps, &pps_size) == false) {
                return IMIMEDIA_FORMAT_ERROR;
            }
            if (vps_size == NULL || sps_size == NULL || pps_size == NULL) {
                return IMIMEDIA_FORMAT_ERROR;
            }
            if (*vps_size == 0 || *sps_size == 0 || *pps_size == 0) {
                return IMIMEDIA_FORMAT_ERROR;
            }
            /*if (MP4GetTrackIntegerProperty(handle->mp4_handle, handle->video_track_id,
                "mdia.minf.stbl.stsd.hvc1.hvcC.profile_compatibility",
                &temp) == false) {
                    return IMIMEDIA_FORMAT_ERROR;
            }
            if (temp != 0x60000000) {
                return IMIMEDIA_FORMAT_ERROR;
            }
            if (MP4GetTrackIntegerProperty(handle->mp4_handle, handle->video_track_id,
                "mdia.minf.stbl.stsd.hvc1.hvcC.m_hvcC_numOfArrays_1B",
                &temp) == false) {
                    return IMIMEDIA_FORMAT_ERROR;
            }
            if (temp != 0x03) {
                return IMIMEDIA_FORMAT_ERROR;
            }*/
        }
            break;
        default:
            return IMIMEDIA_PARAMS_ERROR;
    }
    handle->video_header_buff = (char *) malloc(IMI_NALU_SIZE_MAX * 3);
    if (handle->video_header_buff == NULL) {
        ret = IMIMEDIA_RESOURCE_ERROR;
        goto exit;
    }
    if (_imimp4v2_merge_h2645_nalu(handle->video_header_buff,
                                   IMI_NALU_SIZE_MAX * 3,
                                   &handle->video_header_size,
                                   vps, vps_size,
                                   sps, sps_size,
                                   pps, pps_size) != IMIMEDIA_OK) {
        free(handle->video_header_buff);
        ret = IMIMEDIA_PARAMS_ERROR;
        goto exit;
    }
    exit:
    MP4FreeH2645SeqPictHeaders(vps, vps_size,
                               sps, sps_size,
                               pps, pps_size);
    return ret;
}

int _imimp4v2_open_file_inner(imimp4v2_info_t handle,
                              const char *path,
                              video_codec_id *video,
                              audio_codec_id *audio,
                              unsigned int *vfps,
                              unsigned int *vwidth,
                              unsigned int *vheight,
                              unsigned int *achannel,
                              unsigned int *asamplerate,
                              unsigned long long *duration) {
    int ret = IMIMEDIA_OK;
    uint32_t index = 0, total_track = 0;
    handle->mp4_handle = MP4Read(path);
    if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
        return IMIMEDIA_PARAMS_ERROR;
    }
    total_track = MP4GetNumberOfTracks(handle->mp4_handle, NULL, 0);
    if (total_track <= 0) {
        return IMIMEDIA_PARAMS_ERROR;
    }
    for (index = 0; index < total_track; index++) {
        const char *track_type = NULL;
        MP4TrackId track_id = MP4FindTrackId(handle->mp4_handle, index, NULL, 0);
        if (track_id == MP4_INVALID_TRACK_ID) {
            break;
        }
        track_type = MP4GetTrackType(handle->mp4_handle, track_id);
        if (track_type && strlen(track_type)) {
            if (MP4_IS_VIDEO_TRACK_TYPE(track_type)) {
                const char *video_codec = MP4GetTrackMediaDataName(handle->mp4_handle, track_id);
                if (video_codec && strlen(video_codec)) {
                    if (IMI_TRACK_IS_H264_TYPE(video_codec)) {
                        handle->video_codec = video_codec_h264;
                    } else if (IMI_TRACK_IS_H265_TYPE(video_codec)) {
                        handle->video_codec = video_codec_h265;
                    } else {
                        printf("imimp4v2_open_file video_codec = %s\n", video_codec);
                        return IMIMEDIA_FORMAT_ERROR;
                    }
                } else {
                    printf("imimp4v2_open_file video_codec empty track id = %d\n", track_id);
                }
                handle->video_track_id = track_id;
                handle->video_width = MP4GetTrackVideoWidth(handle->mp4_handle, track_id);
                handle->video_height = MP4GetTrackVideoHeight(handle->mp4_handle, track_id);
                handle->video_fps = (unsigned int) MP4GetTrackVideoFrameRate(handle->mp4_handle,
                                                                             track_id);
                handle->video_index_total = MP4GetTrackNumberOfSamples(handle->mp4_handle,
                                                                       track_id);
                if (handle->video_index_total == 0) {
                    printf("imimp4v2_open_file there is no video data in mp4 file\n");
                    return IMIMEDIA_FORMAT_ERROR;
                }
                handle->video_scale = MP4GetTrackTimeScale(handle->mp4_handle, track_id);
                if (handle->video_scale <= 0) {
                    printf("imimp4v2_open_file video timescale is invalid\n");
                    return IMIMEDIA_FORMAT_ERROR;
                }
                *duration = MP4GetDuration(handle->mp4_handle) * 1000 /
                            MP4GetTrackTimeScale(handle->mp4_handle, track_id);
                ret = _imimp4v2_get_h2645_headers(handle);
                if (ret != IMIMEDIA_OK) {
                    return ret;
                }
            } else if (MP4_IS_AUDIO_TRACK_TYPE(track_type)) {
                const char *audio_codec = MP4GetTrackMediaDataName(handle->mp4_handle, track_id);
                if (audio_codec && strlen(audio_codec)) {
                    if (IMI_TRACK_IS_AAC_TYPE(audio_codec)) {
                        handle->audio_codec = audio_codec_aac;
                    } else if (IMI_TRACK_IS_G711A_TYPE(audio_codec)) {
                        handle->audio_codec = audio_codec_g711a;
                    } else {
                        printf("imimp4v2_open_file audio_codec = %s\n", audio_codec);
                        return IMIMEDIA_FORMAT_ERROR;
                    }
                } else {
                    printf("imimp4v2_open_file audio_codec empty track id = %d\n", track_id);
                }
                handle->audio_track_id = track_id;
                handle->audio_channel = MP4GetTrackAudioChannels(handle->mp4_handle, track_id);
                handle->audio_samplerate = MP4GetTrackTimeScale(handle->mp4_handle, track_id);
                handle->audio_index_total = MP4GetTrackNumberOfSamples(handle->mp4_handle,
                                                                       track_id);
                if (handle->audio_index_total == 0) {
                    printf("imimp4v2_open_file there is no audio data in mp4 file\n");
                    return IMIMEDIA_FORMAT_ERROR;
                }
                handle->audio_scale = MP4GetTrackTimeScale(handle->mp4_handle, track_id);
                if (handle->audio_scale <= 0) {
                    printf("imimp4v2_open_file audio timescale is invalid\n");
                    return IMIMEDIA_FORMAT_ERROR;
                }
            } else {
                printf("imimp4v2_open_file track_type = %s\n", track_type);
            }
        } else {
            printf("imimp4v2_open_file track_type empty track id = %d\n", track_id);
        }
    }
    if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
        printf("imimp4v2_open_file can't find video track\n");
        return IMIMEDIA_FORMAT_ERROR;
    }
    *video = handle->video_codec;
    *audio = handle->audio_codec;
    *vfps = handle->video_fps;
    *vwidth = handle->video_width;
    *vheight = handle->video_height;
    *achannel = handle->audio_channel;
    *asamplerate = handle->audio_samplerate;
    handle->video_index = 1;
    handle->audio_index = 1;
    return IMIMEDIA_OK;
}

int imimp4v2_open_file(const char *path,
                       video_codec_id *video,
                       audio_codec_id *audio,
                       unsigned int *vfps,
                       unsigned int *vwidth,
                       unsigned int *vheight,
                       unsigned int *achannel,
                       unsigned int *asamplerate,
                       unsigned long long *duration,
        /*out*/imimp4v2_info_t *handle) {
    int ret = IMIMEDIA_OK;
    imimp4v2_info_t handle_impl = NULL;
    handle_impl = (imimp4v2_info_t) malloc(sizeof(imimp4v2_info_s));
    if (handle_impl == NULL) {
        printf("imimp4v2_open_file malloc error\n");
        return IMIMEDIA_RESOURCE_ERROR;
    }
    memset(handle_impl, 0, sizeof(imimp4v2_info_s));
    pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
    pthread_mutex_lock(&handle_impl->_lock_mutex);
    ret = _imimp4v2_open_file_inner(handle_impl, path, video, audio, vfps, vwidth, vheight,
                                    achannel, asamplerate, duration);
    if (ret != 0) {
        pthread_mutex_unlock(&handle_impl->_lock_mutex);
        pthread_mutex_destroy(&handle_impl->_lock_mutex);
        free(handle_impl);
        *handle = NULL;
        return ret;
    }
    *handle = handle_impl;
    printf("imimp4v2_open_file handle = %x\n", *handle);
    pthread_mutex_unlock(&handle_impl->_lock_mutex);
    return ret;
}

int _imimp4v2_get_video_frame(imimp4v2_info_t handle,
                              unsigned char *data,
                              unsigned int size,
                              unsigned int *data_len,
                              unsigned long long *timestamp,
                              frame_type_id *frametype) {
    int ret = IMIMEDIA_OK;
    uint8_t *buff = NULL;
    uint32_t buff_len = 0;
    MP4Timestamp ts = 0;
    bool is_key;
    if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
        printf("video track is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->video_index > handle->video_index_total) {
        printf("video track is eof\n");
        return IMIMEDIA_EOF;
    }
    if (MP4ReadSample(handle->mp4_handle,
                      handle->video_track_id,
                      handle->video_index++,
                      &buff,
                      &buff_len,
                      &ts,
                      NULL,
                      NULL,
                      &is_key) == false) {
        printf("MP4ReadSample %d error\n", handle->video_index);
        ret = IMIMEDIA_PARSER_ERROR;
        goto exit;
    }
    if (is_key == true) {
        unsigned int offset = 0;
        unsigned int len = handle->video_header_size + buff_len;
        if (len > size) {
            printf("imimp4v2_get_video_frame size %d is smaller than len %d\n", size, len);
            ret = IMIMEDIA_NOT_ENOUGH_LEN;
            goto exit;
        }
        memcpy(data, handle->video_header_buff, handle->video_header_size);
        offset += handle->video_header_size;
        memcpy(data + offset, imi_nalu_startcode4, sizeof(imi_nalu_startcode4));
        offset += sizeof(imi_nalu_startcode4);
        memcpy(data + offset, buff + sizeof(imi_nalu_startcode4),
               buff_len - sizeof(imi_nalu_startcode4));
        *data_len = buff_len;
        *frametype = frame_type_i;
    } else {
        unsigned int offset = 0;
        unsigned int len = buff_len;
        if (len > size) {
            printf("imimp4v2_get_video_frame size %d is smaller than len %d\n", size, len);
            ret = IMIMEDIA_NOT_ENOUGH_LEN;
            goto exit;
        }
        memcpy(data, imi_nalu_startcode4, sizeof(imi_nalu_startcode4));
        offset += sizeof(imi_nalu_startcode4);
        memcpy(data + offset, buff + sizeof(imi_nalu_startcode4),
               buff_len - sizeof(imi_nalu_startcode4));
        *data_len = buff_len;
        *frametype = frame_type_p;
    }
    *timestamp = (ts * 1000) / handle->video_scale;
    exit:
    MP4FreeSample(buff);
    return ret;
}

int _imimp4v2_get_audio_frame(imimp4v2_info_t handle,
                              unsigned char *data,
                              unsigned int size,
                              unsigned int *data_len,
                              unsigned long long *timestamp,
                              frame_type_id *frametype) {
    int ret = IMIMEDIA_OK;
    uint8_t *buff = NULL;
    uint32_t buff_len = 0;
    MP4Timestamp ts = 0;
    if (handle->audio_track_id == MP4_INVALID_TRACK_ID) {
        printf("audio track is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->audio_index > handle->audio_index_total) {
        printf("audio track is eof\n");
        return IMIMEDIA_EOF;
    }
    if (MP4ReadSample(handle->mp4_handle,
                      handle->audio_track_id,
                      handle->audio_index++,
                      &buff,
                      &buff_len,
                      &ts,
                      NULL,
                      NULL,
                      NULL) == false) {
        printf("MP4ReadSample %d error\n", handle->audio_index);
        ret = IMIMEDIA_PARSER_ERROR;
        goto exit;
    }
    switch (handle->audio_codec) {
        case audio_codec_aac: {
            unsigned int offset = 0;
            unsigned char *aac_header = NULL;
            unsigned int len = buff_len + AAC_ADTS_HEADER;
            if (len > size) {
                printf("imimp4v2_get_audio_frame size %d is smaller than len %d\n", size, len);
                ret = IMIMEDIA_NOT_ENOUGH_LEN;
                goto exit;
            }
            aac_header = imi_make_aac_header_net(handle->audio_samplerate,
                                                 handle->audio_channel,
                                                 len);
            memcpy(data, aac_header, AAC_ADTS_HEADER);
            free(aac_header);
            offset += AAC_ADTS_HEADER;
            memcpy(data + offset, buff, buff_len);
            *data_len = len;
        }
            break;
        case audio_codec_g711a: {
            unsigned int len = buff_len;
            if (len > size) {
                printf("imimp4v2_get_audio_frame size %d is smaller than len %d\n", size, len);
                ret = IMIMEDIA_NOT_ENOUGH_LEN;
                goto exit;
            }
            memcpy(data, buff, buff_len);
            *data_len = buff_len;
        }
            break;
        default:
            ret = IMIMEDIA_PARAMS_ERROR;
            goto exit;
    }
    *timestamp = (ts * 1000) / handle->audio_scale;
    *frametype = frame_type_audio;
    exit:
    MP4FreeSample(buff);
    return ret;
}

int _imimp4v2_get_frame_inner(imimp4v2_info_t handle,
                              unsigned char *data,
                              unsigned int size,
                              unsigned int *data_len,
                              unsigned long long *timestamp,
                              frame_type_id *frametype) {
    int ret = IMIMEDIA_OK;
    if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
        printf("mp4 file handle is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->video_lasttimestamp <= handle->audio_lasttimestamp && handle->video_eof == false) {
        video:
        ret = _imimp4v2_get_video_frame(handle, data, size, data_len, timestamp, frametype);
        if (ret != IMIMEDIA_OK) {
            if (ret == IMIMEDIA_EOF) {
                handle->video_eof = true;
                if (handle->video_eof == true &&
                    handle->audio_eof == true) {
                    return IMIMEDIA_EOF;
                } else {
                    goto audio;
                }
            } else {
                return ret;
            }
        }
        handle->video_lasttimestamp = *timestamp;
    } else {
        audio:
        ret = _imimp4v2_get_audio_frame(handle, data, size, data_len, timestamp, frametype);
        if (ret != IMIMEDIA_OK) {
            if (ret == IMIMEDIA_EOF) {
                handle->audio_eof = true;
                if (handle->video_eof == true &&
                    handle->audio_eof == true) {
                    return IMIMEDIA_EOF;
                } else {
                    goto video;
                }
            } else {
                return ret;
            }
        }
        handle->audio_lasttimestamp = *timestamp;
    }
    return IMIMEDIA_OK;
}

int imimp4v2_get_frame(imimp4v2_info_t handle,
                       unsigned char *data,
                       unsigned int size,
                       unsigned int *data_len,
                       unsigned long long *timestamp,
                       frame_type_id *frametype) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _imimp4v2_get_frame_inner(handle, data, size, data_len, timestamp, frametype);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _imimp4v2_seek_file_inner(imimp4v2_info_t handle, unsigned long long timestamp) {
    unsigned long long video_time = 0;
    unsigned int video_index = 0;
    if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
        printf("mp4 file handle is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    if (handle->video_track_id == MP4_INVALID_TRACK_ID) {
        printf("video track is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    printf("imimp4v2_seek_file timestamp = %lld\n", timestamp);
    video_time = (timestamp / 1000) * handle->video_scale;
    printf("imimp4v2_seek_file video_time = %lld\n", video_time);
    video_index = MP4GetSampleIdFromTime(handle->mp4_handle, handle->video_track_id,
                                         (MP4Timestamp) video_time, false);
    if (video_index == MP4_INVALID_SAMPLE_ID) {
        return IMIMEDIA_PARSER_ERROR;
    }
    while (MP4GetSampleSync(handle->mp4_handle, handle->video_track_id, video_index) == 0) {
        video_index--;
    }
    video_time = MP4GetSampleTime(handle->mp4_handle, handle->video_track_id, video_index);
    if (video_time == MP4_INVALID_TIMESTAMP) {
        return IMIMEDIA_UNKNOWN_ERROR;
    }
    handle->video_index = video_index;
    handle->video_lasttimestamp = (video_time * 1000) / handle->video_scale;
    if (handle->audio_track_id != MP4_INVALID_TRACK_ID) {
        unsigned long long audio_time = 0;
        unsigned int audio_index = 0;
        audio_time = (handle->video_lasttimestamp * handle->audio_scale) / 1000;
        printf("imimp4v2_seek_file audio_time = %lld\n", audio_time);
        audio_index = MP4GetSampleIdFromTime(handle->mp4_handle, handle->audio_track_id,
                                             (MP4Timestamp) audio_time, false);
        if (audio_index == MP4_INVALID_SAMPLE_ID) {
            return IMIMEDIA_PARSER_ERROR;
        }
        handle->audio_index = audio_index;
        handle->audio_lasttimestamp = (audio_time * 1000) / handle->audio_scale;
    }
    handle->video_eof = false;
    handle->audio_eof = false;
    printf("imimp4v2_seek_file to video_index = %d\n", handle->video_index);
    printf("imimp4v2_seek_file to audio_index = %d\n", handle->audio_index);
    return IMIMEDIA_OK;
}

int imimp4v2_seek_file(imimp4v2_info_t handle,
                       unsigned long long timestamp) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _imimp4v2_seek_file_inner(handle, timestamp);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _imimp4v2_close_file_inner(imimp4v2_info_t handle) {
    if (handle->mp4_handle == MP4_INVALID_FILE_HANDLE) {
        printf("mp4 file handle is invalid\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    MP4Close(handle->mp4_handle, 0);
    handle->mp4_handle = MP4_INVALID_FILE_HANDLE;
    handle->video_track_id = MP4_INVALID_TRACK_ID;
    handle->audio_track_id = MP4_INVALID_TRACK_ID;
    if (handle->video_header_buff) {
        free(handle->video_header_buff);
        handle->video_header_buff = NULL;
    }
    printf("imimp4v2_close_file_inner success\n");
    return IMIMEDIA_OK;
}

int imimp4v2_close_file(imimp4v2_info_t handle) {
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    printf("imimp4v2_close_file handle = %x\n", handle);
    ret = _imimp4v2_close_file_inner(handle);
    pthread_mutex_unlock(&handle->_lock_mutex);
    pthread_mutex_destroy(&handle->_lock_mutex);
    free(handle);
    return ret;
}