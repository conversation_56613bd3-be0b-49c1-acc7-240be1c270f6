package com.xiaomi.mico.persistent.func;

import android.content.Context;
import android.hardware.ipcamera.V1_0.Direction;
import android.hardware.ipcamera.V1_0.ICameraRotateController;
import android.hardware.ipcamera.V1_0.IMotorControlCallback;
import android.hardware.ipcamera.V1_0.IStepCallback;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 电机控制
 */
public class CameraRotateManager {

    private final String TAG = "CameraRotateManager";


    private Context mContext;
    private final int TIMEOUT_DELAY = 320; // 同步ipc延时时间
    private final int TIMEOUT_BACK_TO_SLEEP_DELAY = 500; // 背向休眠触发延时时间
    private volatile static CameraRotateManager mInstance;
    private ICameraRotateController mRotateController;
    private int mStepPerCycle = 4080; // 每次转动的步数
    private int mLatestStep = 0;

    // {"ret":0, "angle":50,"elevation":50}
    // 电机默认位置，横向居中，纵向居中
    private final int DEFAULT_ANGLE_VALUE = 100;
    private boolean mIsInReset = false; // 是否在校准电机或者背向休眠中

    private final int ROTATE_LEFT = 1;
    private final int ROTATE_RIGHT = 2;

    private Handler mHandler = new Handler(Looper.getMainLooper());

    public static CameraRotateManager getInstance() {
        if (mInstance == null) {
            synchronized (CameraRotateManager.class) {
                if (mInstance == null) {
                    mInstance = new CameraRotateManager();
                }
            }
        }
        return mInstance;
    }

    private CameraRotateManager() {}

    public void initRotate(Context context) {
//        L.monitor.d("%s =====init=====", TAG);
        this.mContext = context;
        try {
            mRotateController =  ICameraRotateController.getService(Constants.IPCAMERA_SERVICE_NAME);
            if (mRotateController != null) {
                //获取摄像头每旋转⼀圈(360°)  对应电机前进步数 .应用侧会据此计算电机转动⼀步对应度数。
                mStepPerCycle = mRotateController.getStepPerCycle() / 100; // App对应范围0-100
//                L.monitor.d("%s getStepPerCycle: %d", TAG, mStepPerCycle);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * saveRotateStr: 保存转动位置
     */
    private void clearCommandQueue() {
        L.monitor.d("%s =====clearCommandQueue=====", TAG);
        try {
            if (mRotateController != null) {
                mRotateController.clearCommandQueue(new IMotorControlCallback.Stub() {
                    @Override
                    public void onSuccess(int latestSteps) {
                        mLatestStep = latestSteps;
                        L.monitor.d("%s clearCommandQueue --> onSuccess --> data: %d", TAG, latestSteps);
                    }

                    @Override
                    public void onFail(int errNo, String errMsg) {
                        L.monitor.e("%s clearCommandQueue --> onFail --> errNo: %d  -->errMsg: %s", TAG, errNo, errMsg);
                    }
                });
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 电机重置校准
     * fromCloud: 是否来自云端校准
     */
    public void resetMotorStep(boolean firstBoot) {
        L.monitor.d("%s =====resetMotorStep=====", TAG);
        mIsInReset = true;
        try {
            clearCommandQueue();
            CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_ROTATE_DONE, "0");
            if (mRotateController != null) {
                // 电机校准
                mRotateController.resetMotorStep(firstBoot, new IMotorControlCallback.Stub() {
                    @Override
                    public void onSuccess(int latestSteps) {
                        CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_ROTATE_DONE, "1");
                        mLatestStep = latestSteps;
                        mIsInReset = false;
                        L.monitor.d("%s resetMotorStep onSuccess --> data: %d", TAG, latestSteps);
                    }

                    @Override
                    public void onFail(int errNo, String errMsg) {
                        CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_ROTATE_DONE, "0");
                        L.monitor.e("%s resetMotorStep onFail --> errNo: %d -->errMsg: %s", TAG, errNo, errMsg);
                    }
                });
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    public void rotateRight(int step) {
        try {
            if (mRotateController != null) {
                mRotateController.rotateCamera(step, Direction.RIGHT, new IStepCallback.Stub() {
                    @Override
                    public void stepProgress(int latestSteps, int remainingCapacity) {
                        mLatestStep = latestSteps;
                    }
                });
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void rotateLeft(int step) {
        try {
            if (mRotateController != null) {
                mRotateController.rotateCamera(step, Direction.LEFT, new IStepCallback.Stub() {
                    @Override
                    public void stepProgress(int latestSteps, int remainingCapacity) {
                        mLatestStep = latestSteps;
                    }
                });
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Runnable mTimeoutRunnable = new Runnable() {

        @Override
        public void run() {
            clearCommandQueue();
        }
    };

    public void missRotateData(String params) {
        try {
            if (mIsInReset) {
                L.monitor.d("%s Rotate is in resetting", TAG);
                return;
            }
            mHandler.removeCallbacks(mTimeoutRunnable);
            JSONObject qualityJson = new JSONObject(params);
            int operation = qualityJson.getInt("operation");
            if (operation == ROTATE_LEFT || operation == ROTATE_RIGHT) {
                if (ROTATE_LEFT == operation) {
                    rotateLeft(mStepPerCycle * 2);
                } else if (ROTATE_RIGHT == operation) {
                    rotateRight(mStepPerCycle * 2);
                }
                mHandler.postDelayed(mTimeoutRunnable, TIMEOUT_DELAY);
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    boolean isBackToSleep = false;
    boolean latestSleepStatus = true;
    boolean currentSleepStatus = true;
    // 背向休眠
    public void backToSleep(boolean isSleep) {
        latestSleepStatus = isSleep;
        if (isBackToSleep) {
            L.monitor.d("%s In back to sleep.", TAG);
            return;
        }
        currentSleepStatus = isSleep;
        isBackToSleep = true;
        CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_ROTATE_DONE, "0");
        mHandler.postDelayed(() -> {
            // 开启休眠开关，向左转到底休眠, true 不休眠，false 休眠
            try {
                if (mRotateController != null) {
                    mRotateController.sleepMotorStep(!isSleep, new IMotorControlCallback.Stub(){
                        @Override
                        public void onSuccess(int i) {
                            L.monitor.d("%s Back to sleep success.", TAG);
                            isBackToSleep = false;
                            CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_ROTATE_DONE, "1");
                            if (currentSleepStatus != latestSleepStatus) {
                                backToSleep(latestSleepStatus);
                            }
                        }

                        @Override
                        public void onFail(int i, String s) {
                            L.monitor.d("%s Back to sleep failed.", TAG);
                            CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_ROTATE_DONE, "0");
                            isBackToSleep = false;
                        }
                    });
                }
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }, TIMEOUT_BACK_TO_SLEEP_DELAY);
    }
}
