package com.xiaomi.mico.persistent.spec;


import android.content.Context;
import android.database.ContentObserver;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.UVCCameraController;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.cloud.MotionDetection;
import com.xiaomi.mico.persistent.entry.CameraControlConfig;
import com.xiaomi.mico.persistent.func.CameraRotateManager;
import com.xiaomi.mico.persistent.func.CameraScenesManager;
import com.xiaomi.mico.persistent.func.FamilyCareManager;
import com.xiaomi.mico.persistent.monitor.CameraMonitorManager;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;

public class SpecPropertyManager {
    private static final String TAG = "SpecPropertyManager";

    private Context mContext;
    private volatile static SpecPropertyManager mInstance;

    public static SpecPropertyManager getInstance() {
        if (mInstance == null) {
            synchronized (SpecPropertyManager.class) {
                if (mInstance == null) {
                    mInstance = new SpecPropertyManager();
                }
            }
        }
        return mInstance;
    }

    private SpecPropertyManager() {
    }

    public void initProperty(Context context) {
        mContext = context;
        // 注册移动侦测属性监听
        Uri motionDetectionUri = Settings.Global.getUriFor(Constants.KEY_MOTION_DETECTION_CONFIG);
        context.getContentResolver().registerContentObserver(motionDetectionUri, false, mMDObserver);

        // 注册AI检测属性监听
        Uri aiDetectionUri = Settings.Global.getUriFor(Constants.KEY_AI_DETECTION_CONFIG);
        context.getContentResolver().registerContentObserver(aiDetectionUri, false, mAIObserver);

        // 摄像机控制
        Uri cameraControlUri = Settings.Global.getUriFor(Constants.KEY_CAMERA_CONTROL_CONFIG);
        context.getContentResolver().registerContentObserver(cameraControlUri, false, mCCObserver);

        // 自定义功能
        Uri otherFunUri = Settings.Global.getUriFor(Constants.KEY_OTHER_FUNCTION_CONFIG);
        context.getContentResolver().registerContentObserver(otherFunUri, false, mOtherFunObserver);

        // 家人守护功能
        Uri familyGuardUri = Settings.Global.getUriFor(Constants.KEY_FAMILY_GUARD_CONFIG);
        context.getContentResolver().registerContentObserver(familyGuardUri, false, mFamilyGuardObserver);

        // 云存配置开关
        Uri cloudSwitchUri = Settings.Global.getUriFor(Constants.KEY_CLOUD_SWITCH_CONFIG);
        context.getContentResolver().registerContentObserver(cloudSwitchUri, false, mCloudSwitchObserver);

        // 注册系统升级中的监听
        Uri systemUpdateUri = Settings.Global.getUriFor(Constants.KEY_SYSTEM_UPDATE);
        mContext.getContentResolver().registerContentObserver(systemUpdateUri, false, mSystemUpdateObserver);

        // 初始化摄像机控制
        initCameraControlConfig(context);
    }

    private final ContentObserver mMDObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            MotionDetection.getInstance().updateMDConfig();
        }
    };

    private final ContentObserver mAIObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            MotionDetection.getInstance().updateAIConfig();
        }
    };

    private final ContentObserver mCloudSwitchObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            MotionDetection.getInstance().updateCloudSwitch();
        }
    };

    private final ContentObserver mCCObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            String preConfig = CommonApiUtils.getSpfConfig(mContext, Constants.KEY_CAMERA_CONTROL_CONFIG);
            L.monitor.d("%s preConfig: %s", TAG, preConfig);
            CameraControlConfig preCCConfig;
            if (!TextUtils.isEmpty(preConfig)) {
                preCCConfig = new Gson().fromJson(preConfig, CameraControlConfig.class);
            } else {
                preCCConfig = new CameraControlConfig();
            }

            String cameraControl = Settings.Global.getString(mContext.getContentResolver(), Constants.KEY_CAMERA_CONTROL_CONFIG);
            L.monitor.d("%s cameraControl: %s", TAG, cameraControl);
            CameraControlConfig mCurCCConfig;
            if (!TextUtils.isEmpty(cameraControl)) {
                CommonApiUtils.setSpfConfig(mContext, Constants.KEY_CAMERA_CONTROL_CONFIG, cameraControl);
                mCurCCConfig = new Gson().fromJson(cameraControl, CameraControlConfig.class);
            } else {
                mCurCCConfig = new CameraControlConfig();
            }
            // 休眠开关发生变化
            if (preCCConfig.isSwitchStatus() != mCurCCConfig.isSwitchStatus()) {
                // 云端监控，更新休眠状态
                CameraMonitorManager.getInstance().updatePowerStatus(mCurCCConfig.isSwitchStatus());
                // 背向休眠
                CameraRotateManager.getInstance().backToSleep(mCurCCConfig.isSwitchStatus());
                // 移动侦测，更新休眠状态
                MotionDetection.getInstance().updatePowerStatus(mCurCCConfig.isSwitchStatus());
                // 休眠关闭摄像头权限
                CommonApiUtils.setCameraEnable(mContext, mCurCCConfig.isSwitchStatus());
                //夜视功能，更新休眠状态
                CameraScenesManager.getInstance().setCameraMode(!mCurCCConfig.isSwitchStatus());
            }
            // 夜视参数发生变化
            if (preCCConfig.getNightShot() != mCurCCConfig.getNightShot()) {
                CameraScenesManager.getInstance().updateScenesMode(mCurCCConfig.getNightShot());
            }
            // 水印开关发生变化
            if (preCCConfig.isTimeWatermark() != mCurCCConfig.isTimeWatermark()) {
                UVCCameraController.getInstance().setWaterMarkEnable(mCurCCConfig.isTimeWatermark());
            }
            // 畸变矫正开关发生变化
            if (preCCConfig.isImageDistortionCorrection() != mCurCCConfig.isImageDistortionCorrection()) {
                UVCCameraController.getInstance().setDistortionCorrectEnable(mCurCCConfig.isImageDistortionCorrection());
            }
            // 微光全彩模式发生变化
            if (preCCConfig.isGlimmer() != mCurCCConfig.isGlimmer()) {
                CameraScenesManager.getInstance().updateColorMode(mCurCCConfig.isGlimmer());
            }
        }
    };

    private final ContentObserver mOtherFunObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
        }
    };

    private final ContentObserver mFamilyGuardObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            FamilyCareManager.getInstance().updateFamilyCareConfig();
        }
    };

    private final ContentObserver mSystemUpdateObserver =  new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            MotionDetection.getInstance().updateSystemUpdate();
        }
    };

    public void initCameraControlConfig(Context context) {
        String cameraControl = Settings.Global.getString(context.getContentResolver(), Constants.KEY_CAMERA_CONTROL_CONFIG);
        L.monitor.d("%s cameraControl: %s", TAG, cameraControl);
        CameraControlConfig mCCConfig;
        if (cameraControl != null) {
            mCCConfig = new Gson().fromJson(cameraControl, CameraControlConfig.class);
        } else {
            mCCConfig = new CameraControlConfig();
        }

        // 云端监控，更新休眠状态
        CameraMonitorManager.getInstance().updatePowerStatus(mCCConfig.isSwitchStatus());
        // 背向休眠
//        CameraRotateManager.getInstance().backToSleep(mCCConfig.isSwitchStatus());
        // 移动侦测，更新休眠状态
        MotionDetection.getInstance().updatePowerStatus(mCCConfig.isSwitchStatus());
        //夜视功能，更新休眠状态
        CameraScenesManager.getInstance().setCameraMode(!mCCConfig.isSwitchStatus());

        // 更新水印和畸变矫正
        UVCCameraController.getInstance().setWaterMarkEnable(mCCConfig.isTimeWatermark());
        UVCCameraController.getInstance().setDistortionCorrectEnable(mCCConfig.isImageDistortionCorrection());

        // 更新夜视和微光全彩
        CameraScenesManager.getInstance().updateScenesMode(mCCConfig.getNightShot());
        CameraScenesManager.getInstance().updateColorMode(mCCConfig.isGlimmer());
    }
}
