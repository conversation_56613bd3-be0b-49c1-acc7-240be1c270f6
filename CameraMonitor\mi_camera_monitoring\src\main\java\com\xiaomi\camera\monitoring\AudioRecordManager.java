package com.xiaomi.camera.monitoring;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.os.SystemClock;

import com.imilab.opus.OpusCodec;
import com.imilab.opus.OpusUtils;
import com.xiaomi.camera.monitoring.entity.AudioFrameData;
import com.xiaomi.camera.monitoring.utils.L;

public class AudioRecordManager {
    private final String TAG = "AudioRecordManager";

    private volatile static AudioRecordManager mInstance;

    private Thread mRecordThread;
    private boolean isStart;
    private final AudioRecord mAudioRecord;
    private final int mBufferSize;
    private long mOpusEncoder = 0L;
    private AudioRecordStreamProducer mAudioProducerCamera;
    private AudioRecordStreamProducer mAudioProducerCloud;

    public static AudioRecordManager getInstance() {
        if (mInstance == null) {
            synchronized (AudioRecordManager.class) {
                if (mInstance == null) {
                    mInstance = new AudioRecordManager();
                }
            }
        }
        return mInstance;
    }

    public AudioRecordManager() {
//        mBufferSize = AudioRecord.getMinBufferSize(AudioConfig.AUDIO_SAMPLE_RATE,
//                AudioFormat.CHANNEL_IN_MONO, AudioFormat.ENCODING_PCM_16BIT);
        mBufferSize = 1280; // todo 按照之前项目经验，16HZ 单通道 16bit 获取的长度是1280，当前项目返回1408，待排查
        if (Constants.DEBUG) {
            L.monitor.d("%s audio record init, buffer size is %d", TAG, mBufferSize);
        }
        mAudioRecord = new AudioRecord(MediaRecorder.AudioSource.CAMCORDER,
                AudioConfig.AUDIO_SAMPLE_RATE, AudioFormat.CHANNEL_IN_MONO,
                AudioFormat.ENCODING_PCM_16BIT, mBufferSize);
    }

    Runnable recordRunnable = new Runnable() {
        @Override
        public void run() {
            try {
                //设置线程的优先级
                android.os.Process.setThreadPriority(android.os.Process.THREAD_PRIORITY_URGENT_AUDIO);

                if (Constants.DEBUG) {
                    L.monitor.d("%s record running, start recording.", TAG);
                }

                byte[] buffer = new byte[mBufferSize];
                mAudioRecord.startRecording();//开始录音

                long seq = 1;
                while (isStart) {
                    mAudioRecord.read(buffer, 0, mBufferSize);
                    AudioFrameData audioData = new AudioFrameData(SystemClock.elapsedRealtimeNanos() / 1000);
                    short[] inputBytes = OpusUtils.byteArrayToShortArray(buffer);
                    byte[] outputBytes = new byte[buffer.length/8];
                    OpusCodec.getInstance().encode(mOpusEncoder, inputBytes, 0, outputBytes);
                    audioData.dataBytes = outputBytes;
                    audioData.length = outputBytes.length;
                    audioData.index = seq;
                    offerData(audioData);
                    if (Constants.DEBUG) {
                        L.monitor.d("%s record running, record seq is %d", TAG, seq);
                    }
                    seq++;
                }
            } catch (Exception e) {
                L.monitor.e("%s record audio throw exception: %s", TAG, e.getMessage());
            }
        }

    };

    private void offerData(AudioFrameData audioFrame) {
        if (mAudioProducerCamera != null) {
            mAudioProducerCamera.offerData(audioFrame);
        }
        if (mAudioProducerCloud != null) {
            mAudioProducerCloud.offerData(audioFrame);
        }
    }

    public void setAudioProducerCamera(AudioRecordStreamProducer audioRecordStreamProducer) {
        mAudioProducerCamera = audioRecordStreamProducer;
    }

    public void setAudioProducerCloud(AudioRecordStreamProducer audioRecordStreamProducer) {
        mAudioProducerCloud = audioRecordStreamProducer;
    }

    public void removeAudioProducerCamera() {
        mAudioProducerCamera = null;
    }

    public void removeAudioProducerCloud() {
        mAudioProducerCloud = null;
    }


    public synchronized void startAudio() {
        L.monitor.i("%s startAudio isStart: %s", TAG, isStart);
        stopAudio();
        isStart = true;
        mOpusEncoder = OpusCodec.getInstance().createEncoder(AudioConfig.AUDIO_SAMPLE_RATE,
                AudioConfig.OPUS_AUDIO_CHANNEL, AudioConfig.OPUS_AUDIO_COMPLEXITY);
        if (mRecordThread == null) {
            mRecordThread = new Thread(recordRunnable);
            mRecordThread.start();
        }
    }

    public synchronized void stopAudio() {
        L.monitor.i("%s stopAudio isStart: %s", TAG, isStart);
        try {
            isStart = false;
            mAudioRecord.stop();
            L.monitor.d("%s stopAudio completed", TAG);
            if (null != mRecordThread && Thread.State.RUNNABLE == mRecordThread.getState()) {
                Thread.sleep(500);
                mRecordThread.interrupt();
            }
            if (mOpusEncoder != 0) {
                OpusCodec.getInstance().destroyEncoder(mOpusEncoder);
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mRecordThread = null;
            mOpusEncoder = 0L;
        }
    }

    public interface AudioRecordStreamProducer {
        void offerData(AudioFrameData audioFrame);
    }
}
