#ifndef MISS_SERVER_H
#define MISS_SERVER_H

#include "miss.h"

#ifdef MISS_FOR_SERVER
typedef struct miss_client_key_s {
    unsigned char public_key[32];
    unsigned char private_key[32];
    unsigned int port;
    unsigned int port_num;
} miss_client_key_t;

miss_session_t *miss_client_session_open_for_server(miss_device_info_t *dev_info,
        miss_client_config_t *config, miss_client_key_t *key, void * user_data);

int miss_on_connect_server(miss_session_t *session, void *user_data);

void miss_on_video_data_server(miss_session_t *session,
                               miss_frame_header_t *frame_header, void *data, int len, void *user_data);

void miss_on_audio_data_server(miss_session_t *session,
                               miss_frame_header_t *frame_header, void *data, int len, void *user_data);

int miss_rpc_send_for_server(void *rpc_id, const char *method, const char *params, void *user_data, miss_session_t *session);

#endif

#endif
