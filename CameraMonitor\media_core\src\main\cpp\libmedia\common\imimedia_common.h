#ifndef __IMIMEDIA_COMMON__
#define __IMIMEDIA_COMMON__

#include <stdlib.h>
#include <string.h>

#pragma pack(push,1)

#define IMI_VIDEO_TIME_BASE 90000
#define IMI_VIDEO_RTMP_TIME_BASE 1000
#define IMI_AAC_TIME_BASE 1024
#define IMI_NALU_SIZE_MAX 4096
#define IMI_NALU_MAX 32
#define IMI_H264_NALU_TYPE(x) (x & 0x1F)
#define IMI_H265_NALU_TYPE(x) ((x & 0x7E) >> 1);
#define IMI_PTS2TIME_SCALE(CurPTS, PrevPTS, timeScale) \
	((int64_t)((CurPTS - PrevPTS) * 1000 / (double)(1e+6) * timeScale))

typedef enum {
	h264_nalu_type_slice = 1,
	h264_nalu_type_dpa = 2,
	h264_nalu_type_dpb = 3,
	h264_nalu_type_dpc = 4,
	h264_nalu_type_idr = 5,
	h264_nalu_type_sei = 6,
	h264_nalu_type_sps = 7,
	h264_nalu_type_pps = 8,
	h264_nalu_type_aud = 9,
	h264_nalu_type_eoseq = 10,
	h264_nalu_type_eostream = 11,
	h264_nalu_type_fill = 12,
} imi_h264_nalu_type;

typedef enum {
	h265_nalu_type_trail_n = 0,
	h265_nalu_type_trail_r = 1,
	h265_nalu_type_tsa_n = 2,
	h265_nalu_type_tsa_r = 3,
	h265_nalu_type_stsa_n = 4,
	h265_nalu_type_stsa_r = 5,
	h265_nalu_type_radl_n = 6,
	h265_nalu_type_radl_r = 7,
	h265_nalu_type_rasl_n = 8,
	h265_nalu_type_rasl_r = 9,
	h265_nalu_type_bla_w_lp = 16,
	h265_nalu_type_bla_w_radl = 17,
	h265_nalu_type_bla_n_lp = 18,
	h265_nalu_type_idr_w_radl = 19,
	h265_nalu_type_idr_n_lp = 20,
	h265_nalu_type_cra_nut = 21,
	h265_nalu_type_vps = 32,
	h265_nalu_type_sps = 33,
	h265_nalu_type_pps = 34,
	h265_nalu_type_aud = 35,
	h265_nalu_type_eos_nut = 36,
	h265_nalu_type_eob_nut = 37,
	h265_nalu_type_fd_nut = 38,
	h265_nalu_type_sei_prefix = 39,
	h265_nalu_type_sei_suffix = 40,
} imi_h265_nalu_type;

typedef struct imi_nalu_array_s {
	unsigned char* _src_data;
	unsigned int _src_len;
	unsigned int _nalu_num;
	unsigned char* _nalu_index[IMI_NALU_MAX];
	unsigned int _nalu_len[IMI_NALU_MAX];
	unsigned int _nalu_startcode[IMI_NALU_MAX];
} imi_nalu_array_s, *imi_nalu_array_t;

static const unsigned char imi_nalu_startcode4[4] = {0x0, 0x0, 0x0, 0x01};
static const unsigned char imi_nalu_startcode3[3] = {0x0, 0x0, 0x01};

static imi_nalu_array_t imi_get_nalu_array(unsigned char* data, unsigned int data_len) {
	unsigned char* dataEnd = data;
	imi_nalu_array_t nalu_array = (imi_nalu_array_t)malloc(sizeof(imi_nalu_array_s));
	memset(nalu_array, 0, sizeof(imi_nalu_array_s));
	nalu_array->_src_data = data;
	nalu_array->_src_len = data_len;
	while (dataEnd != data + data_len) {
		if ((*dataEnd) == 0x01) {
			int nalu_startcode_size = 0;
			if (memcmp(dataEnd-3, imi_nalu_startcode4, 4) == 0) {
				nalu_startcode_size = sizeof(imi_nalu_startcode4);
			} else if (memcmp(dataEnd-2, imi_nalu_startcode3, 3) == 0) {
				nalu_startcode_size = sizeof(imi_nalu_startcode3);
			} else {
				//do nothing
			}
			if (nalu_startcode_size != 0) {
				nalu_array->_nalu_index[nalu_array->_nalu_num] = dataEnd - nalu_startcode_size + 1;
				nalu_array->_nalu_startcode[nalu_array->_nalu_num] = nalu_startcode_size;
				if (nalu_array->_nalu_num != 0) {
					nalu_array->_nalu_len[nalu_array->_nalu_num-1] = nalu_array->_nalu_index[nalu_array->_nalu_num] - nalu_array->_nalu_index[nalu_array->_nalu_num-1];
				}
				nalu_array->_nalu_num++;
				if (nalu_array->_nalu_num >= IMI_NALU_MAX) {
					free(nalu_array);
					return NULL;
				}
			}
		}
		dataEnd++;
	}
	if (nalu_array->_nalu_num != 0) {
		nalu_array->_nalu_len[nalu_array->_nalu_num-1] = dataEnd - nalu_array->_nalu_index[nalu_array->_nalu_num-1];
		return nalu_array;
	}
	free(nalu_array);
	return NULL;
}

static void imi_free_nalu_array(imi_nalu_array_t nalu_array) {
	free(nalu_array);
	nalu_array = NULL;
}

#pragma pack(pop)

#endif