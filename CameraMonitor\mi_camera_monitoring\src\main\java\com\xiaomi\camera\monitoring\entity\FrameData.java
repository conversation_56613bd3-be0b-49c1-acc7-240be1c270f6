package com.xiaomi.camera.monitoring.entity;

public class FrameData {
    public static final int NV21 = 1;
    public static final int YUV_420_888 = 2;
    public int formatType;
    public byte[] dataBytes;
    public long time;

    public FrameData(long time) {
        this.time = time;
    }

    public int getDataLen() {
        if (dataBytes == null) {
            return 0;
        }
        return dataBytes.length;
    }

    public long getTimeStamp() {
        return time;
    }
}
