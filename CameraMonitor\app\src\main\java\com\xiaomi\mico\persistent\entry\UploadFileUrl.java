package com.xiaomi.mico.persistent.entry;

import org.json.JSONObject;

import java.io.File;

/**
 * 上传到创米云的URL实体类
 */
public class UploadFileUrl {

    private File file;//文件本身
    private String headTag;//请求头Tag
    private String serverPath;//服务器提供的存放路径
    private long expireTime;//失效时间
    private String uploadUrl;//上传的URL

    public UploadFileUrl() {
    }

    public UploadFileUrl(String headTag, String serverPath, long expireTime, String uploadUrl) {
        this.headTag = headTag;
        this.serverPath = serverPath;
        this.expireTime = expireTime;
        this.uploadUrl = uploadUrl;
    }

    public String getServerPath() {
        return serverPath;
    }

    public void setServerPath(String serverPath) {
        this.serverPath = serverPath;
    }

    public long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(long expireTime) {
        this.expireTime = expireTime;
    }

    public String getUploadUrl() {
        return uploadUrl;
    }

    public void setUploadUrl(String uploadUrl) {
        this.uploadUrl = uploadUrl;
    }

    public static UploadFileUrl parse(JSONObject object, String headTag) {
        String path = object.optString("Path");
        long expireTimestamp = object.optLong("ExpireTimestamp", 0);
        String uploadUrl = object.optString("UploadUrl");
        return new UploadFileUrl(headTag, path, expireTimestamp, uploadUrl);
    }

    public static UploadFileUrl fill(UploadFileUrl fileUrl, JSONObject object, String headTag) {
        String path = object.optString("Path");
        long expireTimestamp = object.optLong("ExpireTimestamp", 0);
        String uploadUrl = object.optString("UploadUrl");
        fileUrl.setHeadTag(headTag);
        fileUrl.setServerPath(path);
        fileUrl.setExpireTime(expireTimestamp);
        fileUrl.setUploadUrl(uploadUrl);
        return fileUrl;
    }


    public String getHeadTag() {
        return headTag;
    }

    public void setHeadTag(String headTag) {
        this.headTag = headTag;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }
}
