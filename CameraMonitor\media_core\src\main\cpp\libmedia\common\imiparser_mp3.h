#ifndef IMI_PARSER_MP3
#define IMI_PARSER_MP3 1

#pragma pack(push,1)

#define MP3_HEADER sizeof(mp3_fixed_header)

typedef struct _mp3_fixed_header
{
	unsigned int _syncword:11;				//ͬ����Ϣ
	unsigned int _version:2;				//�汾
	unsigned int _layer:2;					//��
	unsigned int _error_protection:1;		//CRCУ��
	unsigned int _bitrate_index:4;			//λ��
	unsigned int _sampling_frequency:2;		//����Ƶ��
	unsigned int _padding:1;				//֡������
	unsigned int _private:1;				//������
	unsigned int _mode:2;					//����ģʽ
	unsigned int _mode_extension:2;			//����ģʽ
	unsigned int _copyright:1;				//��Ȩ
	unsigned int _original:1;				//ԭ����־
	unsigned int _emphasis:2;				//ǿ��ģʽ
}mp3_fixed_header;

static mp3_fixed_header* imi_parser_mp3_header_net(unsigned char* mp3_data_net)
{
	mp3_fixed_header* fixed = (mp3_fixed_header*)malloc(sizeof(mp3_fixed_header));
	fixed->_syncword = (mp3_data_net[0]|(mp3_data_net[1]<<3));
	fixed->_version = ((mp3_data_net[1]>>3)&0x3);
	fixed->_layer = ((mp3_data_net[1]>>1)&0x3);
	fixed->_error_protection = ((mp3_data_net[1])&0x1);
	fixed->_bitrate_index = ((mp3_data_net[2]>>4)&0xF);
	fixed->_sampling_frequency = ((mp3_data_net[2]>>2)&0x3);
	fixed->_padding = ((mp3_data_net[2]>>1)&0x1);
	fixed->_private = ((mp3_data_net[2])&0x1);
	fixed->_mode = ((mp3_data_net[3]>>6)&0x3);
	fixed->_mode_extension = ((mp3_data_net[3]>>4)&0x3);
	fixed->_copyright = ((mp3_data_net[3]>>3)&0x1);
	fixed->_original = ((mp3_data_net[3]>>2)&0x1);
	fixed->_emphasis = ((mp3_data_net[3])&0x3);
	return fixed;
}

static int imi_parser_mp3_header_bitrate_index(unsigned int index)
{
	switch (index)
	{
	case 0:
		return 0;
	case 1:
		return 8;
	case 2:
		return 16;
	case 3:
		return 24;
	case 4:
		return 32;
	default:
		break;
	}
	return 0;
}

static int imi_parser_mp3_header_sampling_frequency_index(unsigned int index)
{
	switch (index)
	{
	case 0:
		return (int)(22.05*1000);
	case 1:
		return (int)(24*1000);
	case 2:
		return (int)(16*1000);
	default:
		break;
	}
	return 0;
}

static int imi_parser_mp3_header_channel_index(unsigned int index)
{
	switch (index)
	{
	case 2:
		return 2;
	case 3:
		return 1;
	}
	return 0;
}

#pragma pack(pop)

#endif