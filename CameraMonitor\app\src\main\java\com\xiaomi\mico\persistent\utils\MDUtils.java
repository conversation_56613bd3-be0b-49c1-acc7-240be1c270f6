package com.xiaomi.mico.persistent.utils;

import android.graphics.Bitmap;
import android.media.MediaMetadataRetriever;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.MotionDetectionConfig;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.concurrent.ConcurrentLinkedQueue;

public class MDUtils {
    private static final String TAG = "MDUtils";
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    private static final String DEFAULT_START_TIME = "00:00:00";
    private static final String DEFAULT_END_TIME = "23:59:00";
    private static final String DEFAULT_REPETITION = "[1,1,1,1,1,1,1]";
    private static boolean mMDEnabled = false;
    private static String mStartTime = DEFAULT_START_TIME;
    private static String mEndTime = DEFAULT_END_TIME;
    private static String mRepetition = DEFAULT_REPETITION; //看家时间段重复项

    public static void updateMDUtilsConfig(MotionDetectionConfig config) {
        mMDEnabled = config.isEnabled();
        mStartTime = isValidTime(config.getStartTime()) ? config.getStartTime() : DEFAULT_START_TIME;
        mEndTime = isValidTime(config.getEndTime()) ? config.getEndTime() : DEFAULT_END_TIME;
        mRepetition = isValidRepetition(config.getRepetition()) ? config.getRepetition() : DEFAULT_REPETITION;
    }

    // 判断是否在看家时间范围内
    public static boolean shouldReportMd(long time) {
        // 看家开关关闭，不上报
        if (!mMDEnabled) {
            return false;
        }
        // 获取当前星期几
        DayOfWeek currentDayOfWeek = DayOfWeek.from(Calendar.getInstance().toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate());

        // 解析 mMDRepetition 字符串
        int[] reportDays = parseRepetitionString(mRepetition);
        // 检查当前星期几是否在报告范围内
        int currentDayIndex = currentDayOfWeek.getValue() - 1; // 星期一为0，星期日为6
        if (reportDays[currentDayIndex] == 0) {
            return false;
        }

        // 解析时间字符串
        LocalTime startTime = LocalTime.parse(mStartTime, TIME_FORMATTER);
        LocalTime endTime = LocalTime.parse(mEndTime, TIME_FORMATTER);

        // 获取当前时间
        Instant instant = Instant.ofEpochMilli(time);
        LocalTime currentTime = instant.atZone(ZoneId.systemDefault()).toLocalTime();
        // 检查是否跨天
        boolean isCrossDay = endTime.isBefore(startTime);

        if (isCrossDay) {
            // 跨天情况
            return currentTime.isAfter(startTime) || currentTime.isBefore(endTime);
        } else {
            // 不跨天情况
            return currentTime.isAfter(startTime) && currentTime.isBefore(endTime);
        }
    }

    private static int[] parseRepetitionString(String repetition) {
        // 去掉方括号
        String trimmed = repetition.substring(1, repetition.length() - 1);
        // 分割字符串并转换为整数数组
        return Arrays.stream(trimmed.split(","))
                .map(String::trim)
                .mapToInt(Integer::parseInt)
                .toArray();
    }

    private static boolean isValidTime(String time) {
        try {
            // 验证 mStartTime 和 mEndTime 格式
            LocalTime.parse(time, TIME_FORMATTER);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private static boolean isValidRepetition(String repetition) {
        // 验证 mRepetition 格式
        if (!repetition.startsWith("[") || !repetition.endsWith("]")) {
            return false;
        }
        String[] days = repetition.substring(1, repetition.length() - 1).split(",");
        if (days.length != 7) {
            return false;
        }
        for (String day : days) {
            int value = Integer.parseInt(day.trim());
            if (value != 0 && value != 1) {
                return false;
            }
        }
        return true;
    }

    // 通过Mp4获取视频首帧缩略图
    public static void getImageFromVideo(String uploadVideo) {
        L.monitor.d("%s getImageFromVideo: %s", TAG, uploadVideo);
        String uploadImage = uploadVideo.replace(Constants.MP4_PREFIX, Constants.JPEG_PREFIX);
        MediaMetadataRetriever mMediaRetriever =  new MediaMetadataRetriever();
        mMediaRetriever.setDataSource(uploadVideo);
        Bitmap mBitmap = mMediaRetriever.getFrameAtTime();
        if (mBitmap == null) {
            L.monitor.e("%s No image, upload black image.", TAG);
            createAndSaveBlackImage(uploadImage);
            return;
        }
        try (FileOutputStream out = new FileOutputStream(uploadImage)) {
            // 将 Bitmap 压缩为 JPEG 格式，质量为 100（最高质量）
            mBitmap.compress(Bitmap.CompressFormat.JPEG, 50, out);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /*
    获取云存上传的缩略图
    1. 如果上传的缩略图在队列中，则直接返回
    2. 如果上传的缩略图不在队列中，则创建一个黑色图片
     */
    public static String getUploadImage(String uploadVideo, ConcurrentLinkedQueue<String> uploadImgFiles) {
        String uploadImg = uploadVideo.replace(Constants.MP4_PREFIX, Constants.JPEG_PREFIX);

        if (waitForUploadImgFiles(uploadImgFiles)) {
            processUploadImgFile(uploadImgFiles, uploadVideo);
        } else {
            getImageFromVideo(uploadVideo);
        }
        return uploadImg;
    }

    private static boolean waitForUploadImgFiles(ConcurrentLinkedQueue<String> uploadImgFiles) {
        if (!uploadImgFiles.isEmpty()) {
            return true;
        }

        // 尝试等待3s，获取数据
        for (int i = 0; i < 6; i++) {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 恢复中断状态
                throw new RuntimeException("Thread interrupted while waiting for upload image files", e);
            }
            if (!uploadImgFiles.isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /*
    处理上传的图片文件，图片可能会有不存在的情况
     */
    private static void processUploadImgFile(ConcurrentLinkedQueue<String> uploadImgFiles, String uploadVideo) {
        String uploadImg = uploadVideo.replace(Constants.MP4_PREFIX, Constants.JPEG_PREFIX);
        String lastUploadImage = uploadImgFiles.peek(); // 查看队列头部元素
        if (lastUploadImage != null) {
            // 最新的图片可能不存在
            File lastImageFile = new File(lastUploadImage);
            if (!lastImageFile.exists()) {
                L.monitor.e("%s Last image not exists.", TAG);
                uploadImgFiles.poll(); // 移除队列头部元素
                processUploadImgFile(uploadImgFiles, uploadVideo);
                return;
            }
            if (uploadImg.equals(lastUploadImage)) {
                uploadImgFiles.poll(); // 移除队列头部元素
            } else {
                L.monitor.e("%s No image, do copy file.", TAG);
                FileUtils.copyFile(new File(lastUploadImage), new File(uploadImg));
            }
        } else {
            getImageFromVideo(uploadVideo);
        }
    }

    private static void createAndSaveBlackImage(String uploadImg) {
        byte[] fontBlack = FileUtils.createBlackYUV(Constants.CLOUD_IMAGE_WIDTH, Constants.CLOUD_IMAGE_HEIGHT);
        FileUtils.saveImage(fontBlack, uploadImg);
    }
}
