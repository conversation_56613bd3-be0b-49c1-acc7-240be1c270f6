package com.xiaomi.mico.persistent.monitor;

import android.app.job.JobInfo;
import android.app.job.JobParameters;
import android.app.job.JobScheduler;
import android.app.job.JobService;
import android.content.ComponentName;
import android.content.Context;

import com.xiaomi.camera.monitoring.Constants;

public class CheckMonitorRunningService extends JobService {

    private static final ComponentName sCheckMonitorRunningService =
            new ComponentName("com.xiaomi.mico.persistent.monitor", CheckMonitorRunningService.class.getName());

    public static void start(Context context) {
        JobScheduler js = (JobScheduler) context.getSystemService(Context.JOB_SCHEDULER_SERVICE);
        js.cancel(JobServiceID.JOB_ID_CHECK_MONITOR_RUNNING);

        JobInfo jobInfo =
                new JobInfo.Builder(JobServiceID.JOB_ID_CHECK_MONITOR_RUNNING, sCheckMonitorRunningService)
                        .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)
                        .setMinimumLatency(60 * 1000)
                        .build();
        js.schedule(jobInfo);
    }

    @Override
    public boolean onStartJob(JobParameters params) {
        // 如果拔电灭屏状态，停用miss服务
        if (!CommonApiUtils.isMonitorRunning(getApplicationContext())) {
            if (!CommonApiUtils.isBatteryCharging(getApplicationContext())
                    && !CommonApiUtils.isScreenOn(getApplicationContext())) {
                CameraMonitorManager.getInstance().finishMissServer();
            }
        }
        jobFinished(params,false);
        return false;
    }

    @Override
    public boolean onStopJob(JobParameters params) {
        return true;
    }
}
