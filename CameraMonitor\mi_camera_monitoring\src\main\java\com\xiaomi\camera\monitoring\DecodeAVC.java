package com.xiaomi.camera.monitoring;

import android.media.MediaCodec;
import android.media.MediaCodecInfo;
import android.media.MediaFormat;
import android.view.Surface;

import com.xiaomi.camera.monitoring.utils.L;

import java.nio.ByteBuffer;

/**
 * hevc格式编码器
 */
public class DecodeAVC{

    private static final String TAG = "DecodeAVC";

    private static final int FRAME_RATE = 15;//帧率15帧，和底层固定，发送的时间戳计算也要，更重要的是合成视频处也要
    private static final int BIT_RATE_1080P = 2000_000;
    private static final int BIT_RATE_720P = 800_000;

    private MediaCodec mediaCodec;
    private MediaCodec.BufferInfo bufferInfo;

    private byte[] configByte;
    private int width;
    private int height;
    private long generateIndex = 1;
    private MediaFormat mMediaFormat;
    private String mEncodeType = MediaFormat.MIMETYPE_VIDEO_AVC;
    private Surface mSurface;

    public DecodeAVC(Surface surface) {
        this.mSurface = surface;
    }

    public void init(Object... params) {
        mEncodeType = (String) params[0];
        this.width = (int) params[1];
        this.height = (int) params[2];
        L.monitor.v("%s init DecodeHevc: %d %d", TAG, width, height);

        mMediaFormat = MediaFormat.createVideoFormat(mEncodeType, width, height);

        mMediaFormat.setInteger(MediaFormat.KEY_COLOR_FORMAT, MediaCodecInfo.CodecCapabilities.COLOR_FormatYUV420Flexible);

        mMediaFormat.setInteger(MediaFormat.KEY_BIT_RATE, BIT_RATE_720P);

        mMediaFormat.setInteger(MediaFormat.KEY_I_FRAME_INTERVAL, 1);//I帧间隔1
        mMediaFormat.setInteger(MediaFormat.KEY_FRAME_RATE, FRAME_RATE);//帧率
        mMediaFormat.setInteger("vendor.low-latency.enable", 1);
        try {
            mediaCodec = MediaCodec.createDecoderByType(mEncodeType);
            mediaCodec.configure(mMediaFormat, mSurface, null, 0);
            mediaCodec.start();
        } catch (Exception e) {
            L.monitor.e("%s init DecodeHevc throw exception:", TAG, e.getMessage());
            mediaCodec = null;
        }
    }

    public synchronized void decode(byte[] frameData, int length) throws IllegalStateException {
        if (mediaCodec == null) {
            return;
        }
        try {
            final int inputBufferIndex = mediaCodec.dequeueInputBuffer(-1);
            if (inputBufferIndex >= 0) {
                final ByteBuffer inputBuffer = mediaCodec.getInputBuffer(inputBufferIndex);
                inputBuffer.clear();
                if (frameData != null) {
                    inputBuffer.put(frameData);
                    inputBuffer.limit(length);
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, length, computePresentationTime(generateIndex), 0);
                } else {//当buff为只读的情况下
                    inputBuffer.put(frameData);
                    mediaCodec.queueInputBuffer(inputBufferIndex, 0, length, computePresentationTime(generateIndex), 0);
                }
            }

            int outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 0);
            while (outputBufferIndex >= 0) {
                //如果surface绑定了，则直接输入到surface渲染并释放
                mediaCodec.releaseOutputBuffer(outputBufferIndex, true);
                outputBufferIndex = mediaCodec.dequeueOutputBuffer(bufferInfo, 0);
            }
            generateIndex++;
        } catch (Exception e) {
            try {
                mediaCodec = MediaCodec.createDecoderByType(mEncodeType);
                mediaCodec.configure(mMediaFormat, mSurface, null, 0);
                mediaCodec.start();
            } catch (Exception ex) {
                mediaCodec = null;
            }
        }
    }

    public void startDecoder() {
        if (mediaCodec == null) {
            L.monitor.e("%s Please initialize first.", TAG);
            return;
        }
        generateIndex = 1;
        bufferInfo = new MediaCodec.BufferInfo();
    }

    public void stopDecoder() {
        if (mediaCodec != null) {
            try {
                mediaCodec.stop();
                mediaCodec.release();
            } catch (Exception e) {
                L.monitor.e("%s stop DecodeHevc throw exception:", TAG, e.getMessage());
            }
        }
    }

    public synchronized void release() {
        stopDecoder();
        if (mediaCodec != null) {
            mediaCodec.release();
            mediaCodec = null;
        }
    }

    private long computePresentationTime(long frameIndex) {
        return frameIndex * 1000000 / (width * height * 5);
    }
}
