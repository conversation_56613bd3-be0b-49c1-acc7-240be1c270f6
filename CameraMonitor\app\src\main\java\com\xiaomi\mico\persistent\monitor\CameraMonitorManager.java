package com.xiaomi.mico.persistent.monitor;

import android.content.Context;
import android.media.MediaFormat;
import android.net.Uri;
import android.os.Bundle;
import android.provider.Settings;
import android.text.TextUtils;

import com.xiaomi.camera.monitoring.AudioRecordManager;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.UVCCamera0VideoManager;
import com.xiaomi.camera.monitoring.UVCCamera1VideoManager;
import com.xiaomi.camera.monitoring.UVCCameraManager;
import com.xiaomi.camera.monitoring.VideoCameraStreamProducer;
import com.xiaomi.camera.monitoring.entity.MissSession;
import com.xiaomi.camera.monitoring.miss.MissClient;
import com.xiaomi.camera.monitoring.miss.MissConstants;
import com.xiaomi.camera.monitoring.miss.MissDeviceInfo;
import com.xiaomi.camera.monitoring.utils.BuildModelUtils;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.camera.monitoring.utils.StringUtils;
import com.xiaomi.mico.persistent.cloud.MotionDetection;
import com.xiaomi.mico.persistent.utils.MiotManager;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Iterator;

public class CameraMonitorManager extends BaseMissPorting {
    private static final String TAG = "MissManager";

    private final Uri MONITOR_URI = Uri.parse("content://com.xiaomi.mico.home.provider.VIDEO_MONITOR_FEATURE");
    private final String KEY_CODE = "code"; // 0 - 成功， 其他 - 失败
    private final String KEY_CONTENT = "content";
    private final String KEY_METHOD_NAME = "method_name";

    public static MissDeviceInfo missDeviceInfo = new MissDeviceInfo();

    private static volatile CameraMonitorManager sInstance;

    private String mMIotToken;
    private String mEncodeType = MediaFormat.MIMETYPE_VIDEO_HEVC;
    private boolean isVideoStart = false;
    private int sendCount = 0;

    protected CameraMonitorManager() {
        super();
        mMissClient = new MissClient(CameraMonitorManager.this);
        if (BuildModelUtils.isX6A()) {
            mEncodeType = MediaFormat.MIMETYPE_VIDEO_AVC;
        }
    }

    public static CameraMonitorManager getInstance() {
        if (sInstance == null) {
            synchronized (CameraMonitorManager.class) {
                if (sInstance == null) {
                    sInstance = new CameraMonitorManager();
                }
            }
        }

        return sInstance;
    }

    public synchronized void initMiss(Context context) {
        L.monitor.d("%s init miss server", TAG);
        mContext = context;
        // 设置Miss状态
        CommonApiUtils.setMissServerStarting(mContext);
        mExecutorService.submit(() -> {
            try {
                mMissClient.MissFinishServer();
                mSessionList.clear();

                String token = MiotManager.getInstance().getMiotToken();
                if (!TextUtils.isEmpty(token)) {
                    Constants.OT_TOKEN = token;
                    mMIotToken = token;
                    missDeviceInfo.miio_token = mMIotToken;
                    mMissClient.MissServerInit(missDeviceInfo, Constants.CAMERA0_FRAME_SIZE_WIDTH, Constants.CAMERA0_FRAME_SIZE_HEIGHT);
                }
            } catch (Exception e) {
                L.monitor.e("%s get mIot token throw exception: %s", TAG, e.getMessage());
            }
        });
    }

    @Override
    public int cb_miss_rpc_send(int rpc_id, String method, String params) {
        L.monitor.d("%s cb_miss_rpc_send: %d %s, %s", TAG, rpc_id, method, params);
        mExecutorService.submit(() -> {
            try {
                if (TextUtils.isEmpty(mMIotToken)) {
                    L.monitor.d("%s cb_miss_rpc_send onFailed : miot token is null", TAG);
                } else {
                    JSONObject jsonParams = new JSONObject(params);
                    jsonParams.put("token", StringUtils.strTo16(mMIotToken));
                    String content = mIotSend(jsonParams.toString(), method);
                    try {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("result", new JSONObject(content));
                        content = jsonObject.toString();
                        int resultCode = mMissClient.MissRpcProcess(rpc_id, content, content.length());
                        if (Constants.DEBUG) {
                            L.monitor.d("%s miss rpc process result is: %d", TAG, resultCode);
                        }
                    } catch (JSONException e) {
                        L.monitor.e("%s miot send throw exception: %s", TAG, e.getMessage());
                    }
                }
            } catch (Exception e) {
                L.monitor.e("%s cb_miss_rpc_send parse json throw Exception: %s", TAG, e.getMessage());
            }
        });
        return 0;
    }

    @Override
    public int cb_miss_statistics(int rpc_id, String method, String params) {
        L.monitor.d("%s cb_miss_statistics: %d, %s, %s", TAG, rpc_id, method, params);
        try {
            JSONObject jsonParams = new JSONObject();
            jsonParams.put("data", params);
            jsonParams.put("dataType", "EventData");
            mIotSend(jsonParams.toString(), method);
        } catch (Exception e) {
            L.monitor.e("%s cb_miss_statistics: parse json throw Exception: %s", TAG, e.getMessage());
        }
        return 0;
    }

    @Override
    public void cb_miss_on_cmd(int session, int cmd, String params, int length, byte[] user_data) {
        super.cb_miss_on_cmd(session, cmd, params, length, user_data);
        switch (cmd) {
            case MissConstants.MISS_CMD_VIDEO_START:
                // 隐私保护，避免特殊手段绕过miss拦截后依然可以看直播
                if (isCameraDisabled()) {
                    L.monitor.d("%s camera disabled, not allow start video.", TAG);
                    return;
                }
                doVideoSet(cmd);
                getMissSession(session).startVideo(true);
                break;
            case MissConstants.MISS_CMD_VIDEO_STOP:
            case MissConstants.MISS_CMD_VIDEOCALL_STOP:
                getMissSession(session).startVideo(false);
                doVideoSet(cmd);
                break;
            case MissConstants.MISS_CMD_AUDIO_START:
                doAudioSet(cmd);
                getMissSession(session).startAudio(true);
                break;
            case MissConstants.MISS_CMD_AUDIO_STOP:
                getMissSession(session).startAudio(false);
                doAudioSet(cmd);
                break;
            default:
                break;
        }
    }

    @Override
    public int cb_miss_on_disconnect(int session) {
        super.cb_miss_on_disconnect(session);
        if (mSessionList.isEmpty()) {
            doVideoSet(MissConstants.MISS_CMD_VIDEO_STOP);
            doAudioSet(MissConstants.MISS_CMD_AUDIO_STOP);
        }
        return 0;
    }

    private void doVideoSet(int cmd) {
        if (cmd == MissConstants.MISS_CMD_VIDEO_START) {
            if (isAllStopVideo()) {
                if (!isTalking) {
                    MonitorNotificationManager.getInstance().notifyMonitorNotification(mContext.getApplicationContext());
                }
                Settings.Global.putInt(mContext.getContentResolver(), CAMERA_MONITOR_RUNNING, 1);
                isVideoStart = true;
                UVCCamera0VideoManager.getInstance().setVideoProducerCamera(camera0Producer);
                UVCCamera1VideoManager.getInstance().setVideoProducerCamera(camera1Producer);
                if (!UVCCameraManager.getInstance().isCameraRunning()) {
                    UVCCameraManager.getInstance().startCamera();
                }
            }
        } else if (cmd == MissConstants.MISS_CMD_VIDEO_STOP) {
            if (isAllStopVideo()) {
                MonitorNotificationManager.getInstance().cancelMonitorNotification(mContext.getApplicationContext());
                Settings.Global.putInt(mContext.getContentResolver(), CAMERA_MONITOR_RUNNING, 0);
                isVideoStart = false;
                UVCCamera0VideoManager.getInstance().removeVideoProducerCamera();
                UVCCamera1VideoManager.getInstance().removeVideoProducerCamera();
                if (!MotionDetection.getInstance().isRecording()) {
                    UVCCameraManager.getInstance().stopCamera();
                }
            }
        }
    }

    private void doAudioSet(int cmd) {
        if (cmd == MissConstants.MISS_CMD_AUDIO_START) {
            if (isAllStopAudio()) { //如果当前没有音频数据推送,设置音频数据回调
                AudioRecordManager.getInstance().setAudioProducerCamera(mAudioProducer);
                if (!MotionDetection.getInstance().isRecording()) { //如果录制功能也没开启,启动音频
                    AudioRecordManager.getInstance().startAudio();
                }
            }
        } else if (cmd == MissConstants.MISS_CMD_AUDIO_STOP) {
            if (isAllStopAudio()) { //如果当前没有音频数据推送,删除音频数据回调
                AudioRecordManager.getInstance().removeAudioProducerCamera();
                if (!MotionDetection.getInstance().isRecording()) { //如果录制功能也没开启,关闭音频
                    AudioRecordManager.getInstance().stopAudio();
                }
            }
        }
    }

    public void disconnectMiss() {
        if (mMissClient != null) {
            L.monitor.d("%s cmd disconnect miss.", TAG);
            mExecutorService.submit(() -> {
                if (mMissClient != null) {
                    mMissClient.MissCmdSend(mSessionList, MissConstants.MISS_CMD_CAMERA_BOX_QUIT, null, 0, 0);
                }
            });
            startSendingData(false);
            mExecutorService.submit(() -> {
                if (mMissClient != null) {
                    mMissClient.MissCloseServerSession(mSessionList);
                }
            });
        } else {
            L.monitor.e("%s Please init CameraMonitorManager first!!!!!!", TAG);
        }
    }

    public void finishMissServer(boolean force) {
        L.monitor.d("%s finish miss server,is force: %s", TAG, force);
        if (mMissClient != null && (!CommonApiUtils.isMissServerStopped(mContext) || force)) {
            CommonApiUtils.setMissServerStopped(mContext);
            mExecutorService.submit(() -> {
                if (mMissClient != null) {
                    mMissClient.MissFinishServer();
                }
            });
        }
    }

    public void finishMissServer() {
        finishMissServer(false);
    }

    protected void startSendingData(boolean start) {
        if (mMissClient != null) {
            L.monitor.d("%s cmd start sending data, value is %s, speaking: %s", TAG, start, isSpeaker);
            for (MissSession session : mSessionList) {
                cb_miss_on_cmd(session.getSession(), start ? MissConstants.MISS_CMD_VIDEO_START : MissConstants.MISS_CMD_VIDEO_STOP,
                        null, 0, null);
                if (!isSpeaker) {
                    cb_miss_on_cmd(session.getSession(), start ? MissConstants.MISS_CMD_AUDIO_START : MissConstants.MISS_CMD_AUDIO_STOP,
                            null, 0, null);
                }
            }
        } else {
            L.monitor.e("%s Please init CameraMonitorManager first!!!!!!", TAG);
        }
    }

    private String mIotSend(String jsonParams, String method) {
        Bundle params = new Bundle();
        params.putString(KEY_METHOD_NAME, method);
        Bundle result = mContext.getContentResolver()
                .call(MONITOR_URI, "miot_send", jsonParams, params);

        if (result == null) {
            L.monitor.e("%s mIot send failed, bundle is null", TAG);
        } else if (result.getInt(KEY_CODE) == 0) {
            String content = result.getString(KEY_CONTENT);
            if (Constants.DEBUG) {
                L.monitor.d("%s mIot_send provider result is %s", TAG, content);
            }
            return content;
        } else {
            L.monitor.e("%s mIot send failed, code is %s", TAG, result.getInt(KEY_CODE));
        }
        return "";
    }

    private int getEncodeIdByType() {
        if (MediaFormat.MIMETYPE_VIDEO_HEVC.equals(mEncodeType)) {
            return 1;
        } else if (MediaFormat.MIMETYPE_VIDEO_AVC.equals(mEncodeType)) {
            return 2;
        } else {
            return -1;
        }
    }

    private AudioRecordManager.AudioRecordStreamProducer mAudioProducer = (audioFrameData) -> {
        if (!mPowerStatus) return;
//        L.monitor.d("Send Audio timeStamp: %d", audioFrameData.timeStamp);
        mMissClient.MissAudioSend(mSessionList, audioFrameData.dataBytes, audioFrameData.length, audioFrameData.index, audioFrameData.timeStamp);
    };

    private VideoCameraStreamProducer camera0Producer = keyFrameData -> {
        if (!isVideoStart || !mPowerStatus) return;
        Iterator<MissSession> iterator = mSessionList.iterator();
        while (iterator.hasNext()) {
            MissSession session = iterator.next();
            if (session.isRunVideo() && (session.getVideoQuality() == MissSession.VIDEO_QUALITY_HEIGHT
                    || session.getVideoQuality() == MissSession.VIDEO_QUALITY_AUTO)) {
//                L.monitor.d("Send Video timeStamp: %d", keyFrameData.timeStamp);
                int result = mMissClient.MissVideoSend(session, keyFrameData.dataBytes,
                        keyFrameData.length, keyFrameData.isIFrame, keyFrameData.index,
                        keyFrameData.timeStamp, getEncodeIdByType(), Constants.MAIN_STREAM);
                if (result == 26) {
                    sendCount++; // 连续60次发送失败,切换到低质量
                    if (sendCount == 60 && session.getVideoQuality() == MissSession.VIDEO_QUALITY_AUTO) {
                        sendCount = 0;
                        mMissClient.MissCleanBuffer(session.getSession());
                        session.setVideoQuality(MissSession.VIDEO_QUALITY_LOW);
                    }
                } else if (result == 0) {
                    sendCount = 0;
                }
            }
        }
    };

    private VideoCameraStreamProducer camera1Producer = keyFrameData -> {
        if (!isVideoStart || !mPowerStatus) return;
        Iterator<MissSession> iterator = mSessionList.iterator();
        while (iterator.hasNext()) {
            MissSession session = iterator.next();
            if (session.isRunVideo() && session.getVideoQuality() == MissSession.VIDEO_QUALITY_LOW) {
                mMissClient.MissVideoSend(session, keyFrameData.dataBytes,
                        keyFrameData.length, keyFrameData.isIFrame, keyFrameData.index,
                        keyFrameData.timeStamp, getEncodeIdByType(), Constants.SUB_STREAM);
            }
        }
    };

}
