package com.xiaomi.mico.persistent.monitor;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.CameraControlConfig;
import com.xiaomi.mico.persistent.func.CameraRotateManager;
import com.xiaomi.mico.persistent.func.CameraUvcVersion;

public class CameraMonitorReceiver extends BroadcastReceiver {
    private static final String TAG = "CameraMonitorReceiver";
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        L.monitor.v("%s onReceive: %s", TAG, action);
        if (Intent.ACTION_BOOT_COMPLETED.equals(action)) {
            CommonApiUtils.setSpfConfig(context, Constants.KEY_UVC_VERSION_UPDATE, "0");
            CameraUvcVersion.getInstance().updateUvcVersion();

            boolean powerStatus = true;
            String preConfig = CommonApiUtils.getSpfConfig(context, Constants.KEY_CAMERA_CONTROL_CONFIG);
            if (!TextUtils.isEmpty(preConfig)) {
                CameraControlConfig preCCConfig = new Gson().fromJson(preConfig, CameraControlConfig.class);
                powerStatus = preCCConfig.isSwitchStatus();
            }
            String cameraRotateDone = CommonApiUtils.getSpfConfig(context, Constants.KEY_CAMERA_ROTATE_DONE);
            L.monitor.i("%s powerStatus: %s, cameraRotateDone: %s", TAG, powerStatus, cameraRotateDone);
            if (!TextUtils.isEmpty(cameraRotateDone) && cameraRotateDone.equals("0")) {
                if (!powerStatus) { // true:不休眠 false:休眠
                    CameraRotateManager.getInstance().backToSleep(false);
                } else {
                    CameraRotateManager.getInstance().resetMotorStep(true);
                }
            }
        }
    }
}
