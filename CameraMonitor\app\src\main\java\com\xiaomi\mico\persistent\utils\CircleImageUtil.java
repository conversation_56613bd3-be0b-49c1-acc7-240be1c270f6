package com.xiaomi.mico.persistent.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.PorterDuff;
import android.graphics.PorterDuffXfermode;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;

public class CircleImageUtil {

    /**
     * 将资源图片转为圆形
     * 
     * @param context 上下文
     * @param resourceId 资源ID
     * @return 圆形Bitmap
     */
    public static Bitmap getCircleBitmap(Context context, int resourceId) {
        Bitmap sourceBitmap = BitmapFactory.decodeResource(context.getResources(), resourceId);
        return getCircleBitmap(sourceBitmap);
    }

    /**
     * 将Bitmap转为圆形
     * 
     * @param bitmap 原始Bitmap
     * @return 圆形Bitmap
     */
    public static Bitmap getCircleBitmap(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        
        int width = bitmap.getWidth();
        int height = bitmap.getHeight();
        int size = Math.min(width, height);
        
        Bitmap output = Bitmap.createBitmap(size, size, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(output);
        
        final Paint paint = new Paint();
        final Rect rect = new Rect(0, 0, size, size);
        
        paint.setAntiAlias(true);
        canvas.drawARGB(0, 0, 0, 0);
        
        // 绘制圆形
        canvas.drawCircle(size / 2f, size / 2f, size / 2f, paint);
        
        // 设置混合模式
        paint.setXfermode(new PorterDuffXfermode(PorterDuff.Mode.SRC_IN));
        
        // 居中裁剪
        int left = (width - size) / 2;
        int top = (height - size) / 2;
        Rect srcRect = new Rect(left, top, left + size, top + size);
        
        // 绘制图片
        canvas.drawBitmap(bitmap, srcRect, rect, paint);
        
        return output;
    }
} 