package com.xiaomi.mico.persistent.cloud;

import android.content.Context;
import android.hardware.ipcamera.V1_0.UVC_ALGO_ENUM;
import android.hardware.ipcamera.V1_0.UVC_EVENT_ENUM;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.UVCCameraController;
import com.xiaomi.camera.monitoring.UVCEventManager;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.AiDetectionConfig;
import com.xiaomi.mico.persistent.entry.CloudImgEntry;
import com.xiaomi.mico.persistent.entry.CloudUploadEntry;
import com.xiaomi.mico.persistent.entry.CloudVipConfig;
import com.xiaomi.mico.persistent.entry.MotionDetectionConfig;
import com.xiaomi.mico.persistent.entry.PreUploadEntry;
import com.xiaomi.mico.persistent.func.FamilyCareManager;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;
import com.xiaomi.mico.persistent.spec.SpecConstants;
import com.xiaomi.mico.persistent.spec.SpecEventSend;
import com.xiaomi.mico.persistent.utils.FileUtils;
import com.xiaomi.mico.persistent.utils.IntByteStringHexUtil;
import com.xiaomi.mico.persistent.utils.MDUtils;

import org.apache.commons.codec.binary.Base64;

import java.io.File;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 移动侦测功能
 * Vip用户，检测到事件后，延长录制30秒视频，上传到云端，如无事件继续触发停止上传
 * 非Vip用户，检测到事件后，录制9秒视频，上传到云端，录像间隔受看家助手间隔控制
 */
public class MotionDetection377 extends MotionDetection {
    private static final String TAG = "MotionDetection";

    private Context mContext;
    private final int ONE_MINUTE = 60 * 1000;
    private final int ONE_HOUR = 60 * ONE_MINUTE;
    private static final String IP_ADDRESS = "***********";
//    private static final String IP_ADDRESS = "*************";
    private static final int IP_PORT = 5678;

    private boolean mSystemUpdating = false; // 系统是否在升级中
    private boolean mPowerStatus = true; // 休眠状态开关，ture:不休眠 false:休眠
    private MotionDetectionConfig mMDConfig;
    private AiDetectionConfig mAIConfig;
    private CloudVipConfig mVipConfig;

    private int mGetDomainFailTimes = 0;
    private boolean isRecording = false;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private CloudFileUpload mCloudFileUpload;
    private CloudVipSwitch mCloudVipSwitch;
    private SocketClientManager mSocketManager;
    private long mStartRecordTime = 0;
    private boolean mCloudUploadSwitch = false;
    private long[] mAiEventOccured = new long[3];

    protected ExecutorService mCloudExecutor = Executors.newSingleThreadExecutor();
    private final Handler mHandler = new Handler(Looper.getMainLooper());
    private boolean mSocketConnected = false;
    private ByteBuffer mFileBuffer;
    private final LinkedBlockingQueue<CloudUploadEntry> mUploadVideoFiles = new LinkedBlockingQueue<>(20); // 待上传的视频列表
    private final LinkedBlockingQueue<CloudImgEntry> mUploadImgFiles = new LinkedBlockingQueue<>(20); // 待上传的图片列表
    private final HashMap<String, PreUploadEntry> mPreUploadList = new HashMap<>();

    public void initMotionDetection(Context context) {
        this.mContext = context;
        mCloudFileUpload = new CloudFileUpload(mUploadCallback);
        mCloudVipSwitch = new CloudVipSwitch(mVipSwitchCallback);
        mSocketManager = new SocketClientManager();
        mSocketManager.init(IP_ADDRESS, IP_PORT);
        mSocketManager.setOnConnStatusLis(connStatusLis);
        mSocketManager.setOnMsgRecLis(msgRecLis);
        // 注册UVC事件回调
        UVCEventManager.getInstance().init(this::doEventOccurred);
        //获取保存的Vip状态
        mVipConfig = CommonApiUtils.getVipStatus(mContext);
        L.monitor.i("%s Vip: %s", TAG, mVipConfig);
        delOldCloudFile();
        mFileBuffer = ByteBuffer.allocate(1024 * 1024); // 初始容量为 1MB，可以根据需要调整
    }

    public void initStatus() {
        // 获取当前系统是否在升级中
        mSystemUpdating = CommonApiUtils.isSystemUpdating(mContext);
        L.monitor.d("%s initStatus SystemUpdating: %s", TAG, mSystemUpdating);
        // 如果的当前是vip用户，或者云存到期，获取云存vip和开关状态
        if (mVipConfig.isVip() || System.currentTimeMillis() > mVipConfig.getEndTime()) {
            mCloudExecutor.execute(() -> mCloudVipSwitch.getDomainUrl());
        }
        // 获取移动侦测配置
        updateMDConfig();
        // AI检测配置
        updateAIConfig();
        mHandler.post(() -> {
            // 启事件监听
            UVCEventManager.getInstance().startEvent();
        });
    }

    public boolean isRecording() {
        L.monitor.i("%s isRecording: %s", TAG, isRecording);
        return isRecording;
    }

    public boolean isVip() {
        return mVipConfig.isVip();
    }

    // AI事件，录制并上传视频
    public void uploadVideo() {
        startRecord(SpecConstants.EVENT_TYPE_AI);
        mSocketManager.sendData(new SendAIEvent());
    }

    private synchronized void startRecord(String eventType) {
        if (!mSocketConnected) {
//            L.monitor.d("%s Socket is disconnect, do not record", TAG);
            return;
        }
        // 如果当前是休眠状态，不录制视频
        if (!mPowerStatus) {
//            L.monitor.d("%s Camera is power off, do not record", TAG);
            return;
        }
        // Vip用户并且云存开关是关闭状态，不上传云存视频。非Vip用户需要上传9s的云存视频
        if (!mCloudUploadSwitch && mVipConfig.isVip()) {
//            L.monitor.d("%s Cloud upload switch is close", TAG);
            return;
        }
        // 系统升级中，不录制视频
        if (mSystemUpdating) {
//            L.monitor.d("%s System is Updating.", TAG);
            return;
        }
        // 非AI事件，判断是否满足录像间隔，AI事件立刻录制
        if (!SpecConstants.EVENT_TYPE_AI.equals(eventType)) {
            // vip过期或者还没开通vip，按照配置的录像间隔录制
            long curTime = System.currentTimeMillis();
            if (curTime > mVipConfig.getEndTime()) {
                int alarmInterval = mMDConfig.getAlarmInterval();
                if (curTime - mStartRecordTime < alarmInterval * ONE_MINUTE) {
//                    Log.d(TAG, "startRecord -->mMDInterval:" + alarmInterval + " -->mStartRecordTime:" + mStartRecordTime);
                    return;
                }
                if (!mMDConfig.isEnabled()) {
//                    L.monitor.d("%s MD config is false", TAG);
                    return;
                }
            }
        }
//        L.monitor.d("%s StartRecord eventType: %s", TAG, eventType);
        if (!isRecording) {
            mSocketManager.sendData(new SendStartRecord());
            delOldCloudFile();
            mUploadVideoFiles.clear();
            mUploadImgFiles.clear();
            mPreUploadList.clear();
            isRecording = true;
            //创建上传线程
            mHandlerThread = new HandlerThread("UploadBackground");
            mHandlerThread.start();
            mBackgroundHandler = new Handler(mHandlerThread.getLooper());
            // 开启请求云存上传
            mCloudExecutor.execute(() -> mCloudFileUpload.getDomainUrl());
            // 获取录制的开始时间
            mStartRecordTime = System.currentTimeMillis();
        }
    }

    private synchronized void stopRecord(boolean sendTo377) {
        L.monitor.d("%s stopRecord isRecording: %s", TAG, isRecording);
        if (sendTo377) {
            mSocketManager.sendData(new SendStopRecord());
        }
        if (isRecording) {
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
            mUploadVideoFiles.clear();
            mUploadImgFiles.clear();
            mPreUploadList.clear();
            isRecording = false;
        }
    }

    private void doEventOccurred(int event) {
        // 家人守护,触发人形事件
        if (UVC_EVENT_ENUM.EVENT_PERSON_DETECT == event) {
            FamilyCareManager.getInstance().peopleEventOccurred();
        }
        // 检查AI配置是否允许该事件
        if (!isEventAllowed(event)) {
//            L.monitor.v("%s Do not allow event: %d", TAG, event);
            return;
        }
        // 获取事件类型，开始录制
        String eventType = getEventType(event);
        startRecord(eventType);
        // AI Event事件上报独立计时，最短间隔一分钟上报一次
        long curTime = System.currentTimeMillis();
        if (curTime - mAiEventOccured[event] > ONE_MINUTE) {
            mAiEventOccured[event] = curTime;
            SpecEventSend.eventAIReport(eventType);
        }
    }

    private boolean isEventAllowed(int event) {
        switch (event) {
            case UVC_EVENT_ENUM.EVENT_PERSON_DETECT:
                return mAIConfig.isHumanDetection();
            case UVC_EVENT_ENUM.EVENT_SCENE_CHANGED:
                return mAIConfig.isMoveDetection();
            case UVC_EVENT_ENUM.EVENT_CRY_DETECT:
                return mAIConfig.isBabyCryDetection();
        }
        return false; // 默认情况下不允许其它事件
    }

    private String getEventType(int event) {
        switch (event) {
            case UVC_EVENT_ENUM.EVENT_PERSON_DETECT:
                return SpecConstants.EVENT_TYPE_PEOPLE_MOTION;
            case UVC_EVENT_ENUM.EVENT_SCENE_CHANGED:
                return SpecConstants.EVENT_TYPE_OBJECT_MOTION;
            case UVC_EVENT_ENUM.EVENT_CRY_DETECT:
                return SpecConstants.EVENT_TYPE_BABY_CRY;
        }
        return SpecConstants.EVENT_TYPE_OBJECT_MOTION;
    }

    // Socket链接状态更新
    private SocketClientManager.OnConnStatusLis connStatusLis = new SocketClientManager.OnConnStatusLis() {

        @Override
        public void onConnected() {
            mSocketConnected = true;
        }

        @Override
        public void onDisconnected(String error) {
            mSocketConnected = false;
            if (isRecording) {
                stopRecord(false);
            }
        }

        @Override
        public void onError(String error) {

        }
    };

    // 接收377端返回的图片和视频数据 https://zlxlqmxdqa.feishu.cn/wiki/NvAAwJiTti7Tp4k44EEcK6A8nAb
    private SocketClientManager.OnMsgRecLis msgRecLis = body -> {
        try {
            // 获取文件内容
            int fileLen = body.length - MDConstants.TOTAL_LEN;
            byte[] fileData = new byte[fileLen];
            System.arraycopy(body, MDConstants.TOTAL_LEN, fileData, 0, fileLen);
            ensureCapacity(mFileBuffer, fileLen);
            mFileBuffer.put(fileData);
            // 获取文件类型
            boolean fileEnd = body[5] == 1;
            if (fileEnd) {
                // 获取文件类型
                int fileType = body[0];
                // 获取事件类型
                int eventType = body[1];
                // 当前上传图片或视频的index
                int offset = ((body[2] & 0xFF) << 8) | (body[3] & 0xFF);
                // 是否最后一个视频文件
                boolean isEnd = body[4] ==  1;
                // Vip需要连续录制，当录制时长大于等于30min，需要获取新的fieldId继续
                boolean nextFieldId = body[6] == 1;
                if (nextFieldId) {
                    mCloudExecutor.execute(() -> mCloudFileUpload.getDomainUrl());
                }
                // 原始视频文件的签名信息
                byte[] sha256Byte = new byte[MDConstants.SHA256_LEN];
                System.arraycopy(body, MDConstants.OTHER_LEN, sha256Byte, 0, MDConstants.SHA256_LEN);
                String fileSign = Base64.encodeBase64String(sha256Byte);
                // 获取MD5进行校验
                byte[] md5Byte = new byte[MDConstants.MD5_LEN];
                System.arraycopy(body, MDConstants.OTHER_LEN + MDConstants.SHA256_LEN, md5Byte, 0, MDConstants.MD5_LEN);
                String md5 = IntByteStringHexUtil.byteArrayToHexStr(md5Byte);
                L.monitor.d("%s fileType: %s, eventType: %s, offset: %d, isEnd: %s, nextFieldId: %s\n MD5: %s \n fileSign: %s",
                        TAG, fileType, eventType, offset, isEnd, nextFieldId, md5, fileSign);
                // 获取文件内容
                mFileBuffer.flip();
                byte[] fileContent = new byte[mFileBuffer.remaining()];
                mFileBuffer.get(fileContent);
                mFileBuffer.clear();
                String fileMd5 = FileUtils.getFileMd5Byte(fileContent);
                L.monitor.d("%s fileMd5: %s", TAG, fileMd5);
                if (Constants.SAVE_377_FILE) {
                    if (MDConstants.FILE_TYPE_JPEG == fileType) {
                        FileUtils.createFileToByte("/mnt/sdcard/CloudRecord/" + md5 + ".jpg");
                    }  else if (MDConstants.FILE_TYPE_VIDEO == fileType) {
                        FileUtils.createFileToByte("/mnt/sdcard/CloudRecord/" + md5 + ".mp4");
                    }
                    FileUtils.writeByteToFile(fileContent);
                    FileUtils.closeFileToByte();
                }
                if (!md5.equals(fileMd5)) {
                    L.monitor.e("%s MD5 verify failed.", TAG);
                    return;
                }
                // 获取当前视频或图片的加密key
                byte[] keyByte = new byte[MDConstants.KEY_LEN];
                System.arraycopy(body, MDConstants.OTHER_LEN + MDConstants.SHA256_LEN + MDConstants.MD5_LEN, keyByte, 0, MDConstants.KEY_LEN);
                String keyStr = IntByteStringHexUtil.byteArrayToHexStr(keyByte);
                L.monitor.d("%s keyStr: %s", TAG, keyStr);

                if (MDConstants.FILE_TYPE_JPEG == fileType) {
                    CloudImgEntry entry = new CloudImgEntry(fileContent, fileSign);
                    mUploadImgFiles.add(entry);
                } else if (MDConstants.FILE_TYPE_VIDEO == fileType) {
                    boolean shouldReportMd = MDUtils.shouldReportMd(System.currentTimeMillis());
                    CloudUploadEntry entry = new CloudUploadEntry(fileContent, fileSign, getEventType377(eventType),
                            isEnd, offset, !shouldReportMd);
                    entry.setPreUploadEntry(mPreUploadList.get(keyStr));
                    mUploadVideoFiles.add(entry);
                }
            }
        } catch (Exception e) {
            mFileBuffer.clear();
            L.monitor.e("%s SocketClientManager.OnMsgRecLis error: %s", TAG, e.getMessage());
            e.printStackTrace();
        }
    };

    private void ensureCapacity(ByteBuffer buffer, int additionalCapacity) {
        if (buffer.remaining() < additionalCapacity) {
            int newCapacity = buffer.capacity() * 2;
            while (newCapacity - buffer.position() < additionalCapacity) {
                newCapacity *= 2;
            }
            ByteBuffer newBuffer = ByteBuffer.allocate(newCapacity);
            buffer.flip();
            newBuffer.put(buffer);
            mFileBuffer = newBuffer;
        }
    }

    private String getEventType377(int eventType) {
        if (MDConstants.EVENT_TYPE_OBJ == eventType) {
            return SpecConstants.EVENT_TYPE_OBJECT_MOTION;
        } else if (MDConstants.EVENT_TYPE_PEO == eventType) {
            return SpecConstants.EVENT_TYPE_PEOPLE_MOTION;
        } else if (MDConstants.EVENT_TYPE_AI == eventType) {
            return SpecConstants.EVENT_TYPE_AI;
        } else if (MDConstants.EVENT_TYPE_OBJ + MDConstants.EVENT_TYPE_PEO == eventType) {
            return SpecConstants.EVENT_TYPE_OBJECT_MOTION + ":" + SpecConstants.EVENT_TYPE_PEOPLE_MOTION;
        } else if (MDConstants.EVENT_TYPE_OBJ + MDConstants.EVENT_TYPE_AI == eventType) {
            return SpecConstants.EVENT_TYPE_OBJECT_MOTION + ":" + SpecConstants.EVENT_TYPE_AI;
        } else if (MDConstants.EVENT_TYPE_PEO + MDConstants.EVENT_TYPE_AI == eventType) {
            return SpecConstants.EVENT_TYPE_PEOPLE_MOTION + ":" + SpecConstants.EVENT_TYPE_AI;
        }
        return SpecConstants.EVENT_TYPE_OBJECT_MOTION;
    }

    private final CloudVipSwitch.UploadCallback mVipSwitchCallback = new CloudVipSwitch.UploadCallback() {

        @Override
        public void getDomainUrlFailed() {
            // 获取Domain url失败,3s后尝试重新获取
            mHandler.postDelayed(() -> mCloudExecutor.execute(() -> mCloudVipSwitch.getDomainUrl()), 3000);
        }

        @Override
        public void vipStatusCallback(CloudVipConfig vipConfig) {
            L.monitor.i("%s Vip config: %s", TAG, vipConfig);
            CommonApiUtils.saveVipStatus(mContext, vipConfig);
            mVipConfig = vipConfig;
            mSocketManager.sendData(new SendVipData(mVipConfig.isVip()));
        }

        @Override
        public void cloudSwitchCallback(boolean cloudSwitch) {
            L.monitor.i("%s Cloud switch: %s", TAG, cloudSwitch);
            mCloudUploadSwitch = cloudSwitch;
        }
    };

    private final CloudFileUpload.UploadCallback mUploadCallback = new CloudFileUpload.UploadCallback() {

        @Override
        public void getDomainUrlFailed() {
            // 获取Domain url失败,尝试重新获取
            mGetDomainFailTimes++;
            if (mGetDomainFailTimes < 3) {
                mCloudExecutor.execute(() -> mCloudFileUpload.getDomainUrl());
            } else {
                stopRecord(true); // 三次获取失败，停止录制
                mGetDomainFailTimes = 0;
            }
        }

        @Override
        public void getDomainUrlSuccess(String security, String iv) {
            mGetDomainFailTimes = 0;
            byte[] ivKey = Base64.decodeBase64(iv);
            byte[] seKey = Base64.decodeBase64(security);
            L.monitor.d("%s key: %s, iv: %s", TAG, IntByteStringHexUtil.byteArrayToHexStr(seKey),
                    IntByteStringHexUtil.byteArrayToHexStr(ivKey));
            mSocketManager.sendData(new SendKeyIv(seKey, ivKey, mVipConfig.isVip()));
        }

        @Override
        public void preUploadCallback(PreUploadEntry preUploadEntry) {
            String security = preUploadEntry.getSecurityKey();
            byte[] seKey = Base64.decodeBase64(security);
            mPreUploadList.put(IntByteStringHexUtil.byteArrayToHexStr(seKey), preUploadEntry);
            L.monitor.d("%s PreUploadList size: %d", TAG, mPreUploadList.size());
            if (mPreUploadList.size() == 1) {
                nextUpload();
            }
        }

        @Override
        public void doNextUpload(PreUploadEntry preUploadEntry) {
            nextUpload();
        }

        @Override
        public void uploadError(int code, boolean isEnd) {
            L.monitor.e("%s uploadError code: %d", TAG, code);
            if (code == CloudErrorCode.ERROR_CODE_400221) { // 错误的vip状态，尝试重新获取
                mCloudExecutor.execute(() -> mCloudVipSwitch.getDomainUrl());
            } else if (code == CloudErrorCode.ERROR_CODE_PRE_UPLOAD_ERROR
                    || code == CloudErrorCode.ERROR_CODE_FILE_UPLOAD_ERROR
                    || code == CloudErrorCode.ERROR_CODE_META_UPLOAD_ERROR) {
                L.monitor.e("%s CloudFileUpload occur error", TAG);
            }
            if (isEnd) {
                stopRecord(true);
            } else {
                nextUpload();
            }
        }
    };

    private void nextUpload() {
        mBackgroundHandler.post(()->{
            try {
                boolean isVip = mVipConfig.isVip();
                // 上传过程中，vip用户关闭云存开关 或 设备休眠，停止录制上传
                if ((isVip && !mCloudUploadSwitch) || !mPowerStatus) {
                    stopRecord(true);
                    return;
                }
                // 获取视频信息
                CloudUploadEntry cloudUploadEntry = mUploadVideoFiles.take();
                PreUploadEntry preUploadEntry = cloudUploadEntry.getPreUploadEntry();
                byte[] uploadVideo = cloudUploadEntry.getVideoByte();
                String uploadVideoSign = cloudUploadEntry.getVideoSign();
                String eventType = cloudUploadEntry.getEventType();
                int offset = cloudUploadEntry.getOffset();
                boolean isEnd = cloudUploadEntry.isEnd();
                boolean ignoreEvent = cloudUploadEntry.isIgnoreEvent();
                // 获取图片信息
                CloudImgEntry cloudImgEntry = mUploadImgFiles.take();
                byte[] uploadImg = cloudImgEntry.getImageName();
                String uploadImgSing = cloudImgEntry.getImageSign();
                L.monitor.i("%s nextUpload eventType:%s, offset:%d, isEnd:%s, ignoreEvent:%s",
                        TAG, eventType, offset, isEnd, ignoreEvent);
                mCloudFileUpload.uploadFile377(preUploadEntry, isVip, uploadImg, uploadImgSing,
                        uploadVideo, uploadVideoSign, offset, isEnd, eventType, ignoreEvent);
                if (isEnd) {
                    stopRecord(true);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
    }

    // Spec变化，更新休眠状态
    public void updatePowerStatus(boolean status) {
        mPowerStatus = status;
    }

    // 更新移动侦测配置
    public void updateMDConfig() {
        // 获取移动侦测配置
        String motionDetection = Settings.Global.getString(mContext.getContentResolver(), Constants.KEY_MOTION_DETECTION_CONFIG);
//        L.monitor.d("%s updateMDConfig motionDetection: %s", TAG, motionDetection);
        if (!TextUtils.isEmpty(motionDetection)) {
            mMDConfig = new Gson().fromJson(motionDetection, MotionDetectionConfig.class);
        } else {
            mMDConfig = new MotionDetectionConfig();
        }
        L.monitor.i("%s MDConfig: %s", TAG, mMDConfig.toString());
        // 更新看着助手时间配置
        MDUtils.updateMDUtilsConfig(mMDConfig);
    }

    // 更新AI检测配置
    public void updateAIConfig() {
        // AI检测配置
        String aimDetection = Settings.Global.getString(mContext.getContentResolver(), Constants.KEY_AI_DETECTION_CONFIG);
//        L.monitor.d("%s updateAIConfig aimDetection: %s", TAG, aimDetection);
        if (!TextUtils.isEmpty(aimDetection)) {
            mAIConfig = new Gson().fromJson(aimDetection, AiDetectionConfig.class);
        } else {
            mAIConfig = new AiDetectionConfig();
        }
        L.monitor.i("%s AIConfig: %s", TAG, mAIConfig.toString());
        // 更新算法开关状态到UVC
        UVCCameraController.getInstance().setAlgoSwitch(UVC_ALGO_ENUM.ALGO_PERSON_DETECT, true);
        UVCCameraController.getInstance().setAlgoSwitch(UVC_ALGO_ENUM.ALGO_SCENE_CHANGED, true);
        UVCCameraController.getInstance().setAlgoSwitch(UVC_ALGO_ENUM.ALGO_CRY_DETECT, true);
        // 更新灵敏度到UVC
        UVCCameraController.getInstance().SetAlgoSensitivity(UVC_ALGO_ENUM.ALGO_SCENE_CHANGED, mAIConfig.getSensitivity());
    }

    // 更新云存开关配置
    public void updateCloudSwitch() {
        // 云存开关状态变化
        int cloudSwitch = Settings.Global.getInt(mContext.getContentResolver(), Constants.KEY_CLOUD_SWITCH_CONFIG, 0);
        L.monitor.d("%s updateCloudSwitch cloudSwitch: %d", TAG, cloudSwitch);
        if (mVipConfig.isVip() && System.currentTimeMillis() < mVipConfig.getEndTime()) { //开启或者关闭云存开关
            mCloudUploadSwitch = cloudSwitch == 1;
        } else { //更新Vip状态
            mCloudExecutor.execute(() -> mCloudVipSwitch.getDomainUrl());
        }
    }

    public void updateSystemUpdate() {
        mSystemUpdating = CommonApiUtils.isSystemUpdating(mContext);
        L.monitor.d("%s updateSystemUpdate SystemUpdating: %s", TAG, mSystemUpdating);
        if (mSystemUpdating) {
            stopRecord(true);
        }
    }

    // 删除一个小前无效云存视频文件
    private void delOldCloudFile() {
        mCloudExecutor.execute(() -> {
            long currentTime = System.currentTimeMillis() - ONE_HOUR;
            String cloudPath = FileUtils.getCloudRecordPath();
            List<String> files = FileUtils.getAllChildFileName(cloudPath);
            for (String file : files) {
                if (file.startsWith(Constants.ENCRYPT_PREFIX)) {
                    if (file.endsWith(Constants.MP4_PREFIX)) {
                        String mp4File = file.replace(Constants.ENCRYPT_PREFIX, "").replace(Constants.MP4_PREFIX, "");
                        if (Long.parseLong(mp4File) < currentTime) {
                            L.monitor.d("%s Delete filename: %s", TAG, file);
                            FileUtils.delFile(cloudPath + File.separator + file);
                        }
                    } else if(file.endsWith(Constants.JPEG_PREFIX)) {
                        String jpgFile = file.replace(Constants.ENCRYPT_PREFIX, "").replace(Constants.JPEG_PREFIX, "");
                        if (Long.parseLong(jpgFile) < currentTime) {
                            L.monitor.d("%s Delete filename: %s", TAG, file);
                            FileUtils.delFile(cloudPath + File.separator + file);
                        }
                    }
                } else {
                    try {
                        if (Long.parseLong(file) < currentTime) {
                            L.monitor.d("%s Delete filename: %s", TAG, file);
                            FileUtils.deleteDir(cloudPath + File.separator + file);
                        }
                    } catch (Exception e) {
                        // 解析异常,删除其它无关文件
                        L.monitor.e("%s Del %s error: %s", TAG, file, e.getMessage());
                        File errorfile = new File(cloudPath + File.separator + file);
                        if (errorfile.isDirectory()) {
                            FileUtils.deleteDir(cloudPath + File.separator + file);
                        } else {
                            FileUtils.delFile(cloudPath + File.separator + file);
                        }
                    }

                }
            }
        });
    }
}
