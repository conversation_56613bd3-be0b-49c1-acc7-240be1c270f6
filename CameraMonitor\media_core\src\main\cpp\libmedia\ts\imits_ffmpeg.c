#include <stdlib.h>
#include <string.h>
#ifndef WIN32
#include <pthread.h>
#else
#include "imimedia_win2linux.h"
#endif

#include "imimedia_common.h"
#include "imiffmpeg_common.h"
#include "imits_ffmpeg.h"
#include "imiparser_aac.h"

#if defined(WIN32) && !defined(__cplusplus)
#define inline __inline
#endif

#ifdef __cplusplus
extern "C"{
#endif
#include <libavformat/avformat.h>
#include <libavformat/avio.h>
#include <libavutil/mem.h>
#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "avformat.lib")
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#endif

#define MODULE_IMIMP4_FFMPEG "imitsffmpeg"

typedef struct imits_ffmpeg_info_s {
#ifndef WIN32
	pthread_mutex_t _lock_mutex;
#endif
	video_codec_id _video_codec;
	audio_codec_id _audio_codec;
	AVFormatContext* _format_context;
	AVStream* _video_stream;
	unsigned int _video_frame_index;
	unsigned int _video_fps;
	unsigned long long _video_lasttimestamp;
	unsigned int _video_lastpts;
	unsigned int _getfirstframe;
	AVStream* _audio_stream;
	unsigned long long _audio_lasttimestamp;
	unsigned int _audio_frame_index;
} imits_ffmpeg_info_s, *imits_ffmpeg_info_t;

void imits_init_ffmpeg()
{
	printf("imits_init_ffmpeg\n");
	av_register_all();
}

int _ts_create_file_inner(imits_ffmpeg_info_t handle,
	const char* path,
	container_format_id container,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate)
{
	const char* container_ext = NULL;
	int ret = IMIMEDIA_OK;
	if (handle == 0) return IMIMEDIA_PARAMS_ERROR;//ASSERT WARNING
	//////////////////////////////////////////////////////////////////////////
	handle->_format_context = avformat_alloc_context();
	if (handle->_format_context == NULL) {
		printf("imits_create_file avformat_alloc_context error\n");
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	switch (container)
	{
	case container_format_ts:
		{
			container_ext = "ts";
		}
		break;
	default:
		printf("imits_create_file container_format_id default = %d\n", container);
		return IMIMEDIA_PARAMS_ERROR;
	}
	handle->_format_context->oformat = av_guess_format(container_ext, path, NULL);
	if (handle->_format_context->oformat == NULL) {
		printf("imits_create_file av_guess_format error path = %s\n", path);
		goto error;
	}
	switch (video)
	{
	case video_codec_h264:
		handle->_format_context->oformat->video_codec = AV_CODEC_ID_H264;
		break;
	default:
		printf("imits_create_file video_codec_id default = %d\n", video);
		goto error;
	}
	switch (audio)
	{
	case audio_codec_aac:
		handle->_format_context->oformat->audio_codec = AV_CODEC_ID_AAC;
		break;
	default:
		printf("imits_create_file audio_codec_id default = %d\n", audio);
		goto error;
	}
	strncpy(handle->_format_context->filename, path, sizeof(handle->_format_context->filename));
	//////////////////////////////////////////////////////////////////////////
	handle->_video_stream = avformat_new_stream(handle->_format_context, NULL);
	if (handle->_video_stream == NULL) {
		printf("imits_create_file avformat_new_stream video error\n");
		goto error;
	}
	handle->_video_stream->id = handle->_format_context->nb_streams - 1;
	handle->_video_stream->codec->codec_type = AVMEDIA_TYPE_VIDEO;
	switch (video)
	{
	case video_codec_h264:
		handle->_video_stream->codec->codec_id = AV_CODEC_ID_H264;
		handle->_video_stream->codec->level = 0x7F;
		break;
	default:
		printf("imits_create_file video_codec_id default = %d\n", video);
		goto error;
	}
	handle->_video_stream->codec->width = vwidth;
	handle->_video_stream->codec->height = vheight;
	handle->_video_stream->codec->time_base = av_d2q(1.0/vfps, 255);
	handle->_video_stream->r_frame_rate = av_d2q(vfps, 255);
	handle->_video_stream->time_base.num = 1;
	handle->_video_stream->time_base.den = IMI_VIDEO_TIME_BASE;
	handle->_video_fps = vfps;
	//////////////////////////////////////////////////////////////////////////
	handle->_audio_stream = avformat_new_stream(handle->_format_context, NULL);
	if (handle->_audio_stream == NULL) {
		printf("imits_create_file avformat_new_stream audio error\n");
		goto error;
	}
	handle->_audio_stream->id = handle->_format_context->nb_streams - 1;
	handle->_audio_stream->codec->codec_type = AVMEDIA_TYPE_AUDIO;
	switch (audio)
	{
	case audio_codec_aac:
		{
			handle->_audio_stream->codec->codec_id = AV_CODEC_ID_AAC;
			handle->_audio_stream->codec->sample_rate = asamplerate;
			handle->_audio_stream->codec->channels = achannel;
			handle->_audio_stream->codec->profile = FF_PROFILE_AAC_LOW;
			handle->_audio_stream->codec->level = 0x02;
			handle->_audio_stream->codec->frame_size = IMI_AAC_TIME_BASE;
		}
		break;
	default:
		printf("imits_create_file audio_codec_id default = %d\n", audio);
		goto error;
	}
	handle->_audio_stream->codec->time_base.num = 1;
	handle->_audio_stream->codec->time_base.den = asamplerate;
	handle->_audio_stream->time_base.num = 1;
	handle->_audio_stream->time_base.den = asamplerate;
	//////////////////////////////////////////////////////////////////////////
	av_dump_format(handle->_format_context, 0, path, 1);
	ret = avio_open(&handle->_format_context->pb, path, AVIO_FLAG_WRITE);
	if (ret < 0) {
		ffmpeg_err2str("imits_create_file avio_open", ret);
		goto error;
	}
	//////////////////////////////////////////////////////////////////////////
	ret = avformat_write_header(handle->_format_context, NULL);
	if (ret < 0) {
		ffmpeg_err2str("imits_create_file avformat_write_header", ret);
		return IMIMEDIA_CAPABILITY_ERROR;
	}
	handle->_video_codec = video;
	handle->_audio_codec = audio;
	printf("imits_create_file success\n");
	return IMIMEDIA_OK;

error:
	if (handle->_video_stream) {
		avcodec_close(handle->_video_stream->codec);
		handle->_video_stream->codec->extradata = NULL;
		handle->_video_stream->codec->extradata_size = 0;
		handle->_video_stream = NULL;
	}
	if (handle->_audio_stream) {
		avcodec_close(handle->_audio_stream->codec);
		handle->_audio_stream->codec->extradata = NULL;
		handle->_audio_stream->codec->extradata_size = 0;
		handle->_audio_stream = NULL;
	}
	avio_close(handle->_format_context->pb);
	handle->_format_context->pb = NULL;
	avformat_free_context(handle->_format_context);
	handle->_format_context = NULL;
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imits_create_file(const char* path,
	container_format_id container,
	video_codec_id video,
	audio_codec_id audio,
	unsigned int vfps,
	unsigned int vwidth,
	unsigned int vheight,
	unsigned int achannel,
	unsigned int asamplerate,
	/*out*/imits_ffmpeg_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imits_ffmpeg_info_t handle_impl = NULL;
	handle_impl = (imits_ffmpeg_info_t)malloc(sizeof(imits_ffmpeg_info_s));
	if (handle_impl == NULL) {
		printf("imits_create_file malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imits_ffmpeg_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _ts_create_file_inner(handle_impl, path, container, video, audio, vfps, vwidth, vheight, achannel, asamplerate);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imits_create_file handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _ts_write_video_frame_inner(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype)
{
	unsigned int offset = 0;
	AVPacket pkt = { 0 };
	unsigned char* data_buff_src = (unsigned char*)data;
	if (handle->_video_stream == NULL) {
		printf("imits_write_video_frame video_stream is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	if (frametype != frame_type_i && handle->_getfirstframe == 0) {
		printf("imits_write_video_frame getfirstframe is not key frame\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	if (handle->_getfirstframe == 0) {
		handle->_getfirstframe = 1;
	}
	av_init_packet(&pkt);
	if (frametype == frame_type_i) {
		pkt.flags |= AV_PKT_FLAG_KEY;
	}
	pkt.data = (uint8_t*)data_buff_src + offset;
	pkt.size = data_len - offset;
	if (vtimestamp == 0) {
		pkt.pts = av_rescale(handle->_video_frame_index++,
			handle->_video_stream->time_base.den,
			handle->_video_stream->codec->time_base.den);
		pkt.duration = IMI_VIDEO_TIME_BASE/handle->_video_fps;
	} else {
		if (handle->_video_lasttimestamp == 0) {
			pkt.pts = av_rescale(handle->_video_frame_index++,
				handle->_video_stream->time_base.den,
				handle->_video_stream->codec->time_base.den);
			pkt.duration = IMI_VIDEO_TIME_BASE/handle->_video_fps;
		} else {
			int64_t time = IMI_PTS2TIME_SCALE(vtimestamp, handle->_video_lasttimestamp, IMI_VIDEO_TIME_BASE);
			if (time >= IMI_VIDEO_TIME_BASE) {
				time = IMI_VIDEO_TIME_BASE/handle->_video_fps;
			}
			pkt.pts = handle->_video_lastpts + time;
			pkt.duration = time;
		}
		handle->_video_lasttimestamp = vtimestamp;
		handle->_video_lastpts = (unsigned int)pkt.pts;
	}
	pkt.stream_index = handle->_video_stream->index;
	pkt.dts = pkt.pts;
	pkt.pos = -1;
	return av_interleaved_write_frame(handle->_format_context, &pkt);
}

int imits_write_video_frame(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long vtimestamp,
	frame_type_id frametype)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _ts_write_video_frame_inner(handle, data, data_len, vtimestamp, frametype);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _ts_write_audio_frame_inner(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp)
{
	AVPacket pkt = { 0 };
	unsigned char* data_buff_src = (unsigned char*)data;
	if (handle->_audio_stream == NULL) {
		printf("imips_write_audio_frame audio_stream is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	if (handle->_getfirstframe == 0) {
		printf("imits_write_audio_frame doesn't' get first keyframe\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	av_init_packet(&pkt);
	pkt.flags |= AV_PKT_FLAG_KEY;
	switch (handle->_audio_codec)
	{
	case audio_codec_aac:
		{
			pkt.data = (uint8_t*)data_buff_src;
			pkt.size = data_len;
			pkt.duration = (int)(11520/(handle->_audio_stream->codec->sample_rate/8000));
		}
		break;
	default:
		printf("imips_write_audio_frame audio_codec_id default = %d\n", handle->_audio_codec);
		return IMIMEDIA_PARAMS_ERROR;
	}
	pkt.stream_index = handle->_audio_stream->index;
	pkt.pts = pkt.duration*(handle->_audio_frame_index++);
	pkt.dts = pkt.pts;
	pkt.pos = -1;
	return av_interleaved_write_frame(handle->_format_context, &pkt);
}

int imits_write_audio_frame(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int data_len,
	unsigned long long atimestamp)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _ts_write_audio_frame_inner(handle, data, data_len, atimestamp);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _ts_close_file_for_create_inner(imits_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	ret = av_write_trailer(handle->_format_context);
	if (ret != 0 ) {
		ffmpeg_err2str("imits_close_file_for_create av_write_trailer", ret);
	}
	if (handle->_video_stream) {
		avcodec_close(handle->_video_stream->codec);
		handle->_video_stream->codec->extradata = NULL;
		handle->_video_stream->codec->extradata_size = 0;
		handle->_video_stream = NULL;
	}
	if (handle->_audio_stream) {
		avcodec_close(handle->_audio_stream->codec);
		handle->_audio_stream->codec->extradata = NULL;
		handle->_audio_stream->codec->extradata_size = 0;
		handle->_audio_stream = NULL;
	}
	avio_close(handle->_format_context->pb);
	handle->_format_context->pb = NULL;
	avformat_free_context(handle->_format_context);
	handle->_format_context = NULL;
	handle->_audio_lasttimestamp = 0;
	handle->_audio_frame_index = 0;
	handle->_video_frame_index = 0;
	handle->_video_fps = 0;
	handle->_video_lasttimestamp = 0;
	handle->_video_lastpts = 0;
	handle->_getfirstframe = 0;
	printf("imits_close_file_for_create success\n");
	return ret;
}

int imits_close_file_for_create(imits_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	printf("imits_close_file_for_create handle = %x\n", handle);
	ret = _ts_close_file_for_create_inner(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	pthread_mutex_destroy(&handle->_lock_mutex);
	free(handle);
	return ret;
}

int _ts_open_file_inner(imits_ffmpeg_info_t handle,
	const char* path,
	container_format_id* container,
	video_codec_id* video,
	audio_codec_id* audio,
	unsigned int* vfps,
	unsigned int* vwidth,
	unsigned int* vheight,
	unsigned int* achannel,
	unsigned int* asamplerate,
	unsigned int* abitrate,
	unsigned long long* duration)
{
	unsigned int i = 0;
	int ret = avformat_open_input(&handle->_format_context, path, NULL, NULL);
	if (ret != 0) {
		ffmpeg_err2str("imits_open_file avformat_open_input", ret);
		goto error;
	}
	ret = avformat_find_stream_info(handle->_format_context, NULL);
	if (ret < 0) {
		ffmpeg_err2str("imits_open_file avformat_find_stream_info", ret);
		goto error;
	}
	if (strstr((char*)handle->_format_context->iformat->extensions, "mp4") == NULL) {
		printf("imits_open_file extensions error = %s\n", (char*)handle->_format_context->iformat->extensions);
		goto error;
	}
	*container = container_format_mp4;
	for (i = 0; i < handle->_format_context->nb_streams; i++) {
		AVStream* stream = handle->_format_context->streams[i];
		switch (stream->codec->codec_type)
		{
		case AVMEDIA_TYPE_VIDEO:
			{
				switch (stream->codec->codec_id)
				{
				case AV_CODEC_ID_H264:
					{
						*video = video_codec_h264;
					}
					break;
				case AV_CODEC_ID_H265:
					{
						*video = video_codec_h265;
					}
					break;
				default:
					printf("imits_open_file video stream type default = %d\n", stream->codec->codec_id);
					goto error;
				}
				*vfps = stream->avg_frame_rate.num/stream->avg_frame_rate.den;
				*vwidth = stream->codec->width;
				*vheight = stream->codec->height;
			}
			break;
		case AVMEDIA_TYPE_AUDIO:
			{
				switch (stream->codec->codec_id)
				{
				case AV_CODEC_ID_AAC:
					{
						*audio = audio_codec_aac;
					}
					break;
				case AV_CODEC_ID_PCM_ALAW:
					{
						*audio = audio_codec_g711a;
					}
					break;
				default:
					printf("imits_open_file audio stream type default = %d\n", stream->codec->codec_id);
					goto error;
				}
				*achannel = stream->codec->channels;
				*asamplerate = stream->codec->sample_rate;
				*abitrate = (unsigned int)stream->codec->bit_rate;
			}
			break;
		default:
			continue;
		}
	}
	*duration = (unsigned long long)handle->_format_context->duration;
	printf("imits_open_file success\n");
	return IMIMEDIA_OK;

error:
	if (handle->_format_context) {
		avformat_close_input(&handle->_format_context);
		handle->_format_context = NULL;
	}
	return IMIMEDIA_CAPABILITY_ERROR;
}

int imits_open_file(const char* path,
	container_format_id* container,
	video_codec_id* video,
	audio_codec_id* audio,
	unsigned int* vfps,
	unsigned int* vwidth,
	unsigned int* vheight,
	unsigned int* achannel,
	unsigned int* asamplerate,
	unsigned int* abitrate,
	unsigned long long* duration,
	/*out*/imits_ffmpeg_info_t* handle)
{
	int ret = IMIMEDIA_OK;
	imits_ffmpeg_info_t handle_impl = NULL;
	handle_impl = (imits_ffmpeg_info_t)malloc(sizeof(imits_ffmpeg_info_s));
	if (handle_impl == NULL) {
		printf("imits_open_file malloc error\n");
		return IMIMEDIA_RESOURCE_ERROR;
	}
	memset(handle_impl, 0, sizeof(imits_ffmpeg_info_s));
	pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
	pthread_mutex_lock(&handle_impl->_lock_mutex);
	ret = _ts_open_file_inner(handle_impl, path, container, video, audio, vfps, vwidth, vheight, achannel, asamplerate, abitrate, duration);
	if (ret != 0) {
		pthread_mutex_unlock(&handle_impl->_lock_mutex);
		pthread_mutex_destroy(&handle_impl->_lock_mutex);
		free(handle_impl);
		*handle = NULL;
		return ret;
	}
	*handle = handle_impl;
	printf("imits_open_file handle = %x\n", *handle);
	pthread_mutex_unlock(&handle_impl->_lock_mutex);
	return ret;
}

int _ts_get_frame_inner(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype)
{
	int ret = IMIMEDIA_OK;
	int64_t time = 0;
	AVStream* stream = NULL;
	AVPacket pkt = { 0 };
	if (handle->_format_context == NULL) {
		printf("imits_get_video_frame format_context is null\n");
		return IMIMEDIA_STATUS_ERROR;
	}
	ret = av_read_frame(handle->_format_context, &pkt);
	if (ret < 0 || pkt.size == 0) {
		if (ret == AVERROR_EOF) {
			//this is correct error
			printf("imits_get_video_frame av_read_frame eof\n");
			return IMIMEDIA_EOF;
		} else {
			ffmpeg_err2str("imits_get_video_frame av_read_frame", ret);
			return ret;
		}
	}
	stream = handle->_format_context->streams[pkt.stream_index];
	switch (stream->codec->codec_type)
	{
	case AVMEDIA_TYPE_VIDEO:
		{
			if (pkt.flags & AV_PKT_FLAG_KEY) {
				*frametype = frame_type_i;
			} else {
				*frametype = frame_type_p;
			}
			*data_len = pkt.size;
			memcpy(data, (unsigned char*)pkt.data, pkt.size);
		}
		break;
	case AVMEDIA_TYPE_AUDIO:
		{
			*frametype = frame_type_audio;
			if (stream->codec->codec_id == AV_CODEC_ID_AAC) {
				unsigned char* aac_header = imi_make_aac_header_net(stream->codec->sample_rate, stream->codec->channels, pkt.size+AAC_ADTS_HEADER);
				memcpy(data, aac_header, AAC_ADTS_HEADER);
				free(aac_header);
				*data_len = pkt.size+AAC_ADTS_HEADER;
				memcpy(data+AAC_ADTS_HEADER, (unsigned char*)pkt.data, pkt.size);
			} else {
				*data_len = pkt.size;
				memcpy(data, (unsigned char*)pkt.data, pkt.size);
			}
		}
		break;
	default:
		break;
	}
	*timestamp = (unsigned long long)(((double)pkt.pts / (double)stream->time_base.den) * 1000);
	av_packet_unref(&pkt);
	return 0;
}

int imits_get_frame(imits_ffmpeg_info_t handle,
	unsigned char* data,
	unsigned int* data_len,
	unsigned long long* timestamp,
	frame_type_id* frametype)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _ts_get_frame_inner(handle, data, data_len, timestamp, frametype);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _ts_seek_file_inner(imits_ffmpeg_info_t handle, unsigned long long timestamp)
{
	int ret = IMIMEDIA_OK;
	int64_t time = 0;
	printf("imits_seek_file av_seek_frame timestamp = %lld\n", timestamp);
	time = (int64_t)(((double)(timestamp)/(double)1000)*AV_TIME_BASE + (double)handle->_format_context->start_time);
	ret = av_seek_frame(handle->_format_context, -1, time, AVSEEK_FLAG_BACKWARD);//AVSEEK_FLAG_BACKWARD
	if (ret < 0) {
		ffmpeg_err2str("imits_seek_file av_seek_frame", ret);
		return ret;
	}
	return IMIMEDIA_OK;
}

int imits_seek_file(imits_ffmpeg_info_t handle, unsigned long long timestamp)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	ret = _ts_seek_file_inner(handle, timestamp);
	pthread_mutex_unlock(&handle->_lock_mutex);
	return ret;
}

int _ts_close_file_for_open_inner(imits_ffmpeg_info_t handle)
{
	if (handle->_format_context) {
		avformat_close_input(&handle->_format_context);
		handle->_format_context = NULL;
	}
	printf("imits_close_file_for_open success\n");
	return 0;
}

int imits_close_file_for_open(imits_ffmpeg_info_t handle)
{
	int ret = IMIMEDIA_OK;
	pthread_mutex_lock(&handle->_lock_mutex);
	printf("imits_close_file_for_open handle = %x\n", handle);
	ret = _ts_close_file_for_open_inner(handle);
	pthread_mutex_unlock(&handle->_lock_mutex);
	pthread_mutex_destroy(&handle->_lock_mutex);
	free(handle);
	return ret;
}