#include <stdlib.h>
#include <string.h>
#ifndef WIN32
#include <pthread.h>
#include <../log.h>
#else
#include "imimedia_win2linux.h"
#endif

#include "imiffmpeg_common.h"
#include "imidecoder_ffmpeg.h"

#if defined(WIN32) && !defined(__cplusplus)
#define inline __inline
#endif

#ifdef __cplusplus
extern "C"{
#endif
#include <libavcodec/avcodec.h>
#include <libswscale/swscale.h>
#include <libswresample/swresample.h>
#ifdef __cplusplus
}
#endif

#ifdef WIN32
//ffmpeg lib windows
#pragma comment(lib, "avcodec.lib")
#pragma comment(lib, "avutil.lib")
#pragma comment(lib, "swresample.lib")
#endif

typedef struct imidecoder_ffmpeg_info_s {
#ifndef WIN32
    pthread_mutex_t _lock_mutex;
#endif
    AVCodecContext* _avcodec;
    AVFrame* _avframe;
} imidecoder_ffmpeg_info_s, *imidecoder_ffmpeg_info_t;

AVCodec* _decoder_get_video_codec(video_codec_id codec) {
    AVCodec* avcodec = NULL;
    switch (codec)
    {
        case video_codec_h264:
            avcodec = avcodec_find_decoder(AV_CODEC_ID_H264);
            break;
        case video_codec_h265:
            avcodec = avcodec_find_decoder(AV_CODEC_ID_H265);
            break;
        default:
            break;
    }
    return avcodec;
}

int _decoder_video_init(imidecoder_ffmpeg_info_t handle,
                        video_codec_id codec,
                        pixel_format_id format,
                        int width,
                        int height)
{
    int ret = IMIMEDIA_OK;
    AVCodec* avcodec;
    avcodec = _decoder_get_video_codec(codec);
    if (avcodec == NULL) {
        printf("avcodec_find_decoder video codec error %d\n", codec);
        return IMIMEDIA_UNINSTALL_ERROR;
    }
    handle->_avcodec = avcodec_alloc_context3(avcodec);
    if (handle->_avcodec == NULL) {
        printf("avcodec_alloc_context3 video error\n");
        return IMIMEDIA_UNINSTALL_ERROR;
    }
    handle->_avcodec->codec_type = AVMEDIA_TYPE_VIDEO;
    handle->_avcodec->pix_fmt = pixel_format2ffmpeg(format);
    handle->_avcodec->thread_count = 4;
    handle->_avcodec->frame_number = 1;
    handle->_avcodec->framerate.num = 0;
    handle->_avcodec->framerate.den = 1;
    handle->_avcodec->width = width;
    handle->_avcodec->height = height;
    ret = avcodec_open2(handle->_avcodec, avcodec, NULL);
    if (ret == 0) {
        handle->_avframe = av_frame_alloc();
    } else {
        ffmpeg_err2str("imidecoder_open avcodec_open2 video", ret);
        av_free(handle->_avcodec);
        handle->_avcodec =  NULL;
        return ret;
    }
    return IMIMEDIA_OK;
}

AVCodec* _decoder_get_audio_codec(audio_codec_id codec) {
    AVCodec* avcodec = NULL;
    switch (codec)
    {
        case audio_codec_aac:
            avcodec = avcodec_find_decoder(AV_CODEC_ID_AAC);
            break;
        case audio_codec_g711a:
            avcodec = avcodec_find_decoder(AV_CODEC_ID_PCM_ALAW);
            break;
        case audio_codec_g711u:
            avcodec = avcodec_find_decoder(AV_CODEC_ID_PCM_MULAW);
            break;
        default:
            break;
    }
    return avcodec;
}

int _decoder_audio_init(imidecoder_ffmpeg_info_t handle,
                        audio_codec_id codec,
                        sample_format_id format,
                        int sample_rate,
                        int channels)
{
    int ret = IMIMEDIA_OK;
    AVCodec* avcodec;
    avcodec = _decoder_get_audio_codec(codec);
    if (avcodec == NULL) {
        printf("avcodec_find_decoder audio codec error %d\n", codec);
        return IMIMEDIA_UNINSTALL_ERROR;
    }
    handle->_avcodec = avcodec_alloc_context3(avcodec);
    if (handle->_avcodec == NULL) {
        printf("avcodec_alloc_context3 audio error\n");
        return IMIMEDIA_UNINSTALL_ERROR;
    }
    handle->_avcodec->codec_type = AVMEDIA_TYPE_AUDIO;
    handle->_avcodec->sample_fmt = sample_format2ffmpeg(format);
    handle->_avcodec->sample_rate = sample_rate;
    handle->_avcodec->channels = channels;
    handle->_avcodec->channel_layout = av_get_default_channel_layout(channels);
    ret = avcodec_open2(handle->_avcodec, avcodec, NULL);
    if (ret == 0) {
        handle->_avframe = av_frame_alloc();
        handle->_avframe->sample_rate = sample_rate;
        handle->_avframe->channels = channels;
        handle->_avframe->channel_layout = av_get_default_channel_layout(channels);
    } else {
        ffmpeg_err2str("imidecoder_open avcodec_open2 audio", ret);
        av_free(handle->_avcodec);
        handle->_avcodec =  NULL;
        return ret;
    }
    return IMIMEDIA_OK;
}

int _decoder_open_inner(imidecoder_ffmpeg_info_t handle,
                        imidecoder_codec_info *info)
{
    int ret = IMIMEDIA_OK;
    int codec = info->codec;
    if (codec < AUDIO_CODEC_INDEX) {
        ret = _decoder_video_init(handle,
                                  (video_codec_id)codec,
                                  (pixel_format_id)info->format,
                                  (int)info->width,
                                  (int)info->height);
    } else {
        ret = _decoder_audio_init(handle,
                                  (audio_codec_id)codec,
                                  (sample_format_id)info->format,
                                  (int)info->sample_rate,
                                  (int)info->channels);
    }
    return ret;
}

int imidecoder_open(imidecoder_codec_info *info,
        /*out*/imidecoder_ffmpeg_info_t *handle)
{
    int ret = IMIMEDIA_OK;
    imidecoder_ffmpeg_info_t handle_impl = NULL;
    handle_impl = (imidecoder_ffmpeg_info_t)malloc(sizeof(imidecoder_ffmpeg_info_s));
    if (handle_impl == NULL) {
        printf("imidecoder_open malloc error\n");
        return IMIMEDIA_RESOURCE_ERROR;
    }
    memset(handle_impl, 0, sizeof(imidecoder_ffmpeg_info_s));
    pthread_mutex_init(&handle_impl->_lock_mutex, NULL);
    pthread_mutex_lock(&handle_impl->_lock_mutex);
    ret = _decoder_open_inner(handle_impl, info);
    if (ret != 0) {
        pthread_mutex_unlock(&handle_impl->_lock_mutex);
        pthread_mutex_destroy(&handle_impl->_lock_mutex);
        free(handle_impl);
        *handle = NULL;
        return ret;
    }
    *handle = handle_impl;
    printf("imidecoder_open handle = %x\n", *handle);
    pthread_mutex_unlock(&handle_impl->_lock_mutex);
    return ret;
}

int _method_yv12(AVFrame *avframe,
                 unsigned char* dispframe)
{
    unsigned int i = 0;
    unsigned char *ybuff = avframe->data[0];
    unsigned int ylen = avframe->width * avframe->height;
    unsigned char *ubuff = avframe->data[2];
    unsigned int ulen = avframe->width/2 * avframe->height/2;
    unsigned char *vbuff = avframe->data[1];
    unsigned int vlen = avframe->width/2 * avframe->height/2;
    memcpy(dispframe, ybuff, ylen);
    dispframe += ylen;
    memcpy(dispframe, ubuff, ulen);
    dispframe += ulen;
    memcpy(dispframe, vbuff, vlen);
    dispframe += vlen;
    return (ylen + ulen + vlen);
}

int _method_nv12(AVFrame *avframe,
                 unsigned char* dispframe)
{
    unsigned int i = 0;
    unsigned char *ybuff = avframe->data[0];
    unsigned int ylen = avframe->width * avframe->height;
    unsigned char *uvbuff = avframe->data[1];
    unsigned int uvlen = (avframe->width * avframe->height)/2;
    memcpy(dispframe, ybuff, ylen);
    dispframe += ylen;
    memcpy(dispframe, uvbuff, uvlen);
    dispframe += uvlen;
    return (ylen + uvlen);
}

int _method_rgb(AVFrame *avframe,
                unsigned char* dispframe)
{
    unsigned char *rgbbuff = avframe->data[0];
    unsigned int rgblen = avframe->linesize[0];
    memcpy(dispframe, rgbbuff, rgblen);
    return rgblen;
}

int _method_audio(AVFrame *avframe,
                  unsigned char* dispframe)
{
    memcpy(dispframe, avframe->data, avframe->linesize[0]);
    return avframe->linesize[0];
}

int _decoder_frame(imidecoder_ffmpeg_info_t handle,
                   endecoder_frame_info *frame)
{
    int ret, len, got_frame_ptr = 0;
    AVPacket avpacket = { 0 };
    if (handle->_avcodec == NULL || handle->_avframe == NULL) {
        printf("imidecoder_one_frame null error\n");
        return IMIMEDIA_STATUS_ERROR;
    }
    len = (int)frame->enc_data_len;
    av_new_packet(&avpacket, len);
    memcpy((void *)avpacket.data, frame->enc_data, len);
    avpacket.size = len;
    if (handle->_avcodec->codec_type == AVMEDIA_TYPE_VIDEO) {
        ret = avcodec_send_packet(handle->_avcodec, &avpacket);
        if (ret < 0) {
            ffmpeg_err2str("imidecoder_one_frame avcodec_send_packet", ret);
            av_packet_unref(&avpacket);
            return IMIMEDIA_DECODE_ERROR;
        }

        while (ret >= 0) {
            ret = avcodec_receive_frame(handle->_avcodec, handle->_avframe);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                break;
            } else if (ret < 0) {
                ffmpeg_err2str("imidecoder_one_frame avcodec_receive_frame", ret);
                av_packet_unref(&avpacket);
                return IMIMEDIA_DECODE_ERROR;
            }

            switch (handle->_avcodec->pix_fmt) {
                case AV_PIX_FMT_YUVJ420P:
                case AV_PIX_FMT_YUV420P:
                    frame->raw_data_len = _method_yv12(handle->_avframe, frame->raw_data);
                    break;
                case AV_PIX_FMT_NV12:
                    frame->raw_data_len = _method_nv12(handle->_avframe, frame->raw_data);
                    break;
                case AV_PIX_FMT_RGB24:
                case AV_PIX_FMT_RGB32:
                    frame->raw_data_len = _method_rgb(handle->_avframe, frame->raw_data);
                    break;
                default:
                    av_packet_unref(&avpacket);
                    return IMIMEDIA_PARAMS_ERROR;
            }
            frame->pixel = ffmpeg2pixel_format(handle->_avcodec->pix_fmt);
            frame->sample = sample_format_unknown;
            av_packet_unref(&avpacket);
            break;
        }
    } else if (handle->_avcodec->codec_type == AVMEDIA_TYPE_AUDIO) {
        int ret = IMIMEDIA_OK;
        SwrContext* swrcontext = NULL;
        ret = avcodec_send_packet(handle->_avcodec, &avpacket);
        if (ret < 0) {
            ffmpeg_err2str("imidecoder_one_frame avcodec_send_packet", ret);
            av_packet_unref(&avpacket);
            return IMIMEDIA_DECODE_ERROR;
        }

        while (ret >= 0) {
            ret = avcodec_receive_frame(handle->_avcodec, handle->_avframe);
            if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF) {
                break;
            } else if (ret < 0) {
                ffmpeg_err2str("imidecoder_one_frame avcodec_receive_frame", ret);
                av_packet_unref(&avpacket);
                return IMIMEDIA_DECODE_ERROR;
            }

            swrcontext = swr_alloc_set_opts(swrcontext,
                                            handle->_avframe->channel_layout, AV_SAMPLE_FMT_S16, handle->_avframe->sample_rate,
                                            handle->_avframe->channel_layout, (enum AVSampleFormat)handle->_avframe->format, handle->_avframe->sample_rate,
                                            0, NULL);
            if (swrcontext == NULL) {
                printf("imidecoder_one_frame swr_alloc_set_opts error\n");
                av_packet_unref(&avpacket);
                return IMIMEDIA_PARAMS_ERROR;
            }
            ret = swr_init(swrcontext);
            if (ret < 0) {
                swr_free(&swrcontext);
                printf("imidecoder_one_frame swr_init error\n");
                av_packet_unref(&avpacket);
                return IMIMEDIA_CAPABILITY_ERROR;
            }
            frame->raw_data_len = av_samples_get_buffer_size(NULL, handle->_avframe->channels, handle->_avframe->nb_samples, AV_SAMPLE_FMT_S16, 1);
            len = swr_convert(swrcontext, &frame->raw_data, frame->raw_data_len, (const uint8_t**)handle->_avframe->data, handle->_avframe->nb_samples);
            swr_free(&swrcontext);
            if (len <= 0) {
                ffmpeg_err2str("imidecoder_open swr_convert audio", len);
                av_packet_unref(&avpacket);
                return IMIMEDIA_FORMAT_ERROR;
            }
            frame->sample = ffmpeg2sample_format(AV_SAMPLE_FMT_S16);
            frame->pixel = pixel_format_unknown;
            av_packet_unref(&avpacket);
            break;
        }
    } else {
        av_packet_unref(&avpacket);
        printf("imidecoder_one_frame codec_type error %d\n", handle->_avcodec->codec_type);
        return IMIMEDIA_UNKNOWN_ERROR;
    }
    return IMIMEDIA_OK;
}

int imidecoder_one_frame(imidecoder_ffmpeg_info_t handle,
                         endecoder_frame_info *frame)
{
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    ret = _decoder_frame(handle, frame);
    pthread_mutex_unlock(&handle->_lock_mutex);
    return ret;
}

int _decoder_close_inner(imidecoder_ffmpeg_info_t handle) {
    if (handle->_avcodec) {
        avcodec_close(handle->_avcodec);
        av_free(handle->_avcodec);
        handle->_avcodec =  NULL;
    }
    if (handle->_avframe) {
        av_free(handle->_avframe);
        handle->_avframe = NULL;
    }
    return IMIMEDIA_OK;
}

int imidecoder_close(imidecoder_ffmpeg_info_t handle)
{
    int ret = IMIMEDIA_OK;
    pthread_mutex_lock(&handle->_lock_mutex);
    printf("imidecoder_close handle = %x\n", handle);
    ret = _decoder_close_inner(handle);
    pthread_mutex_unlock(&handle->_lock_mutex);
    pthread_mutex_destroy(&handle->_lock_mutex);
    free(handle);
    return ret;
}
