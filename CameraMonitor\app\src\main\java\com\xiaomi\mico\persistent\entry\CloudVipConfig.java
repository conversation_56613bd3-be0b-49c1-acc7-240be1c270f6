package com.xiaomi.mico.persistent.entry;

/**
 * 云存文档介绍
 * https://zlxlqmxdqa.feishu.cn/wiki/XUcowWLptioIBakwtlwc3jQlnFd
 */

public class CloudVipConfig {

    private boolean vip = false;
    private long startTime;
    private long endTime;
    private boolean closeWindow = false;
    private long freeHomeSurExpireTime = -1;

    public CloudVipConfig() {
    }

    public CloudVipConfig(boolean vip, long startTime, long endTime, boolean closeWindow, long freeHomeSurExpireTime) {
        this.vip = vip;
        this.startTime = startTime;
        this.endTime = endTime;
        this.closeWindow = closeWindow;
        this.freeHomeSurExpireTime = freeHomeSurExpireTime;
    }

    public boolean isVip() {
        return vip;
    }

    public void setVip(boolean vip) {
        this.vip = vip;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getEndTime() {
        return endTime;
    }

    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }

    public boolean isCloseWindow() {
        return closeWindow;
    }

    public void setCloseWindow(boolean closeWindow) {
        this.closeWindow = closeWindow;
    }

    public long getFreeHomeSurExpireTime() {
        return freeHomeSurExpireTime;
    }

    public void setFreeHomeSurExpireTime(int freeHomeSurExpireTime) {
        this.freeHomeSurExpireTime = freeHomeSurExpireTime;
    }

    @Override
    public String toString() {
        return "CloudVipConfig{" +
                "vip=" + vip +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", closeWindow=" + closeWindow +
                ", freeHomeSurExpireTime=" + freeHomeSurExpireTime +
                '}';
    }
}
