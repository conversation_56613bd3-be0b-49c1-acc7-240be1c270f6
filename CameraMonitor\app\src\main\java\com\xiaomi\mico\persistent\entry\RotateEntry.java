package com.xiaomi.mico.persistent.entry;

public class RotateEntry {
    private int angle;
    private int elevation;

    public RotateEntry(int angle, int elevation) {
        this.angle = angle;
        this.elevation = elevation;
    }

    public int getAngle() {
        return angle;
    }

    public void setAngle(int angle) {
        this.angle = angle;
    }

    public int getElevation() {
        return elevation;
    }

    public void setElevation(int elevation) {
        this.elevation = elevation;
    }
}
