package com.xiaomi.camera.monitoring;

import android.media.MediaFormat;

import java.io.File;

public class Constants {
    public static final String TAG = "CameraMonitor";

    public static final boolean DEBUG = new File("/sdcard/mico_debug").exists();
    public static final boolean DELETE_CLOUD_FILE = true;
    public static final boolean SAVE_377_FILE = false;

    public static final String IPCAMERA_SERVICE_NAME = "ipcamera";

    public static final String CAMERA_ENCODE_TYPE = MediaFormat.MIMETYPE_VIDEO_HEVC;
    public static final int CAMERA0_FRAME_SIZE_WIDTH = 2960; //2960
    public static final int CAMERA0_FRAME_SIZE_HEIGHT = 1666; //1666

    public static final int CAMERA1_FRAME_SIZE_WIDTH = 864;
    public static final int CAMERA1_FRAME_SIZE_HEIGHT = 480;

    public static final int CLOUD_IMAGE_WIDTH = CAMERA1_FRAME_SIZE_WIDTH;
    public static final int CLOUD_IMAGE_HEIGHT = CAMERA1_FRAME_SIZE_HEIGHT;
    public static final int CLOUD_IMAGE_QUALITY = 80;

    public static final int CAMERA_PREVIEW_WIDTH = 1280;
    public static final int CAMERA_PREVIEW_HEIGHT = 720;
    public static final int CAMERA_VOIP_WIDTH = 640;
    public static final int CAMERA_VOIP_HEIGHT = 480;
    public static String OT_TOKEN = "";

    public static final int MAIN_STREAM = 0;
    public static final int SUB_STREAM = 1;

    public static String JPEG_PREFIX = ".jpg";
    public static String MP4_PREFIX = ".mp4";
    public static String ENCRYPT_PREFIX = "encrypt-";

    public static String PARAGRAPH_SPACE_FLAG = "\\r";

    public static long BOOT_TIME_GAP = 0;

    // 移动侦测
    public static final String KEY_MOTION_DETECTION_CONFIG = "mico_motion_detection_config";
    // AI检测
    public static final String KEY_AI_DETECTION_CONFIG = "mico_ai_detection_config";
    // 摄像机控制
    public static final String KEY_CAMERA_CONTROL_CONFIG = "mico_camera_control_config";
    // 自定义功能
    public static final String KEY_OTHER_FUNCTION_CONFIG = "mico_other_function_config";
    // 家人守护功能
    public static final String KEY_FAMILY_GUARD_CONFIG = "mico_family_guard_config";
    // 云存配置开关
    public static final String KEY_CLOUD_SWITCH_CONFIG = "mico_cloud_switch_config";
    // 系统是否在升级中
    public static final String KEY_SYSTEM_UPDATE = "mico_system_update";
    // 377软件版本号
    public static final String KEY_UVC_VERSION_UPDATE = "mico_uvc_version_update";
    public static final String KEY_UVC_VERSION = "mico_uvc_version";
    // 背向休眠未完成，校准未完成，上电后继续执行
    public static final String KEY_CAMERA_ROTATE_DONE = "mico_camera_rotate_done";
    /*
    高负载场景小爱唤醒策略: 考虑非VIP的云存录制两次之间最短间隔只有3分钟，可能存在反复给用户关闭和打开唤醒的情况，
    频繁提示非常打扰用户，所以toast提示逻辑设计为：一次toast提醒后，后续30分钟内不再toast
     */
    public static final String KEY_SOUND_BOX_STOP_TIME = "mico_sound_box_stop_time";
    // 停止小爱唤醒算法
    public static final int SOUND_BOX_ALGO_OPEN = 0;
    public static final int SOUND_BOX_ALGO_CLOSE = 1;
    public static final String KEY_SOUND_BOX_STOP = "mico_force_disable_wakeup";
    // voip通话设置PackageName
    public static final String VOIP_RUNNING_PKG = "voip_running_pkg";
    // 当前Resume activity
    public static final String GLOBAL_TOP_RESUMED_ACTIVITY = "GLOBAL_TOP_RESUMED_ACTIVITY";
    public static final String CLOUD_RUNNING = "mico_cloud_running";
}
