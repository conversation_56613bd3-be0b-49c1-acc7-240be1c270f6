#include <jni.h>
#include <string>
#include "log.h"

#ifdef __cplusplus
extern "C" {
#endif
#include "imimp4_ffmpeg.h"
#include "imidecoder_ffmpeg.h"

frame_type_id get_frame_type_id(jint frame_type) {
    switch (frame_type) {
        case 0x01:
            return frame_type_i;
        case 0x02:
            return frame_type_p;
        case 0x03:
            return frame_type_b;
        case 0x04:
            return frame_type_audio;
        default:
            return frame_type_i;
    }
}

pixel_format_id get_pixel_format_id(jint pixel) {
    switch (pixel) {
        case 0:
            return pixel_format_yv12;
        case 1:
            return pixel_format_nv12;
        case 2:
            return pixel_format_rgb24;
        case 3:
            return pixel_format_bgr24;
        case 4:
            return pixel_format_rgb32;
        case 5:
            return pixel_format_bgr32;
        default:
            return pixel_format_unknown;
    }
}

JNIEXPORT jlong JNICALL
Java_com_argusak_media_core_AkDecodeFrame_initDecoder(JNIEnv *env, jclass clazz, jint codec,
                                                      jint format, jint width, jint height) {
    auto handle_ = (imidecoder_ffmpeg_info_t) 0;
    imidecoder_codec_info info = {0};
    info.codec = codec;
    info.format = format;
    info.width = width;
    info.height = height;
    int ret = imidecoder_open(&info, &handle_);
    LOGD("imidecoder_open %d", ret);
    return reinterpret_cast<jlong>(handle_);
}

JNIEXPORT jint JNICALL
Java_com_argusak_media_core_AkDecodeFrame_decodeFrame(JNIEnv *env, jclass clazz, jlong handle,
                                                      jbyteArray input_data, jint input_len,
                                                      jbyteArray out, jint out_len) {
    auto handle_info = (imidecoder_ffmpeg_info_t)handle;
    endecoder_frame_info frame = {0};
    frame.enc_data = (unsigned char *)env->GetByteArrayElements(input_data, nullptr);
    frame.enc_data_len = input_len;
    frame.frame_type = frame_type_i;
    frame.raw_data = (unsigned char *)env->GetByteArrayElements(out, nullptr);
    frame.raw_data_len = out_len;
    frame.pixel = pixel_format_nv12;
    frame.sample = sample_format_s16;
    int ret = imidecoder_one_frame(handle_info, &frame);
    return ret;
}

JNIEXPORT jint JNICALL
Java_com_argusak_media_core_AkDecodeFrame_closeDecoder(JNIEnv *env, jclass clazz, jlong handle) {
    auto handle_info = (imidecoder_ffmpeg_info_t)handle;
    int ret = imidecoder_close(handle_info);
    return ret;
}

#ifdef __cplusplus
}
#endif