plugins {
    id 'com.android.library'
}

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.3"

    defaultConfig {
        minSdkVersion 28
        targetSdkVersion 30
        versionCode 1
        versionName "1.0.0"

        sourceSets {
            main {
                jniLibs.srcDirs = ['jinLibs']
            }
        }

        externalNativeBuild {
            cmake {
                cppFlags("")
                abiFilters("armeabi-v7a"/*, "arm64-v8a"*/)
            }
        }
    }
    externalNativeBuild {
        cmake {
            path "src/CMakeLists.txt"
        }
    }
}

dependencies {
}
