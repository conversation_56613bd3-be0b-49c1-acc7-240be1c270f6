package com.xiaomi.mico.persistent.monitor;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.database.ContentObserver;
import android.media.AudioManager;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.provider.Settings;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.AudioTrackManager;
import com.xiaomi.camera.monitoring.entity.VideoFrameData;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.VoipEntry;
import com.xiaomi.mico.persistent.func.CameraRotateManager;
import com.xiaomi.camera.monitoring.entity.MissSession;
import com.xiaomi.camera.monitoring.miss.MissClient;
import com.xiaomi.camera.monitoring.miss.MissConstants;
import com.xiaomi.camera.monitoring.miss.MissPorting;
import com.xiaomi.mico.persistent.utils.VDecUtils;
import com.xiaomi.mico.persistent.voip.MiVoipUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

abstract class BaseMissPorting implements MissPorting {
    public static final String CAMERA_MONITOR_RUNNING = "mi_camera_monitor_running";
    private final String CAMERA_COVER_KEY = "SW_CAMERA_LENS_COVER";
    private final String CAMERA_DISABLE_KEY = "mi_camera_disable";
    private final int CAMERA_DISABLED_VALUE = 1;

    protected Context mContext;
    protected MissClient mMissClient;
    protected ExecutorService mExecutorService;
    protected List<MissSession> mSessionList;
    protected boolean mCameraDisabled = false;

    private static final int MAX_CACHE_SIZE = 180;//最大生产存储缓存
    private final LinkedBlockingQueue<VideoFrameData> mVideoQueue = new LinkedBlockingQueue<>(MAX_CACHE_SIZE); //存放生产出的数据
    protected int currentSpeaker = -1;
    protected boolean isTalking = false;
    protected boolean isSpeaker = false;
    protected boolean mPowerStatus = true;

    private static final long TIME_HANDLE_HANG_UP_DELAY = 15 * 1000;
    private static final int MSG_HANDLE_HANG_UP = 0x1001;
    private Handler.Callback mCallback = msg -> {
        int what = msg.what;
        switch (what) {
            case MSG_HANDLE_HANG_UP:
                MiVoipUtil.finishVoipActivity(mContext);
                break;
        }
        return false;
    };
    protected Handler mHandler = new Handler(Looper.getMainLooper(), mCallback);

    protected BaseMissPorting() {
        mExecutorService = Executors.newSingleThreadExecutor();
        mSessionList = Collections.synchronizedList(new ArrayList<>());
//        CameraRotateManager.getInstance().setRotateCallback(mRotateCallback);
    }

    public LinkedBlockingQueue<VideoFrameData> getVideoQueue() {
        return mVideoQueue;
    }

    @Override
    public int cb_miss_ack_send(byte[] buf, int length) {
        L.monitor.d("cb_miss_ack_send: %d", length);
        return 0;
    }

    @Override
    public int cb_miss_on_connect(int session) {
        L.monitor.d("cb_miss_on_connect: %d", session);
        if (mContext != null && mSessionList.isEmpty()) {
            mCameraDisabled = Settings.System.getInt(mContext.getContentResolver(), CAMERA_DISABLE_KEY, 0) == CAMERA_DISABLED_VALUE;
            Uri uriDisable = Settings.System.getUriFor(CAMERA_DISABLE_KEY);
            mContext.getContentResolver().registerContentObserver(uriDisable, false, mCameraObserver);

            IntentFilter filter = new IntentFilter();
            filter.addAction(AudioManager.ACTION_MICROPHONE_MUTE_CHANGED);
            mContext.registerReceiver(mMicrophoneMuteReceiver, filter);
        }
        mSessionList.add(new MissSession(session));
        return mSessionList.size();
    }

    @Override
    public void cb_miss_on_cmd(int session, int cmd, String params, int length, byte[] user_data) {
        L.monitor.d("cb_miss_on_cmd: %d, %s, %d, %d", cmd, params, length, session);
        if (getMissSession(session) == null) {
            L.monitor.i("session is invalid and does not need to be processed.");
            return;
        }
        switch (cmd) {
            case MissConstants.MISS_CMD_STREAM_CTRL_REQ:
                mMissClient.MissCleanBuffer(session);
                getMissSession(session).setVideoQuality(params);
                break;
            case MissConstants.MISS_CMD_MOTOR_REQ:
                CameraRotateManager.getInstance().missRotateData(params);
                break;
            case MissConstants.MISS_CMD_SPEAKER_START_REQ:
                if (isSpeaker) {
                    mMissClient.MissCmdSendOne(session, MissConstants.MISS_CMD_SPEAKER_START_RESP, null, 0, -1);
                    return;
                } else {
                    mMissClient.MissCmdSendOne(session, MissConstants.MISS_CMD_SPEAKER_START_RESP, null, 0, 0);
                }
                isSpeaker = true;
                currentSpeaker = session;
                AudioTrackManager.getInstance().startPlay();
                break;
            case MissConstants.MISS_CMD_SPEAKER_STOP://结束对讲
                if (currentSpeaker == session) {
                    isSpeaker = false;
                    AudioTrackManager.getInstance().stopPlay();
                }
                break;
            case MissConstants.MISS_CMD_VIDEOCALL_START_REQ:
                if (mHandler.hasMessages(MSG_HANDLE_HANG_UP)) {
                    mHandler.removeMessages(MSG_HANDLE_HANG_UP);
                }
                if (isTalking) {
                    mMissClient.MissCmdSendOne(session, MissConstants.MISS_CMD_VIDEOCALL_START_RESP, null, 0, -1);
                    return;
                } else {
                    mVideoQueue.clear();
                    mMissClient.MissCmdSendOne(session, MissConstants.MISS_CMD_VIDEOCALL_START_RESP, null, 0, 0);
                }
                MonitorNotificationManager.getInstance().cancelMonitorNotification(mContext.getApplicationContext());
                isTalking = true;
                if (!VDecUtils.PACKAGE_NAME_MONITOR_CLASS.equals(VDecUtils.getClassName(mContext))) {
                    MiVoipUtil.startVoipActivity(mContext, params);
                }
                break;
            case MissConstants.MISS_CMD_VIDEOCALL_STOP:
                if (currentSpeaker == session) {
                    isTalking = false;
                    mVideoQueue.clear();
                    mHandler.post(() -> {
                        MiVoipUtil.finishVoipActivity(mContext);
                    });
                    // 挂断电话，如果当前有别的用户在看监控，恢复通知栏
                    if (mSessionList.size() > 1) {
                        MonitorNotificationManager.getInstance().notifyMonitorNotification(mContext.getApplicationContext());
                    }
                }
                break;
            case MissConstants.MISS_CMD_CLIENT_INFO:
                VoipEntry mVoipEntry = new Gson().fromJson(params, VoipEntry.class);
                L.monitor.d("mVoipEntry: %s", mVoipEntry);
                break;
            case MissConstants.MISS_CMD_OPEN_CAMERA_REMOTE:
            case MissConstants.MISS_CMD_CLOSE_CAMERA_REMOTE:
                MiVoipUtil.sendMessageToVoip(mContext, cmd);
                break;
            default:
                break;
        }
    }

    @Override
    public int cb_miss_on_disconnect(int session) {
        L.monitor.d("cb_miss_on_disconnect: %d", session);
        if (currentSpeaker == session) {
            isSpeaker = false;
            isTalking = false;
            AudioTrackManager.getInstance().stopPlay();
            if (MiVoipUtil.isInCalling) {
                mHandler.sendEmptyMessageDelayed(MSG_HANDLE_HANG_UP, TIME_HANDLE_HANG_UP_DELAY);
            }
        }
        Iterator<MissSession> iterator = mSessionList.iterator();
        while (iterator.hasNext()) {
            MissSession missSession = iterator.next();
            if (session == missSession.getSession()) {
                iterator.remove();
                break;
            }
        }
        if (mContext != null && mSessionList.isEmpty()) {
//            CameraVideoManager.getInstance().stopRecord();
//            AudioRecordManager.getInstance().stopAudio();
            MonitorNotificationManager.getInstance().cancelMonitorNotification(mContext.getApplicationContext());
            Settings.Global.putInt(mContext.getContentResolver(), CAMERA_MONITOR_RUNNING, 0);

            mContext.getContentResolver().unregisterContentObserver(mCameraObserver);
            mContext.unregisterReceiver(mMicrophoneMuteReceiver);
        }
        return 0;
    }

    @Override
    public int cb_miss_on_error(int session, int error, byte[] user_data) {
        L.monitor.d("cb_miss_on_error: %d, %d", session, error);
        return 0;
    }

    @Override
    public void cb_miss_on_server_ready() {
        L.monitor.d("cb_miss_on_server_ready");
        CommonApiUtils.setMissServerRunning(mContext);
    }

    @Override
    public void cb_miss_on_video_data(byte[] data, int len, int isKeyFrame) {
        if (isKeyFrame == 1) {
            L.monitor.d("Receiver I Frame...");
        }
        // 插件返回的视频数据
        VideoFrameData videoFrameData = new VideoFrameData(System.currentTimeMillis());
        videoFrameData.dataBytes = data;
        videoFrameData.length = len;
        videoFrameData.isIFrame = (isKeyFrame == 1);
        mVideoQueue.offer(videoFrameData);
    }

    @Override
    public void cb_miss_on_audio_data(byte[] data, int len, int sample, int data_bits, int code_id) {
        // 插件返回的音频数据
        if (MiVoipUtil.isInCalling) {
            AudioTrackManager.getInstance().onAudioBufferReceive(data);
        }
    }

    @Override
    public void cb_miss_on_rdt_data(int session, byte[] rdt_data, int length, byte[] user_data) {
        // ignore
    }

    @Override
    public void cb_miss_encrypt_data(byte[] encrypt_param) {
        // ignore
    }

    @Override
    public void cb_miss_decrypt_data(byte[] decrypt_param) {
        // ignore
    }

    abstract void disconnectMiss();

    abstract void startSendingData(boolean start);

    private final ContentObserver mCameraObserver = new ContentObserver(new Handler(Looper.getMainLooper())) {
        @Override
        public void onChange(boolean selfChange, Uri uri) {
            mExecutorService.submit(() -> {
                if (!mPowerStatus) {
                    L.monitor.d("CAMERA_DISABLE_KEY change, but camera is power off.");
                    return;
                }
                int disabled = Settings.System.getInt(mContext.getContentResolver(), CAMERA_DISABLE_KEY, 0);
                // 获取挡板状态
                int covered = Settings.Secure.getInt(mContext.getContentResolver(), CAMERA_COVER_KEY, 0);

                int cmdCode = MissConstants.MISS_CMD_CAMERA_OPENED;
                if (covered == CAMERA_DISABLED_VALUE) {
                    cmdCode = MissConstants.MISS_CMD_CAMERA_COVERED;
                } else if (disabled == CAMERA_DISABLED_VALUE) {
                    cmdCode = MissConstants.MISS_CMD_CAMERA_CLOSED;
                }
                mMissClient.MissCmdSend(mSessionList, cmdCode, null, 0, 0);

                // 只有挡板关闭响应断开视频，挡板打开由手机端决定是否立即播放
                if (disabled == CAMERA_DISABLED_VALUE) {
                    startSendingData(disabled != CAMERA_DISABLED_VALUE);
                }
                mCameraDisabled = disabled == CAMERA_DISABLED_VALUE;
            });
        }
    };

    BroadcastReceiver mMicrophoneMuteReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent.getAction().equals(AudioManager.ACTION_MICROPHONE_MUTE_CHANGED)) {
                AudioManager audioManager = (AudioManager) mContext.getSystemService(Context.AUDIO_SERVICE);
                mExecutorService.submit(() -> {
                    mMissClient.MissCmdSend(mSessionList, audioManager.isMicrophoneMute() ? MissConstants.MISS_CMD_MICROPHONE_MUTE
                            : MissConstants.MISS_CMD_MICROPHONE_NORMAL, null, 0, 0);
                });
            }
        }
    };

    protected MissSession getMissSession(int sessionId) {
        if (mSessionList != null) {
            for (MissSession session : mSessionList) {
                if (sessionId == session.getSession()) {
                    return session;
                }
            }
        }
        return null;
    }

    public boolean isAllStopVideo() {
        if (mSessionList != null) {
            for (MissSession session : mSessionList) {
                if (session.isRunVideo()) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean isAllStopAudio() {
        if (mSessionList != null) {
            for (MissSession session : mSessionList) {
                if (session.isRunAudio()) {
                    return false;
                }
            }
        }
        return true;
    }

    protected boolean isCameraDisabled() {
        return mCameraDisabled;
    }

//    private CameraRotateManager.RotateCallback mRotateCallback = new CameraRotateManager.RotateCallback() {
//        @Override
//        public void onRotateAngle(String rotateStr) {
//            mExecutorService.submit(() -> {
//                mMissClient.MissCmdSendParams(mSessionList, MissConstants.MISS_CMD_MOTOR_RESP, rotateStr, rotateStr.length());
//            });
//        }
//    };

    // 米家挂断电话 "{\"type\":\"hang_up\",\"code\":0}"
    public void mijiaHangUp(String hangupParam) {
        mExecutorService.submit(() -> {
            mMissClient.MissCmdSendParams(mSessionList, MissConstants.MISS_CMD_CALL_STATUS_RESP, hangupParam, hangupParam.length());
        });
    }

    // 更新本地相机状态
    public void mijiaCameraStatus(boolean camIsOpen) {
        mExecutorService.submit(() -> {
            if (camIsOpen) {
                mMissClient.MissCmdSendOne(currentSpeaker, MissConstants.MISS_CMD_OPEN_CAMERA_LOCAL, null, 0, 0);
            } else {
                mMissClient.MissCmdSendOne(currentSpeaker, MissConstants.MISS_CMD_CLOSE_CAMERA_LOCAL, null, 0, 0);
            }
        });
    }

    // 接通电话，传递接通消息给插件
    public void callStart() {
        mExecutorService.submit(() -> {
            mMissClient.MissCmdSend(mSessionList, MissConstants.MISS_CMD_VOIP_START, null, 0, 0);
        });
    }

    // Spec变化，更新休眠状态
    public void updatePowerStatus(boolean status) {
        mPowerStatus = status;
    }
}
