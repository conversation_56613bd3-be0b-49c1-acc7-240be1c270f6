package com.imilab.opus;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class OpusUtils {

    public static short[] byteArrayToShortArray(byte[] byteArray) {
        short[] shortArray =  new short[byteArray.length / 2];
        ByteBuffer.wrap(byteArray).order(ByteOrder.nativeOrder()).asShortBuffer().get(shortArray);
        return shortArray;
    }

    public static byte[] shortArrayToByteArray(short[] shortArray) {
        int count = shortArray.length;
        byte[] dest = new byte[count << 1];
        for (int i = 0; i < count; i++) {
            dest[i * 2] = (byte) ((shortArray[i] & 0xFFFF) >> 0);
            dest[i * 2 + 1] = (byte) ((shortArray[i] & 0xFFFF) >> 8);
        }
        return dest;
    }
}
