package com.xiaomi.mico.persistent.cloud;

import com.xuhao.didi.core.iocore.interfaces.ISendable;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

public class SendVipData implements ISendable {

    private byte type = 0x05;
    private boolean isVip;

    public SendVipData(boolean isVip) {
        this.isVip = isVip;
    }

    @Override
    public byte[] parse() {
        int length = 4 + 1 + 1;
        ByteBuffer bb = ByteBuffer.allocate(length);
        bb.order(ByteOrder.BIG_ENDIAN);
        bb.putInt(length - 4);
        bb.put(type);
        bb.put(isVip ? (byte) 1 : (byte) 0);
        return bb.array();
    }
}
