#include <jni.h>
#include <string>
#include "log.h"

#ifdef __cplusplus
extern "C" {
#endif

#include "imimp4_ffmpeg.h"
#include "imimedia_include.h"


video_codec_id getVideoId(jint videoId) {
    switch (videoId) {
        case 0x01:
            return video_codec_h264;
        case 0x02:
            return video_codec_h265;
        default:
            return video_codec_h264;
    }
}

audio_codec_id getAudioId(jint audioId) {
    switch (audioId) {
        case 0xA:
            return audio_codec_aac;
        case 0xB:
            return audio_codec_g711a;
        case 0xC:
            return audio_codec_g711u;
        case 0xD:
            return audio_codec_opus;
        default:
            return audio_codec_aac;
    }
}

JNIEXPORT void JNICALL
Java_com_argusak_media_core_AKMp4Muxer_initMp4(JNIEnv *env, jclass thiz) {
    imi_mp4_init_ffmpeg();
}

JNIEXPORT jlong

JNICALL
Java_com_argusak_media_core_AKMp4Muxer_createFile(JNIEnv *env, jclass thiz,
                                                  jstring path,
                                                  jint container,
                                                  jint videoCodecId,
                                                  jint audioCodecId,
                                                  jint v_fps,
                                                  jint v_width,
                                                  jint v_height,
                                                  jint a_channel,
                                                  jint a_sample_rate,
                                                  jint duration,
                                                  jlong cur_time) {
    //视频本地路径
    const char *path_ = const_cast<char *>(env->GetStringUTFChars(path, nullptr));

    auto handle_ = (imimp4_ffmpeg_info_t) 0;
    LOGD("imi_mp4_create_file path:%s ,container:%x ,video:%x ,audio:%d, duration:%d, cur_time:%ld, vfps:%d, vwidth:%d, vheight:%d, achannel:%d, asamplerate:%d, handle_:%x\n",
         path_, container, videoCodecId, audioCodecId, duration, cur_time, v_fps, v_width, v_height,
         a_channel, a_sample_rate, handle_);
    int ret = imi_mp4_create_file(path_,
                                  (container_format_id) container,
                                  timestamp_correct_enable,
                                  getVideoId(videoCodecId),
                                  getAudioId(audioCodecId),
                                  v_fps,
                                  v_width,
                                  v_height,
                                  a_channel,
                                  a_sample_rate,
                                  duration,
                                  cur_time,
                                  &handle_);

    LOGD("imi_mp4_create_file ret:%d\n", ret);
    env->ReleaseStringUTFChars(path, path_);

    return reinterpret_cast<jlong>(handle_);
}

JNIEXPORT jint

JNICALL
Java_com_argusak_media_core_AKMp4Muxer_writeVideoFrameFile(JNIEnv *env, jclass thiz,
                                                           jlong handle,
                                                           jbyteArray videoFrame,
                                                           jint data_len,
                                                           jlong v_times_tamp,
                                                           jint rea_len,
                                                           jlong timestamp_ms_utc) {
    auto handle_info = (imimp4_ffmpeg_info_t) handle;
    LOGD("imi_mp4_write_video_frame ret start v_times_tamp:%lld.", v_times_tamp);
//    auto *data = static_cast< unsigned char *>(env->GetDirectBufferAddress(videoBuffer));
    unsigned char *data = (unsigned char *) env->GetByteArrayElements(videoFrame, 0);

    int ret = imi_mp4_write_video_frame(handle_info, data, data_len, v_times_tamp,
                                        (frame_type_id) rea_len, timestamp_ms_utc);

    env->ReleaseByteArrayElements(videoFrame, reinterpret_cast<jbyte *>(data), 0);
    LOGD("imi_mp4_write_video_frame ret:%d\n", ret);
    return ret;
}

JNIEXPORT jint

JNICALL
Java_com_argusak_media_core_AKMp4Muxer_writeAudioFrameFile(JNIEnv *env, jclass thiz,
                                                           jlong handle,
                                                           jbyteArray audioFrame,
                                                           jint data_len,
                                                           jlong a_times_tamp) {
    auto handle_info = (imimp4_ffmpeg_info_t) handle;
    LOGD("imi_mp4_write_audio_frame ret start a_times_tamp:%lld.", a_times_tamp);
    unsigned char *data = (unsigned char *) env->GetByteArrayElements(audioFrame, 0);

    int ret = imi_mp4_write_audio_frame(handle_info, data, data_len, a_times_tamp);

    env->ReleaseByteArrayElements(audioFrame, reinterpret_cast<jbyte *>(data), 0);
    LOGD("imi_mp4_write_audio_frame ret:%d\n", ret);
    return ret;
}


JNIEXPORT jint

JNICALL
Java_com_argusak_media_core_AKMp4Muxer_closeFile(JNIEnv *env, jclass thiz,
                                                 jlong handle) {
    auto handle_info = (imimp4_ffmpeg_info_t) handle;
    int ret = imi_mp4_close_file_for_create(handle_info);
    return ret;
}

JNIEXPORT jlong

JNICALL
Java_com_argusak_media_core_AKMp4Muxer_openFile(JNIEnv *env, jclass thiz,
                                                jstring path,
                                                jint container,
                                                jint videoCodecId,
                                                jint audioCodecId,
                                                jint v_fps,
                                                jint v_width,
                                                jint v_height,
                                                jint a_channel,
                                                jint a_sample_rate,
                                                jint a_bitrate,
                                                jint duration) {
    //视频本地路径
    const char *path_ = const_cast<char *>(env->GetStringUTFChars(path, nullptr));

    LOGD("openFile  path_  %s", path_);
    auto handle_ = (imimp4_ffmpeg_info_t) 0;

    int ret = imi_mp4_open_file(path_,
                                (container_format_id *) (container_format_id) container,
                                (video_codec_id *) (video_codec_id) videoCodecId,
                                (audio_codec_id *) (audio_codec_id) audioCodecId,
                                reinterpret_cast<unsigned int *>(&v_fps),
                                reinterpret_cast<unsigned int *>(&v_width),
                                reinterpret_cast<unsigned int *>(&v_height),
                                reinterpret_cast<unsigned int *>(&a_channel),
                                reinterpret_cast<unsigned int *>(&a_sample_rate),
                                reinterpret_cast<unsigned int *>(&a_bitrate),
                                reinterpret_cast<unsigned long long int *>(duration),
                                &handle_);

    LOGD("openFile ret:  %d", ret);

    env->ReleaseStringUTFChars(path, path_);

    return reinterpret_cast<jlong>(handle_);
}

JNIEXPORT jint

JNICALL
Java_com_argusak_media_core_AKMp4Muxer_getVideoFrame(JNIEnv *env, jclass thiz,
                                                     jlong handle,
                                                     jbyteArray videoFrame,
                                                     jint data_len,
                                                     jlong v_times_tamp,
                                                     jint frame_type) {

    auto handle_info = (imimp4_ffmpeg_info_t) handle;
    LOGD("imi_mp4_get_frame  start.");
    unsigned char *data = (unsigned char *) env->GetByteArrayElements(videoFrame, 0);

    int ret = imi_mp4_get_frame(handle_info, data, reinterpret_cast<unsigned int *>(data_len),
                                reinterpret_cast<unsigned long long int *>(v_times_tamp),
                                (frame_type_id *) (frame_type_id) frame_type);
    LOGD("imi_mp4_get_frame  end");
    env->ReleaseByteArrayElements(videoFrame, reinterpret_cast<jbyte *>(data), 0);
    return ret;
}


JNIEXPORT void JNICALL
Java_com_argusak_media_core_AKMp4Muxer_mp4Test(JNIEnv *env, jclass thiz) {
    while (1) {
        container_format_id container;
        video_codec_id video;
        audio_codec_id audio;
        unsigned int vfps = 0;
        unsigned int vwidth = 0;
        unsigned int vheight = 0;
        unsigned int vindex = 0;
        unsigned int achannel = 0;
        unsigned int asamplerate = 0;
        unsigned int abitrate = 0;
        unsigned int aindex = 0;
        unsigned long long duration = 0;
        imimp4_ffmpeg_info_t mp4_o = 0;
        LOGD("imi_mp4_open_file start.\n");
        int ret = imi_mp4_open_file("/sdcard/Android/data/com.argusak.demo/cache/record/record.mp4",
                                    &container, &video, &audio, &vfps, &vwidth,
                                    &vheight, &achannel, &asamplerate, &abitrate, &duration,
                                    &mp4_o);
        LOGD("imi_mp4_open_file ret:%d ,container:%x ,video:%x ,audio:%x, duration:%lld, vfps:%d, vwidth:%d, vheight:%d, achannel:%d, asamplerate:%d, abitrate:%d, mp4_o:%x\n",
             ret, container, video, audio, duration, vfps, vwidth, vheight, achannel, asamplerate,
             abitrate, mp4_o);
        if (ret != 0) {
            return;
        }
        imimp4_ffmpeg_info_t mp4_c = 0;
        LOGD("imi_mp4_create_file start.\n");
        ret = imi_mp4_create_file("/sdcard/Android/data/com.argusak.demo/cache/record/out.mp4",
                                  container_format_mp4,
                                  timestamp_correct_enable,
                                  video,
                                  audio,
                                  vfps,
                                  vwidth,
                                  vheight,
                                  achannel,
                                  asamplerate,
                                  1000,
                                  time(NULL),
                                  &mp4_c);
        LOGD("imi_mp4_create_file ret:%d \n", ret);
        if (ret != 0) {
            return;
        }
        FILE *file = fopen("/sdcard/Android/data/com.argusak.demo/cache/record/test.h264", "wb+");
//        FILE *file_a = fopen("c://aac.aac", "wb+");
        unsigned char *data = new unsigned char[500 * 1024];
        unsigned int data_len;
        unsigned long long timestamp = 0;
        frame_type_id frametype;
        int index = 0;
        while (imi_mp4_get_frame(mp4_o, data, &data_len, &timestamp, &frametype) == 0) {
            if (frametype == frame_type_audio) {
                //printf("imi_mp4_get_frame audio len %d\n", data_len);
                LOGE("imi_mp4_get_frame audio len %d frametype %d timestamp %llu\n", data_len,
                     frametype, timestamp);
//                fwrite(data, data_len, 1, file_a);
                imi_mp4_write_audio_frame(mp4_c, data, data_len, 0);
            } else {
                LOGE("imi_mp4_get_frame video len %d frametype %d timestamp %llu\n", data_len,
                     frametype, timestamp);
                fwrite(data, data_len, 1, file);
                LOGD("imi_mp4_write_video_frame start.\n");
                int writeVideoRet = imi_mp4_write_video_frame(mp4_c, data, data_len, timestamp,
                                                              frametype, 0);
                LOGD("imi_mp4_write_video_frame ret:%d \n", writeVideoRet);
            }
        }
//        fclose(file_a);
        fclose(file);
        imi_mp4_close_file_for_create(mp4_c);
        imi_mp4_close_file_for_open(mp4_o);
        delete[] data;
    }
}

#ifdef __cplusplus
}
#endif
extern "C"
JNIEXPORT jint JNICALL
Java_com_argusak_media_core_AKMp4Muxer_runFfmpegCmd(JNIEnv *env, jclass thiz,
                                                    jobjectArray commands) {
    return 0;
}