package com.imilab.opus;

public class OpusCodec {

    // Used to load the 'opus' library on application startup.
    static {
        System.loadLibrary("opus");
        System.loadLibrary("media_opus");
    }

    private volatile static OpusCodec mInstance;
    public static OpusCodec getInstance() {
        if (mInstance == null) {
            synchronized (OpusCodec.class) {
                if (mInstance == null) {
                    mInstance = new OpusCodec();
                }
            }
        }
        return mInstance;
    }

    /**
     * A native method that is implemented by the 'opus' native library,
     * which is packaged with this application.
     */
    public native long createEncoder(int sampleRateInHz, int channelConfig, int complexity);
    public native long createDecoder(int sampleRateInHz, int channelConfig);
    public native int encode(long pOpusEnc, short[] samples, int offset, byte[] bytes);
    public native int decode(long pOpusDec, byte[] bytes, short[] samples);
    public native void destroyEncoder(long pOpusEnc);
    public native void destroyDecoder(long pOpusDec);
}