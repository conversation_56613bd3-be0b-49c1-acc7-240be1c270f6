package com.xiaomi.mico.persistent.monitor;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.BatteryManager;
import android.os.Bundle;
import android.os.PowerManager;
import android.provider.Settings;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.CloudVipConfig;

public class CommonApiUtils {
    private static final String TAG = "CommonApiUtils";

    private static final String MONITOR_PREF_FILE = "monitorPreferences";

    private static final String MISS_SERVER_STATUS_KEY = "mi_miss_server_status";
    private static final int MISS_SERVER_STOPPED = 0;
    private static final int MISS_SERVER_STARTING = 1;
    private static final int MISS_SERVER_RUNNING = 2;
    private static final String CLOUD_VIP_STATUS_KEY = "cloud_vip_status";
    public static final String MICO_MIOT_CONNECTED = "mico_miot_connected";

    public static boolean isMonitorRunning(Context context) {
        if (context == null) {
            return false;
        }
        return Settings.Global.getInt(context.getContentResolver(), BaseMissPorting.CAMERA_MONITOR_RUNNING, 0) == 1;
    }

    public static boolean isBatteryCharging(Context context){
        if (context == null) {
            return true;
        }
        IntentFilter intentFilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
        Intent intent = context.registerReceiver(null, intentFilter);
        return intent.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1) > 0;
    }

    public static boolean isScreenOn(Context context) {
        if (context == null) {
            return true;
        }
        PowerManager powerManager = (PowerManager) context.getSystemService(Context.POWER_SERVICE);
        return powerManager.isInteractive();
    }

    public static void setMissServerStopped(Context context) {
        if (context == null) {
            return;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        sharedPreferences.edit().putInt(MISS_SERVER_STATUS_KEY, MISS_SERVER_STOPPED).apply();
    }

    public static void setMissServerStarting(Context context) {
        if (context == null) {
            return;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        sharedPreferences.edit().putInt(MISS_SERVER_STATUS_KEY, MISS_SERVER_STARTING).apply();
    }

    public static void setMissServerRunning(Context context) {
        if (context == null) {
            return;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        sharedPreferences.edit().putInt(MISS_SERVER_STATUS_KEY, MISS_SERVER_RUNNING).apply();
    }

    public static boolean isMissServerStopped(Context context) {
        if (context == null) {
            return true;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        return sharedPreferences.getInt(MISS_SERVER_STATUS_KEY, MISS_SERVER_STOPPED) == MISS_SERVER_STOPPED;
    }

    public static int getMissServerStatus(Context context) {
        if (context == null) {
            return MISS_SERVER_STOPPED;
        }
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        return sharedPreferences.getInt(MISS_SERVER_STATUS_KEY, MISS_SERVER_STOPPED);
    }

    public static void saveVipStatus(Context context, CloudVipConfig vipConfig) {
        String vipStatus = new Gson().toJson(vipConfig);
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        sharedPreferences.edit().putString(CLOUD_VIP_STATUS_KEY, vipStatus).apply();
    }

    public static CloudVipConfig getVipStatus(Context context) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        String vipStatus = sharedPreferences.getString(CLOUD_VIP_STATUS_KEY, "");
        if (TextUtils.isEmpty(vipStatus)) {
            return new CloudVipConfig();
        }
        return new Gson().fromJson(vipStatus, CloudVipConfig.class);
    }

    public static void setSpfConfig(Context context, String configKey, String configValue) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        sharedPreferences.edit().putString(configKey, configValue).apply();
    }

    public static String getSpfConfig(Context context, String configKey) {
        SharedPreferences sharedPreferences = context.getSharedPreferences(MONITOR_PREF_FILE, Context.MODE_PRIVATE);
        return sharedPreferences.getString(configKey, "");
    }

    // 是否有Voip通话中
    public static boolean isVoipRunning(Context context) {
        if (context == null){
            return false;
        }
        String pkgName = Settings.Global.getString(context.getContentResolver(), Constants.VOIP_RUNNING_PKG);
        L.monitor.i("%s isVoipRunning: %s", TAG, pkgName);
        if (TextUtils.isEmpty(pkgName)) {
            return false;
        }
        return true;
    }

    // 设置当前正在Voip通话
    public static void setVoipRunning(Context context, String packageName) {
        Settings.Global.putString(context.getContentResolver(), Constants.VOIP_RUNNING_PKG, packageName);
    }

    // 获取当前正在通话的Voip包名
    public static String getVoipRunning(Context context) {
        return Settings.Global.getString(context.getContentResolver(), Constants.VOIP_RUNNING_PKG);
    }

    public static int getMiotConnected(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), MICO_MIOT_CONNECTED, 0);
    }

    // 系统是否在升级中
    public static boolean isSystemUpdating(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), Constants.KEY_SYSTEM_UPDATE, 0) == 1;
    }

    // 休眠关闭摄像头权限
    public static void setCameraEnable(Context context, boolean enable) {
        Bundle params = new Bundle();
        params.putBoolean("enable", enable);
        context.getContentResolver().call(Uri.parse("content://com.android.systemui.provider.MICO_FEATURE"), "setCameraEnable", null, params);
    }

    // 相机是否关闭
    public static boolean getCameraStatus(Context context) {
        return Settings.System.getInt(context.getContentResolver(), "mi_camera_disable", 0) == 1;
    }

    // 设置唤醒算法状态
    public static void setSoundBoxStatus(Context context, int stopped) {
        Settings.Global.putInt(context.getContentResolver(), Constants.KEY_SOUND_BOX_STOP, stopped);
    }

    // 设置唤醒算法状态
    public static int getSoundBoxStatus(Context context) {
        return Settings.Global.getInt(context.getContentResolver(), Constants.KEY_SOUND_BOX_STOP, Constants.SOUND_BOX_ALGO_OPEN);
    }

    // 获取当前Top resume activity页面
    public static String getTopResumeActivity(Context context) {
        return Settings.Global.getString(context.getContentResolver(), Constants.GLOBAL_TOP_RESUMED_ACTIVITY);
    }

    // 设置云存录制状态 true: 录制中 false: 录制结束
    public static void setCloudRunning(Context context, boolean running) {
        Settings.Global.putInt(context.getContentResolver(), Constants.CLOUD_RUNNING, running ? 1 : 0);
    }
}
