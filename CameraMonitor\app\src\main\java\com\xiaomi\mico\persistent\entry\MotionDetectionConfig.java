package com.xiaomi.mico.persistent.entry;

public class MotionDetectionConfig {

    private boolean enable = false; //移动侦测开关
    private int alarmInterval = 10; //报警时间间隔
    private int detectionSensitivity = 0; //侦测灵敏度
    private String startTime = "00:00:00"; //移动侦测开始时间
    private String endTime = "23:59:00"; //移动侦测结束时间
    private String repetition = "[1,1,1,1,1,1,1]"; //看家时间段重复项

    public boolean isEnabled() {
        return enable;
    }

    public void setEnabled(boolean enabled) {
        this.enable = enabled;
    }

    public int getAlarmInterval() {
        return alarmInterval;
    }

    public void setAlarmInterval(int alarmInterval) {
        this.alarmInterval = alarmInterval;
    }

    public int getDetectionSensitivity() {
        return detectionSensitivity;
    }

    public void setDetectionSensitivity(int detectionSensitivity) {
        this.detectionSensitivity = detectionSensitivity;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getRepetition() {
        return repetition;
    }

    public void setRepetition(String repetition) {
        this.repetition = repetition;
    }

    @Override
    public String toString() {
        return "MotionDetectionConfig{" +
                "enabled=" + enable +
                ", alarmInterval=" + alarmInterval +
                ", detectionSensitivity=" + detectionSensitivity +
                ", startTime='" + startTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", repetition='" + repetition + '\'' +
                '}';
    }
}
