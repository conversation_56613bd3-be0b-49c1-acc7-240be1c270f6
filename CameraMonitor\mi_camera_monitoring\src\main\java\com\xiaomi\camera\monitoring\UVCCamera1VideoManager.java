package com.xiaomi.camera.monitoring;

import android.hardware.ipcamera.V1_0.IUVCCameraController;
import android.hardware.ipcamera.V1_0.IUVCFrameCallback;
import android.hardware.ipcamera.V1_0.UVC_DEVICE_ENUM;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.RemoteException;

import com.google.common.primitives.Bytes;
import com.xiaomi.camera.monitoring.entity.VideoFrameData;
import com.xiaomi.camera.monitoring.utils.L;

import java.util.ArrayList;

/**
 * 通过UVC获取Camera1数据
 */
public class UVCCamera1VideoManager {

    private final String TAG = "UVCCamera1VideoManager";

    private volatile static UVCCamera1VideoManager mInstance;

    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private VideoCameraStreamProducer mStreamProducerCamera;
    private VideoCameraStreamProducer mStreamProducerCloud;

    private IUVCCameraController mUVCameraController;
    private static final byte CAMERA1 = UVC_DEVICE_ENUM.UVC_DEV_2;

    public static UVCCamera1VideoManager getInstance() {
        if (mInstance == null) {
            synchronized (UVCCamera1VideoManager.class) {
                if (mInstance == null) {
                    mInstance = new UVCCamera1VideoManager();
                }
            }
        }
        return mInstance;
    }

    private UVCCamera1VideoManager() {
    }

    public void setVideoProducerCamera(VideoCameraStreamProducer videoProducer) {
        mStreamProducerCamera = videoProducer;
    }
    public void setVideoProducerCloud(VideoCameraStreamProducer videoProducer) {
        mStreamProducerCloud = videoProducer;
    }

    public void removeVideoProducerCamera() {
        mStreamProducerCamera = null;
    }

    public void removeVideoProducerCloud() {
        mStreamProducerCloud = null;
    }

    public void init() {
//        L.monitor.d("%s =====init=====", TAG);
        try {
            mUVCameraController = IUVCCameraController.getService(Constants.IPCAMERA_SERVICE_NAME);
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    public boolean startCamera() {
        boolean startSuccess = false;
        mHandlerThread = new HandlerThread("CAMERA1Background");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        try {
            if (mUVCameraController != null) {
                startSuccess = mUVCameraController.registerFrameCallback(CAMERA1, uvcCallback) == 0;
            } else {
                L.monitor.e("%s mUVCameraController is NULL！！！！！！", TAG);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
        return startSuccess;
    }

    public void stopCamera() {
        try {
            if (mUVCameraController != null) {
                mUVCameraController.registerFrameCallback(CAMERA1, null);
            }
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mHandlerThread = null;
            mBackgroundHandler = null;
        }
    }

    // 强制I帧
    public void requestIDR() {
        L.monitor.i("%s Request camera1 IDR", TAG);
        if (mUVCameraController != null) {
            try {
                mUVCameraController.requestIDR(CAMERA1);
            } catch (RemoteException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private final IUVCFrameCallback.Stub uvcCallback = new IUVCFrameCallback.Stub() {
        @Override
        public void onFrameReceived(boolean success, ArrayList<Byte> frameData, int frameLength,
                                    boolean isIFrame, long sequence, long timestamp) {
            mBackgroundHandler.post(() -> {
                if (!success) {
                    L.monitor.v("%s onFrameReceived error", TAG);
                    return;
                }
                byte[] byteArray = Bytes.toArray(frameData);
//                L.monitor.v("%s CAMERA1 onFrameReceived isIFrame: %s, sequence: %d, timestamp: %d", TAG, isIFrame, sequence, timestamp);
                VideoFrameData keyFrameData = new VideoFrameData(timestamp);
                keyFrameData.index = sequence;
                keyFrameData.dataBytes = byteArray;
                keyFrameData.setDataLen(frameLength);
                keyFrameData.setIFrame(isIFrame);
                offerData(keyFrameData);
            });
        }
    };

    private void offerData(VideoFrameData keyFrameData) {
        if (mStreamProducerCamera != null) {
            mStreamProducerCamera.offerData(keyFrameData);
        }
        if (mStreamProducerCloud != null) {
            mStreamProducerCloud.offerData(keyFrameData);
        }
    }
}
