#pragma once

#include "imimedia_include.h"

typedef struct imiencoder_ffmpeg_info_s *imiencoder_ffmpeg_info_t;

typedef struct _imiencoder_codec_info {
	/*video_codec_id or audio_codec_id*/unsigned int codec;
	/*pixel_format_id or sample_format_id*/unsigned int format;
	unsigned int width, height;
	unsigned int rate;
	unsigned int gop, bit_rate;
	unsigned int max_b;
	unsigned int sample_rate;
	unsigned int channels;
} imiencoder_codec_info;

void imiencoder_init_ffmpeg();

int imiencoder_open(imiencoder_codec_info *info,
	/*out*/imiencoder_ffmpeg_info_t *handle);

int imiencoder_one_frame(imiencoder_ffmpeg_info_t handle,
	endecoder_frame_info *frame);

int imiencoder_close(imiencoder_ffmpeg_info_t handle);
