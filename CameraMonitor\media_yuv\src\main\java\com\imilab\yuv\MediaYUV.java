package com.imilab.yuv;

import android.view.Surface;

public class MediaYUV {

    // Used to load the 'media_yuv' library on application startup.
    static {
        System.loadLibrary("yuv");
        System.loadLibrary("media_yuv");
    }

    private volatile static MediaYUV mInstance;
    public static MediaYUV getInstance() {
        if (mInstance == null) {
            synchronized (MediaYUV.class) {
                if (mInstance == null) {
                    mInstance = new MediaYUV();
                }
            }
        }
        return mInstance;
    }

//    public native long initSurface(Surface surface);
//    public native void showFrontSurfaceYuv(long surface, byte[] yuvData, int width, int height);
//    public native void showBgSurfaceYuv(long surface, byte[] yuvData, int width, int height);
//    public native void releaseSurface(long surface);

    public native void showFrontYuv(Surface surface, byte[] yuvData, int width, int height);
    public native void showBgYuv(Surface surface, byte[] yuvData, int width, int height);

    public native byte[] NV12ToNV21(byte[] nv12, int width, int height);
    public native byte[] NV12To420P(byte[] nv12, int width, int height);
}