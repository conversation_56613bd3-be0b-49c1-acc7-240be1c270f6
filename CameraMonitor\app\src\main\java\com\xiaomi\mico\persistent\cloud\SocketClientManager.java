package com.xiaomi.mico.persistent.cloud;

import com.xiaomi.camera.monitoring.utils.L;
import com.xuhao.didi.core.iocore.interfaces.ISendable;
import com.xuhao.didi.core.pojo.OriginalData;
import com.xuhao.didi.socket.client.sdk.OkSocket;
import com.xuhao.didi.socket.client.sdk.client.ConnectionInfo;
import com.xuhao.didi.socket.client.sdk.client.OkSocketOptions;
import com.xuhao.didi.socket.client.sdk.client.action.SocketActionAdapter;
import com.xuhao.didi.socket.client.sdk.client.connection.IConnectionManager;

public class SocketClientManager {
    private static final String TAG = "SocketClientManager";

    private static final int BUFFER_SIZE = 1024 * 32 + MDConstants.TOTAL_LEN + 16; // 和377对齐

    private IConnectionManager mConnManager;
    private ConnectionInfo mConnInfo;
    private OnMsgRecLis messageListener;
    private OnConnStatusLis connectionStatusListener;

    public interface OnMsgRecLis {
        void onMessageReceived(byte[] data);
    }

    public interface OnConnStatusLis {
        void onConnected();
        void onDisconnected(String error);
        void onError(String error);
    }

    public void setOnMsgRecLis(OnMsgRecLis listener) {
        this.messageListener = listener;
    }

    public void setOnConnStatusLis(OnConnStatusLis listener) {
        this.connectionStatusListener = listener;
    }

    SocketActionAdapter socketActionAdapter = new SocketActionAdapter() {
        @Override
        public void onSocketDisconnection(ConnectionInfo info, String action, Exception e) {
            super.onSocketDisconnection(info, action, e);
            L.monitor.d("%s onSocketDisconnection %s", TAG, e.getMessage());
            if (connectionStatusListener != null) {
                connectionStatusListener.onDisconnected(e.getMessage());
            }
        }

        @Override
        public void onSocketConnectionSuccess(ConnectionInfo info, String action) {
            super.onSocketConnectionSuccess(info, action);
            L.monitor.d("%s onSocketConnectionSuccess", TAG);
            if (connectionStatusListener != null) {
                connectionStatusListener.onConnected();
            }
        }

        @Override
        public void onSocketConnectionFailed(ConnectionInfo info, String action, Exception e) {
            super.onSocketConnectionFailed(info, action, e);
            L.monitor.d("%s onSocketConnectionFailed %s", TAG, e.getMessage());
            if (connectionStatusListener != null) {
                connectionStatusListener.onError(e.getMessage());
            }
        }

        @Override
        public void onSocketReadResponse(ConnectionInfo info, String action, OriginalData data) {
            super.onSocketReadResponse(info, action, data);
//            L.monitor.d("onSocketReadResponse:" + data.getHeadBytes().length + "---" + data.getBodyBytes().length);
            if (messageListener != null) {
                messageListener.onMessageReceived(data.getBodyBytes());
            }
        }
    };

    public void init(String serverIp, int serverPort) {
        mConnInfo = new ConnectionInfo(serverIp, serverPort);
        mConnManager = OkSocket.open(mConnInfo);
        OkSocketOptions options = mConnManager.getOption();
//        OkSocketOptions.setIsDebug(true);

        OkSocketOptions.Builder optionsBuilder = new OkSocketOptions.Builder(options);
//        optionsBuilder.setReaderProtocol(readerProtocol);
        optionsBuilder.setReadPackageBytes(BUFFER_SIZE);
        mConnManager.option(optionsBuilder.build());
        mConnManager.registerReceiver(socketActionAdapter);
        mConnManager.connect();
    }

    public void sendData(ISendable data) {
        OkSocket.open(mConnInfo).send(data);
    }

    public boolean isConnected() {
        if (mConnManager != null) {
            return mConnManager.isConnect();
        }
        return false;
    }

    public void disconnect() {
        mConnManager.unRegisterReceiver(socketActionAdapter);
        mConnManager.disconnect();
    }

}

