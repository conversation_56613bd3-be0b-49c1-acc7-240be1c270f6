package com.xiaomi.mico.persistent.utils;

import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ImageSpan;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.DialogFragment;

import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.mico.persistent.monitor.R;

import java.util.ArrayList;
import java.util.List;

import miuix.animation.Folme;
import miuix.animation.ITouchStyle;
import miuix.animation.base.AnimConfig;
import miuix.animation.utils.EaseManager;

public class MicoCommonDialog extends DialogFragment {

    private TextView mTitleTv;
    private TextView mContentTv;
    private Button mPositiveBtn;
    private Button mNegativeBtn;

    private String mTitle;
    private String mContent;
    private String mPositiveBtnSrt;
    private String mNegativeBtnStr;
    private int mContentParaSpace;
    private int mHeight;
    private CallBack mCallBack;
    private boolean mCancelable;
    private boolean mIsCenterTitle;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        if (bundle != null) {
            mTitle = bundle.getString(KEY_TITLE);
            mContent = bundle.getString(KEY_CONTENT);
            mPositiveBtnSrt = bundle.getString(KEY_OK_BUTTON_STRING);
            mNegativeBtnStr = bundle.getString(KEY_CANCEL_BUTTON_STRING);
            mContentParaSpace = bundle.getInt(KEY_CONTENT_PARA_SPACE);
            mCancelable = bundle.getBoolean(KEY_CANCELABLE);
            mIsCenterTitle = bundle.getBoolean(KEY_IS_CENTER_TITLE);
            mHeight = bundle.getInt(KEY_HEIGHT);
        }
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.layout_common_dialog, container, false);
        mTitleTv = view.findViewById(R.id.title_tv);
        mContentTv = view.findViewById(R.id.content_tv);
        mPositiveBtn = view.findViewById(R.id.ok_btn);
        mNegativeBtn = view.findViewById(R.id.cancel_btn);
        mPositiveBtn.setOnClickListener(mViewClickListener);
        if (mIsCenterTitle) mTitleTv.setGravity(Gravity.CENTER);
        if (TextUtils.isEmpty(mNegativeBtnStr)) {
            mNegativeBtn.setVisibility(View.GONE);
            ViewGroup.LayoutParams params = mPositiveBtn.getLayoutParams();
            params.width = ViewGroup.LayoutParams.MATCH_PARENT;
            mPositiveBtn.setLayoutParams(params);
        } else {
            mNegativeBtn.setOnClickListener(mViewClickListener);
        }
        AnimConfig animConfig = new AnimConfig();
        animConfig.setEase(new EaseManager.InterpolateEaseStyle(EaseManager.EaseStyleDef.DECELERATE).setDuration(150L));
        configAnim(mPositiveBtn, animConfig);
        configAnim(mNegativeBtn, animConfig);
        return view;
    }

    public void configAnim(View view, AnimConfig animConfig) {
        Folme.clean(view);
        Folme.useAt(view)
                .touch()
                .setAlpha(0.7f, ITouchStyle.TouchType.DOWN)
                .setAlpha(1.0f, ITouchStyle.TouchType.UP)
                .handleTouchOf(view, false, animConfig);
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        Dialog dialog = super.onCreateDialog(savedInstanceState);
        dialog.getWindow().setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        if (!mCancelable) {
            dialog.setCanceledOnTouchOutside(false);
            setCancelable(false);
        }
        return dialog;
    }

    @Override
    public void onResume() {
        super.onResume();
        updateInfo();
    }

    private void updateInfo() {
        int width = getResources().getDimensionPixelSize(R.dimen.dialog_view_width);
        int height = mHeight;
        if (height == 0) {
            height = getResources().getDimensionPixelSize(R.dimen.dialog_view_height);
        }
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(width, height);
        }
        if (mContentParaSpace <= 0) {
            mContentParaSpace = getResources().getDimensionPixelSize(R.dimen.dialog_view_content_para_space);
        }
        mTitleTv.setText(mTitle);
        if (mContentParaSpace != 0) {
            mContentTv.setText(getSpannableStringWithParaSpace(getContext(), mContent, mContentParaSpace, mContentTv.getLineHeight()));
        } else {
            mContentTv.setText(mContent);
        }
        mPositiveBtn.setText(mPositiveBtnSrt);
        mNegativeBtn.setText(mNegativeBtnStr);
    }

    public void setCallBack(CallBack mCallBack) {
        this.mCallBack = mCallBack;
    }

    private final View.OnClickListener mViewClickListener = new View.OnClickListener() {
        @Override
        public void onClick(View v) {
            int id = v.getId();
            if (id == R.id.ok_btn) {
                if (mCallBack != null) mCallBack.onFirBtnClick(MicoCommonDialog.this);
            } else if (id == R.id.cancel_btn) {
                if (mCallBack != null) mCallBack.onSecBtnClick(MicoCommonDialog.this);
            }
            dismiss();
        }
    };

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        mCallBack = null;
    }

    /**
     * 回调接口
     */
    public interface CallBack {
        //确认
        void onFirBtnClick(DialogFragment dialog);

        //取消
        void onSecBtnClick(DialogFragment dialog);
    }

    public static class Builder {
        private String mTitle;
        private String mContent;
        private String mPositiveBtnSrt;
        private String mNegativeBtnStr;
        private int mContentParaSpace;
        private boolean mIsCancelable = true;
        private boolean mIsTitleCenter;
        private int mHeight;

        public MicoCommonDialog build() {
            return newInstance(
                mTitle,
                mContent,
                mPositiveBtnSrt,
                mNegativeBtnStr,
                mContentParaSpace,
                mIsCancelable,
                mHeight,
                mIsTitleCenter
            );
        }

        public Builder enableCenterTitle() {
            mIsTitleCenter = true;
            return this;
        }

        public Builder setTitle(String title) {
            mTitle = title;
            return this;
        }

        public Builder setContent(String content) {
            mContent = content;
            return this;
        }

        public Builder setHeight(int height) {
            mHeight = height;
            return this;
        }

        public Builder setPositiveButton(String btnSrt) {
            mPositiveBtnSrt = btnSrt;
            return this;
        }

        public Builder setNegativeButton(String cancelBtnStr) {
            mNegativeBtnStr = cancelBtnStr;
            return this;
        }

        public Builder setContentParaSpace(int contentParaSpace) {
            mContentParaSpace = contentParaSpace;
            return this;
        }

        public Builder setCancelable(boolean cancelable) {
            mIsCancelable = cancelable;
            return this;
        }
    }

    private static final String KEY_TITLE = "KEY_TITLE";
    private static final String KEY_CONTENT = "KEY_CONTENT";
    private static final String KEY_OK_BUTTON_STRING = "KEY_BUTTON_STRING";
    private static final String KEY_CANCEL_BUTTON_STRING = "CANCEL_KEY_BUTTON_STRING";
    private static final String KEY_CONTENT_PARA_SPACE = "KEY_CONTENT_PARA_SPACE";
    private static final String KEY_CANCELABLE = "KEY_CANCELABLE";
    private static final String KEY_HEIGHT = "KEY_HEIGHT";
    private static final String KEY_IS_CENTER_TITLE = "is_center_title";

    public static MicoCommonDialog newInstance(
            String title,
            String content,
            String positiveButtonStr,
            String cancelButtonStr,
            int contentParaSpace,
            boolean cancelable,
            int height,
            boolean isTitleCenter) {
        MicoCommonDialog dialog = new MicoCommonDialog();
        Bundle bundle = new Bundle(6);
        bundle.putString(KEY_TITLE, title);
        bundle.putString(KEY_CONTENT, content);
        bundle.putString(KEY_OK_BUTTON_STRING, positiveButtonStr);
        bundle.putString(KEY_CANCEL_BUTTON_STRING, cancelButtonStr);
        bundle.putInt(KEY_CONTENT_PARA_SPACE, contentParaSpace);
        bundle.putInt(KEY_HEIGHT, height);
        bundle.putBoolean(KEY_CANCELABLE, cancelable);
        bundle.putBoolean(KEY_IS_CENTER_TITLE, isTitleCenter);
        dialog.setArguments(bundle);
        return dialog;
    }

    private SpannableString getSpannableStringWithParaSpace(
            Context context,
            String text,
            int paragraphSpacing,
            int lineHeight) {
        SpannableString spanString = new SpannableString(text);
        if (context == null || paragraphSpacing <= 0 || lineHeight <= 0 || text == null || !text.contains(Constants.PARAGRAPH_SPACE_FLAG)) {
            return spanString;
        }
        int previousIndex = text.indexOf(Constants.PARAGRAPH_SPACE_FLAG);
        //记录每个段落开始的index，第一段没有，从第二段开始
        List<Integer> nextParagraphBeginIndexes = new ArrayList<>();
        nextParagraphBeginIndexes.add(previousIndex);
        while (previousIndex != -1) {
            previousIndex = text.indexOf(Constants.PARAGRAPH_SPACE_FLAG, previousIndex + 2);
            if (previousIndex != -1) {
                nextParagraphBeginIndexes.add(previousIndex);
            }
        }

        //把\r替换成透明长方形（宽:1px，高：字高+段距）
        Drawable d = ContextCompat.getDrawable(context, R.drawable.paragraph_space);
        //int强转部分为：行高 - 行距 + 段距
        d.setBounds(0, 0, 1, (paragraphSpacing + lineHeight));
        for (int index : nextParagraphBeginIndexes) {
            // \r在String中占一个index
            spanString.setSpan(new ImageSpan(d), index + 1, index + 2, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }
        return spanString;
    }
}
