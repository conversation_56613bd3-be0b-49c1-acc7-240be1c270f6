package com.xiaomi.camera.monitoring;

import android.graphics.ImageFormat;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CaptureRequest;
import android.media.Image;
import android.media.ImageReader;
import android.os.Handler;
import android.os.HandlerThread;
import android.util.Range;
import android.view.Surface;

import androidx.annotation.NonNull;

import com.xiaomi.camera.monitoring.utils.L;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

public class CloudYUVData {

    private final String TAG = "CloudYUVData";

    private volatile static CloudYUVData mInstance;

    private CameraDevice mCameraDevice;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private CameraCaptureSession mCameraCaptureSession;
    private ImageReader mImageReader;

    public int mVideoWidth;
    public int mVideoHeight;
    private VideoCameraYuvProducer mVideoProducer;
    private boolean mStartCallback = false;

    public static CloudYUVData getInstance() {
        if (mInstance == null) {
            synchronized (CloudYUVData.class) {
                if (mInstance == null) {
                    mInstance = new CloudYUVData();
                }
            }
        }
        return mInstance;
    }

    public CloudYUVData() {
    }

    public void init(CameraDevice camera, VideoCameraYuvProducer videoProducer, int videoWidth, int videoHeight) {
        mCameraDevice = camera;
        mVideoProducer = videoProducer;
        mVideoWidth = videoWidth;
        mVideoHeight = videoHeight;
    }

    Runnable recordRunnable = new Runnable() {
        @Override
        public void run() {
            if (mCameraDevice == null) {
                L.monitor.e("%s Please init first!!!!!!", TAG);
                return;
            }
            createCaptureSession(mCameraDevice);
        }

    };

    public synchronized void startPreview() {
        mHandlerThread = new HandlerThread("CloudYuvBackground");
        mHandlerThread.start();
        mBackgroundHandler = new Handler(mHandlerThread.getLooper());
        mBackgroundHandler.post(recordRunnable);
    }

    public synchronized void stopPreview() {
        try {
            mStartCallback = false;
            if (mImageReader != null) {
                mImageReader.setOnImageAvailableListener(null, null);
            }
            if (mCameraCaptureSession != null) {
                mCameraCaptureSession.stopRepeating();
                mCameraCaptureSession.abortCaptures();
                mCameraCaptureSession.close();
            }
            if (mCameraDevice != null) {
                mCameraDevice.close();
            }
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
        } catch (Exception e) {
            L.monitor.e("%s destroy thread throw exception: %s", TAG, e.getMessage());
        } finally {
            mHandlerThread = null;
            mBackgroundHandler = null;
            mCameraCaptureSession = null;
            mCameraDevice = null;
            mImageReader = null;
        }
    }

    public void startCallback() {
        mStartCallback = true;
    }

    public void stopCallback() {
        mStartCallback = false;
    }

    private void createCaptureSession(CameraDevice camera) {
        mImageReader = ImageReader.newInstance(mVideoWidth, mVideoHeight,
                ImageFormat.YUV_420_888, 4);
        mImageReader.setOnImageAvailableListener(reader -> {
            Image image = reader.acquireLatestImage();
            if (image == null) {
                return;
            }
            if (mStartCallback) {
                L.monitor.d("%s Callback YUV data start", TAG);
                byte[] nv12_data = processImage(image);
                mVideoProducer.offerData(nv12_data);
            }
            image.close();
        }, mBackgroundHandler);

        List<Surface> surfaces = new ArrayList<>();
        surfaces.add(mImageReader.getSurface());

        try {
            final CaptureRequest.Builder captureRequest = camera.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            captureRequest.addTarget(mImageReader.getSurface());
            Range<Integer> targetFpsRange = new Range<>(10, 10);
            captureRequest.set(CaptureRequest.CONTROL_AE_TARGET_FPS_RANGE, targetFpsRange);
            camera.createCaptureSession(surfaces, new CameraCaptureSession.StateCallback() {
                @Override
                public void onConfigured(@NonNull CameraCaptureSession session) {
                    try {
                        mCameraCaptureSession = session;
                        mCameraCaptureSession.setRepeatingRequest(captureRequest.build(), null, null);
                    } catch (Exception e) {
                        L.monitor.e("%s set repeating request throw exception: %s", TAG, e.getMessage());
                    }
                }

                @Override
                public void onConfigureFailed(@NonNull CameraCaptureSession session) {
                    L.monitor.w("%s create capture session onConfigureFailed.", TAG);
                }
            }, null);
        } catch (Exception e) {
            L.monitor.e("%s create capture session throw exception: %s", TAG, e.getMessage());
        }
    }

    private byte[] processImage(Image image) {
        // 获取 YUV 数据的三个平面
        Image.Plane[] planes = image.getPlanes();
        ByteBuffer yBuffer = planes[0].getBuffer();
        ByteBuffer uBuffer = planes[1].getBuffer();
        ByteBuffer vBuffer = planes[2].getBuffer();

        int ySize = yBuffer.remaining();
        int uSize = uBuffer.remaining();
        int vSize = vBuffer.remaining();

        byte[] yuvData = new byte[ySize + uSize + vSize];
        yBuffer.get(yuvData, 0, ySize);
        uBuffer.get(yuvData, ySize, uSize);
        vBuffer.get(yuvData, ySize + uSize, vSize);
        return yuvData;
    }
}
