package com.xiaomi.mico.persistent.cloud;

import android.content.Context;
import android.hardware.ipcamera.V1_0.UVC_ALGO_ENUM;
import android.hardware.ipcamera.V1_0.UVC_EVENT_ENUM;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.provider.Settings;
import android.text.TextUtils;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.AudioRecordManager;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.camera.monitoring.UVCCamera0VideoManager;
import com.xiaomi.camera.monitoring.UVCCamera1VideoManager;
import com.xiaomi.camera.monitoring.UVCCameraController;
import com.xiaomi.camera.monitoring.UVCCameraManager;
import com.xiaomi.camera.monitoring.UVCEventManager;
import com.xiaomi.camera.monitoring.VideoCameraStreamProducer;
import com.xiaomi.camera.monitoring.entity.VideoFrameData;
import com.xiaomi.camera.monitoring.utils.L;
import com.xiaomi.mico.persistent.entry.AiDetectionConfig;
import com.xiaomi.mico.persistent.entry.CloudUploadEntry;
import com.xiaomi.mico.persistent.entry.CloudVipConfig;
import com.xiaomi.mico.persistent.entry.MotionDetectionConfig;
import com.xiaomi.mico.persistent.entry.PreUploadEntry;
import com.xiaomi.mico.persistent.func.FamilyCareManager;
import com.xiaomi.mico.persistent.monitor.CameraMonitorManager;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;
import com.xiaomi.mico.persistent.spec.SpecConstants;
import com.xiaomi.mico.persistent.spec.SpecEventSend;
import com.xiaomi.mico.persistent.utils.FileUtils;
import com.xiaomi.mico.persistent.utils.MDUtils;

import java.io.File;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;

/**
 * 移动侦测功能
 * Vip用户，检测到事件后，延长录制30秒视频，上传到云端，如无事件继续触发停止上传
 * 非Vip用户，检测到事件后，录制9秒视频，上传到云端，录像间隔受看家助手间隔控制
 */
public class MotionDetectionLocal extends MotionDetection {
    private static final String TAG = "MotionDetection";

    private Context mContext;
    private final int ONE_MINUTE = 60 * 1000;
    private final int ONE_HOUR = 60 * ONE_MINUTE;

    private boolean mSystemUpdating = false; // 系统是否在升级中
    private boolean mPowerStatus = true; // 休眠状态开关，ture:不休眠 false:休眠
    private MotionDetectionConfig mMDConfig;
    private AiDetectionConfig mAIConfig;
    private CloudVipConfig mVipConfig;

    private int mGetDomainFailTimes = 0;
    private boolean isRecording = false;
    private Handler mBackgroundHandler;
    private HandlerThread mHandlerThread;
    private CloudFileUpload mCloudFileUpload;
    private CloudVipSwitch mCloudVipSwitch;
    private CloudRecord mCloudRecord;
    private long mStartRecordTime = 0;
    private boolean mCloudUploadSwitch = false;
    private long[] mAiEventOccured = new long[3];

    protected ExecutorService mCloudSingleExe = Executors.newFixedThreadPool(2);
    private final Handler mHandler = new Handler(Looper.getMainLooper());

    @Override
    public void initMotionDetection(Context context) {
        this.mContext = context;
        mCloudFileUpload = new CloudFileUpload(mUploadCallback);
        mCloudVipSwitch = new CloudVipSwitch(mVipSwitchCallback);
        mCloudRecord = new CloudRecord();
        // 注册UVC事件回调
        UVCEventManager.getInstance().init(this::doEventOccurred);
        //获取保存的Vip状态
        mVipConfig = CommonApiUtils.getVipStatus(mContext);
        mCloudRecord.setIsVip(mVipConfig.isVip());
        L.monitor.i("%s Vip: %s", TAG, mVipConfig);
        delOldCloudFile();
    }

    @Override
    public void initStatus() {
        // 获取当前系统是否在升级中
        mSystemUpdating = CommonApiUtils.isSystemUpdating(mContext);
        L.monitor.d("%s initStatus SystemUpdating: %s", TAG, mSystemUpdating);
        // 如果的当前是vip用户，或者云存到期，获取云存vip和开关状态
        if (mVipConfig.isVip() || System.currentTimeMillis() > mVipConfig.getEndTime()) {
            mCloudSingleExe.execute(() -> mCloudVipSwitch.getDomainUrl());
        }
        // 获取移动侦测配置
        updateMDConfig();
        // AI检测配置
        updateAIConfig();
        mHandler.post(() -> {
            // 启事件监听
            UVCEventManager.getInstance().startEvent();
        });
    }

    @Override
    public boolean isRecording() {
        L.monitor.i("%s isRecording: %s", TAG, isRecording);
        return isRecording;
    }

    @Override
    public boolean isVip() {
        return mVipConfig.isVip();
    }

    // AI事件，录制并上传视频
    public void uploadVideo() {
        startRecord(SpecConstants.EVENT_TYPE_AI);
    }

    private synchronized void startRecord(String eventType) {
        // 如果当前是休眠状态，不录制视频
        if (!mPowerStatus) {
//            L.monitor.d("%s Camera is power off, do not record", TAG);
            return;
        }
        // Vip用户并且云存开关是关闭状态，不上传云存视频。非Vip用户需要上传9s的云存视频
        if (!mCloudUploadSwitch && mVipConfig.isVip()) {
//            L.monitor.d("%s Cloud upload switch is close", TAG);
            return;
        }
        // 系统升级中，不录制视频
        if (mSystemUpdating) {
//            L.monitor.d("%s System is Updating.", TAG);
            return;
        }
        // 非AI事件，判断是否满足录像间隔，AI事件立刻录制
        if (!SpecConstants.EVENT_TYPE_AI.equals(eventType)) {
            // vip过期或者还没开通vip，按照配置的录像间隔录制
            long curTime = System.currentTimeMillis();
            if (curTime > mVipConfig.getEndTime()) {
                int alarmInterval = mMDConfig.getAlarmInterval();
                if (curTime - mStartRecordTime < alarmInterval * ONE_MINUTE) {
//                    Log.d(TAG, "startRecord -->mMDInterval:" + alarmInterval + " -->mStartRecordTime:" + mStartRecordTime);
                    return;
                }
                if (!mMDConfig.isEnabled()) {
//                    L.monitor.d("%s MD config is false", TAG);
                    return;
                }
            }
        }
//        L.monitor.d("%s StartRecord eventType: %s", TAG, eventType);
        // 云存设置事件类型，并上报AI事件
        mCloudRecord.setEventType(eventType);

        if (!isRecording) {
            delOldCloudFile();
            isRecording = true;
            //创建上传线程
            mHandlerThread = new HandlerThread("UploadBackground");
            mHandlerThread.start();
            mBackgroundHandler = new Handler(mHandlerThread.getLooper());
            // 获取录制的开始时间
            mStartRecordTime = System.currentTimeMillis();
            mCloudRecord.startRecord(mStartRecordTime);
            // 开启请求云存上传
            mBackgroundHandler.post(() -> mCloudFileUpload.getDomainUrl());
            //设置Sub视频数据回调Callback，用于缩略图
            UVCCamera1VideoManager.getInstance().setVideoProducerCloud(mVideoSubProducer);
            //辅码流强制I帧
            UVCCamera1VideoManager.getInstance().requestIDR();
            //设置Main视频数据回调Callback，用于视频录制
            UVCCamera0VideoManager.getInstance().setVideoProducerCloud(mVideoMainProducer);
            if (!UVCCameraManager.getInstance().isCameraRunning()) {
                boolean startSuccess = UVCCameraManager.getInstance().startCamera();
                if (!startSuccess) {
                    stopRecord();
                    return;
                }
            }
            //主码流强制I帧
            UVCCamera0VideoManager.getInstance().requestIDR();
            //设置视频数据回调Callback
            AudioRecordManager.getInstance().setAudioProducerCloud(mAudioProducer);
            if (CameraMonitorManager.getInstance().isAllStopAudio()) { //如果当前没有音频数据推送,启动音频流
                AudioRecordManager.getInstance().startAudio();
            }
            // 设置云存录制中标记
            CommonApiUtils.setCloudRunning(mContext, true);
            // 判断是否禁用小爱语控
//            SoundBoxManager.getInstance().startCloudControl(0);
        }
    }

    private synchronized void stopRecord() {
        L.monitor.d("%s stopRecord isRecording: %s", TAG, isRecording);
        if (isRecording) {
            UVCCamera1VideoManager.getInstance().removeVideoProducerCloud();
            UVCCamera0VideoManager.getInstance().removeVideoProducerCloud();
            if (CameraMonitorManager.getInstance().isAllStopVideo()) {
                UVCCameraManager.getInstance().stopCamera();
            }
            AudioRecordManager.getInstance().removeAudioProducerCloud();
            if (CameraMonitorManager.getInstance().isAllStopAudio()) { //如果当前没有音频数据推送,关闭音频流
                AudioRecordManager.getInstance().stopAudio();
            }
            mCloudRecord.stopRecord(System.currentTimeMillis());
            // 设置云存录制结束标记
            CommonApiUtils.setCloudRunning(mContext, false);
//            SoundBoxManager.getInstance().stopCloudControl();
            if (mBackgroundHandler != null) {
                mBackgroundHandler.removeCallbacksAndMessages(null);
            }
            if (null != mHandlerThread && Thread.State.RUNNABLE == mHandlerThread.getState()) {
                mHandlerThread.quit();
                mHandlerThread.interrupt();
            }
            isRecording = false;
        }
    }

    private void doEventOccurred(int event) {
        // 家人守护,触发人形事件
        if (UVC_EVENT_ENUM.EVENT_PERSON_DETECT == event) {
            FamilyCareManager.getInstance().peopleEventOccurred();
        }
        // 检查AI配置是否允许该事件
        if (!isEventAllowed(event)) {
//            L.monitor.v("%s Do not allow event: %d", TAG, event);
            return;
        }
        // 获取事件类型，开始录制
        String eventType = getEventType(event);
        startRecord(eventType);
        // AI Event事件上报独立计时，最短间隔一分钟上报一次
        long curTime = System.currentTimeMillis();
        if (curTime - mAiEventOccured[event] > ONE_MINUTE) {
            mAiEventOccured[event] = curTime;
            SpecEventSend.eventAIReport(eventType);
        }
    }

    private boolean isEventAllowed(int event) {
        switch (event) {
            case UVC_EVENT_ENUM.EVENT_PERSON_DETECT:
                return mAIConfig.isHumanDetection();
            case UVC_EVENT_ENUM.EVENT_SCENE_CHANGED:
                return mAIConfig.isMoveDetection();
            case UVC_EVENT_ENUM.EVENT_CRY_DETECT:
                return mAIConfig.isBabyCryDetection();
        }
        return false; // 默认情况下不允许其它事件
    }

    private String getEventType(int event) {
        switch (event) {
            case UVC_EVENT_ENUM.EVENT_PERSON_DETECT:
                return SpecConstants.EVENT_TYPE_PEOPLE_MOTION;
            case UVC_EVENT_ENUM.EVENT_SCENE_CHANGED:
                return SpecConstants.EVENT_TYPE_OBJECT_MOTION;
            case UVC_EVENT_ENUM.EVENT_CRY_DETECT:
                return SpecConstants.EVENT_TYPE_BABY_CRY;
        }
        return SpecConstants.EVENT_TYPE_OBJECT_MOTION;
    }

    private final VideoCameraStreamProducer mVideoMainProducer = new VideoCameraStreamProducer() {
        @Override
        public void offerData(VideoFrameData keyFrameData) {
            if (mCloudRecord != null) {
                mCloudRecord.offerMainVideoData(keyFrameData);
            }
        }
    };

    private final VideoCameraStreamProducer mVideoSubProducer = new VideoCameraStreamProducer() {
        @Override
        public void offerData(VideoFrameData keyFrameData) {
            if (mCloudRecord != null) {
                mCloudRecord.offerSubVideoData(keyFrameData);
            }
        }
    };

    private final AudioRecordManager.AudioRecordStreamProducer mAudioProducer = (audioFrameData) -> {
        if (mCloudRecord != null) {
            mCloudRecord.offerAudioData(audioFrameData);
        }
    };

    private final CloudVipSwitch.UploadCallback mVipSwitchCallback = new CloudVipSwitch.UploadCallback() {

        @Override
        public void getDomainUrlFailed() {
            // 获取Domain url失败,3s后尝试重新获取
            mHandler.postDelayed(() -> mCloudVipSwitch.getDomainUrl(), 3000);
        }

        @Override
        public void vipStatusCallback(CloudVipConfig vipConfig) {
            L.monitor.i("%s Vip config: %s", TAG, vipConfig);
            CommonApiUtils.saveVipStatus(mContext, vipConfig);
            mVipConfig = vipConfig;
            mCloudRecord.setIsVip(mVipConfig.isVip());
        }

        @Override
        public void cloudSwitchCallback(boolean cloudSwitch) {
            L.monitor.i("%s Cloud switch: %s", TAG, cloudSwitch);
            mCloudUploadSwitch = cloudSwitch;
        }
    };

    private final CloudFileUpload.UploadCallback mUploadCallback = new CloudFileUpload.UploadCallback() {

        @Override
        public void getDomainUrlFailed() {
            // 获取Domain url失败,尝试重新获取
            mGetDomainFailTimes++;
            if (mGetDomainFailTimes < 3) {
                mCloudFileUpload.getDomainUrl();
            } else {
                stopRecord(); // 三次获取失败，停止录制
                mGetDomainFailTimes = 0;
            }
        }

        @Override
        public void getDomainUrlSuccess(String security, String iv) {
            mGetDomainFailTimes = 0;
        }

        @Override
        public void preUploadCallback(PreUploadEntry preUploadEntry) {
            nextUpload(preUploadEntry);
        }

        @Override
        public void doNextUpload(PreUploadEntry preUploadEntry) {
            nextUpload(preUploadEntry);
        }

        @Override
        public void uploadError(int code, boolean isEnd) {
            L.monitor.e("%s uploadError code: %d", TAG, code);
            if (code == CloudErrorCode.ERROR_CODE_400221) { // 错误的vip状态，尝试重新获取
                mCloudVipSwitch.getDomainUrl();
            } else if (code == CloudErrorCode.ERROR_CODE_PRE_UPLOAD_ERROR
                    || code == CloudErrorCode.ERROR_CODE_FILE_UPLOAD_ERROR
                    || code == CloudErrorCode.ERROR_CODE_META_UPLOAD_ERROR) {
                L.monitor.e("%s CloudFileUpload occur error", TAG);
            }
            stopRecord();
        }
    };

    private void nextUpload(PreUploadEntry preUploadEntry) {
        mBackgroundHandler.post(()->{
            LinkedBlockingQueue<CloudUploadEntry> uploadVideoFiles = mCloudRecord.getUploadVideoFiles();
            try {
                boolean isVip = mVipConfig.isVip();
                // 上传过程中，vip用户关闭云存开关 或 设备休眠，停止录制上传
                if ((isVip && !mCloudUploadSwitch) || !mPowerStatus) {
                    stopRecord();
                    return;
                }
                CloudUploadEntry cloudUploadEntry = uploadVideoFiles.take();
                String uploadVideo = cloudUploadEntry.getVideoName();
                String eventType = cloudUploadEntry.getEventType();
                int offset = cloudUploadEntry.getOffset();
                boolean isEnd = cloudUploadEntry.isEnd();
                boolean ignoreEvent = cloudUploadEntry.isIgnoreEvent();
                boolean nextFieldId = cloudUploadEntry.isNextFieldId();
                // 云存挂测，有概率会获取不到缩略图的情况
                String uploadImg = MDUtils.getUploadImage(uploadVideo, mCloudRecord.getUploadImgFiles());
                File imgFile = new File(uploadImg);
                File videoFile = new File(uploadVideo);
                L.monitor.i("%s nextUpload imgFile:%s, videoFile:%s", TAG, imgFile.getName(), videoFile.getName());
                L.monitor.i("%s nextUpload eventType:%s, offset:%d, isEnd:%s, ignoreEvent:%s, nextFieldId:%s",
                        TAG, eventType, offset, isEnd, ignoreEvent, nextFieldId);
                mCloudFileUpload.encryptFileAndUpload(preUploadEntry, isVip, imgFile,
                        videoFile, offset, isEnd, eventType, ignoreEvent);
                if (nextFieldId) {
                    mCloudFileUpload.getDomainUrl();
                    return;
                }
                if (isEnd) {
                    stopRecord();
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
    }

    // Spec变化，更新休眠状态
    @Override
    public void updatePowerStatus(boolean status) {
        L.monitor.d("%s Update power status: %s", TAG, status);
        mPowerStatus = status;
    }

    // 更新移动侦测配置
    @Override
    public void updateMDConfig() {
        // 获取移动侦测配置
        String motionDetection = Settings.Global.getString(mContext.getContentResolver(), Constants.KEY_MOTION_DETECTION_CONFIG);
//        L.monitor.d("%s updateMDConfig motionDetection: %s", TAG, motionDetection);
        if (!TextUtils.isEmpty(motionDetection)) {
            mMDConfig = new Gson().fromJson(motionDetection, MotionDetectionConfig.class);
        } else {
            mMDConfig = new MotionDetectionConfig();
        }
        L.monitor.i("%s MDConfig: %s", TAG, mMDConfig.toString());
        // 更新看着助手时间配置
        MDUtils.updateMDUtilsConfig(mMDConfig);
    }

    // 更新AI检测配置
    @Override
    public void updateAIConfig() {
        // AI检测配置
        String aimDetection = Settings.Global.getString(mContext.getContentResolver(), Constants.KEY_AI_DETECTION_CONFIG);
//        L.monitor.d("%s updateAIConfig aimDetection: %s", TAG, aimDetection);
        if (!TextUtils.isEmpty(aimDetection)) {
            mAIConfig = new Gson().fromJson(aimDetection, AiDetectionConfig.class);
        } else {
            mAIConfig = new AiDetectionConfig();
        }
        L.monitor.i("%s AIConfig: %s", TAG, mAIConfig.toString());
        // 更新算法开关状态到UVC
        UVCCameraController.getInstance().setAlgoSwitch(UVC_ALGO_ENUM.ALGO_PERSON_DETECT, true);
        UVCCameraController.getInstance().setAlgoSwitch(UVC_ALGO_ENUM.ALGO_SCENE_CHANGED, true);
        UVCCameraController.getInstance().setAlgoSwitch(UVC_ALGO_ENUM.ALGO_CRY_DETECT, true);
        // 更新灵敏度到UVC
        UVCCameraController.getInstance().SetAlgoSensitivity(UVC_ALGO_ENUM.ALGO_SCENE_CHANGED, mAIConfig.getSensitivity());
    }

    // 更新云存开关配置
    @Override
    public void updateCloudSwitch() {
        // 云存开关状态变化
        int cloudSwitch = Settings.Global.getInt(mContext.getContentResolver(), Constants.KEY_CLOUD_SWITCH_CONFIG, 0);
        L.monitor.d("%s updateCloudSwitch cloudSwitch: %d", TAG, cloudSwitch);
        if (mVipConfig.isVip() && System.currentTimeMillis() < mVipConfig.getEndTime()) { //开启或者关闭云存开关
            mCloudUploadSwitch = cloudSwitch == 1;
        } else { //更新Vip状态
            mCloudVipSwitch.getDomainUrl();
        }
    }

    public void updateSystemUpdate() {
        mSystemUpdating = CommonApiUtils.isSystemUpdating(mContext);
        L.monitor.d("%s updateSystemUpdate SystemUpdating: %s", TAG, mSystemUpdating);
        if (mSystemUpdating) {
            stopRecord();
        }
    }

    // 删除一个小前无效云存视频文件
    private void delOldCloudFile() {
        mCloudSingleExe.execute(() -> {
            long currentTime = System.currentTimeMillis() - ONE_HOUR;
            String cloudPath = FileUtils.getCloudRecordPath();
            List<String> files = FileUtils.getAllChildFileName(cloudPath);
            for (String file : files) {
                if (file.startsWith(Constants.ENCRYPT_PREFIX)) {
                    if (file.endsWith(Constants.MP4_PREFIX)) {
                        String mp4File = file.replace(Constants.ENCRYPT_PREFIX, "").replace(Constants.MP4_PREFIX, "");
                        if (Long.parseLong(mp4File) < currentTime) {
                            L.monitor.d("%s Delete filename: %s", TAG, file);
                            FileUtils.delFile(cloudPath + File.separator + file);
                        }
                    } else if(file.endsWith(Constants.JPEG_PREFIX)) {
                        String jpgFile = file.replace(Constants.ENCRYPT_PREFIX, "").replace(Constants.JPEG_PREFIX, "");
                        if (Long.parseLong(jpgFile) < currentTime) {
                            L.monitor.d("%s Delete filename: %s", TAG, file);
                            FileUtils.delFile(cloudPath + File.separator + file);
                        }
                    }
                } else {
                    try {
                        if (Long.parseLong(file) < currentTime) {
                            L.monitor.d("%s Delete filename: %s", TAG, file);
                            FileUtils.deleteDir(cloudPath + File.separator + file);
                        }
                    } catch (Exception e) {
                        // 解析异常,删除其它无关文件
                        L.monitor.e("%s Del %s error: %s", TAG, file, e.getMessage());
                        File errorfile = new File(cloudPath + File.separator + file);
                        if (errorfile.isDirectory()) {
                            FileUtils.deleteDir(cloudPath + File.separator + file);
                        } else {
                            FileUtils.delFile(cloudPath + File.separator + file);
                        }
                    }

                }
            }
        });
    }
}
