package com.xiaomi.mico.persistent.func;

import android.content.Context;
import android.hardware.ipcamera.V1_0.Direction;
import android.hardware.ipcamera.V1_0.ICameraRotateController;
import android.hardware.ipcamera.V1_0.IMotorControlCallback;
import android.hardware.ipcamera.V1_0.IStepCallback;
import android.os.Handler;
import android.os.Looper;
import android.os.RemoteException;
import android.text.TextUtils;
import android.util.Log;

import com.google.gson.Gson;
import com.xiaomi.camera.monitoring.Constants;
import com.xiaomi.mico.persistent.entry.RotateEntry;
import com.xiaomi.mico.persistent.monitor.CommonApiUtils;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * 电机控制
 */
public class CameraRotateManagerBackup {

    private final String TAG = "CameraRotateManager";


    private Context mContext;
    private final String SPF_KEY_ROTATE = "spf_key_rotate";
    private final int TIMEOUT_DELAY = 320; // 同步ipc延时时间
    private final int TIMEOUT_BACK_TO_SLEEP_DELAY = 500; // 背向休眠触发延时时间
    private volatile static CameraRotateManagerBackup mInstance;
    private ICameraRotateController mRotateController;
    private int mStepPerCycle = 4080; // 每次转动的步数
    private int mLatestStep = 0;
    private long accumulatedSteps = 0;

    // {"ret":0, "angle":50,"elevation":50}
    // 电机默认位置，横向居中，纵向居中
    private final int DEFAULT_ANGLE_VALUE = 50;
    private int mAngle = DEFAULT_ANGLE_VALUE;
    private int mElevation = DEFAULT_ANGLE_VALUE;
    private boolean mIsInReset = false; // 是否在校准电机或者背向休眠中

    private final int ROTATE_LEFT = 1;
    private final int ROTATE_RIGHT = 2;
    private RotateCallback mRotateCallback;

    private Handler mHandler = new Handler(Looper.getMainLooper());

    public static CameraRotateManagerBackup getInstance() {
        if (mInstance == null) {
            synchronized (CameraRotateManagerBackup.class) {
                if (mInstance == null) {
                    mInstance = new CameraRotateManagerBackup();
                }
            }
        }
        return mInstance;
    }

    private CameraRotateManagerBackup() {}

    public void initRotate(Context context) {
        Log.d(TAG, "=====init=====");
        this.mContext = context;
        try {
            mRotateController =  ICameraRotateController.getService(Constants.IPCAMERA_SERVICE_NAME);
            if (mRotateController != null) {
                //获取摄像头每旋转⼀圈(360°)  对应电机前进步数 .应用侧会据此计算电机转动⼀步对应度数。
                mStepPerCycle = mRotateController.getStepPerCycle() / 100; // App对应范围0-100
                Log.i(TAG, "getStepPerCycle:" + mStepPerCycle);
//                // 获取转速范围，转速需要结合转动步数动态调整
//                SpeedRange speedRange = mRotateController.getMotorSpeedRange();
//                Log.i(TAG, "speedRange.min:" + speedRange.min + " -->speedRange.max:" + speedRange.max);
//                int range = speedRange.max - speedRange.min;
//                int rotateSpeed = speedRange.min + range / 8;
//                // 设置转速
//                mRotateController.setRotateSpeed(rotateSpeed, new IMotorControlCallback.Stub() {
//                    @Override
//                    public void onSuccess(int data) throws RemoteException {
//                        Log.i(TAG, "setRotateSpeed --> onSuccess --> data:" + data);
//                    }
//
//                    @Override
//                    public void onFail(int errNo, String errMsg) throws RemoteException {
//                        Log.i(TAG, "setRotateSpeed --> onFail --> errNo:" + errNo + " -->errMsg:" + errMsg);
//                    }
//                });
//                resetMotorStep(false);
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * saveRotateStr: 保存转动位置
     */
    private void clearCommandQueue(boolean saveRotateStr) {
        Log.d(TAG, "=====clearCommandQueue===== " + saveRotateStr);
        try {
            if (mRotateController != null) {
                mRotateController.clearCommandQueue(new IMotorControlCallback.Stub() {
                    @Override
                    public void onSuccess(int latestSteps) throws RemoteException {
                        mLatestStep = latestSteps;
                        // 转动结束，保存当前电机位置
                        if (saveRotateStr) {
                            String rotateStr = getRotateStr(mAngle, mElevation);
                            CommonApiUtils.setSpfConfig(mContext, SPF_KEY_ROTATE, rotateStr);
                        }
                        Log.i(TAG, "clearCommandQueue --> onSuccess --> data:" + latestSteps);
                    }

                    @Override
                    public void onFail(int errNo, String errMsg) throws RemoteException {
                        Log.i(TAG, "clearCommandQueue --> onFail --> errNo:" + errNo + " -->errMsg:" + errMsg);
                    }
                });
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 电机重置校准
     * fromCloud: 是否来自云端校准
     */
    public void resetMotorStep(boolean firstBoot, boolean fromCloud) {
        Log.d(TAG, "=====resetMotorStep=====");
        mIsInReset = true;
        try {
            clearCommandQueue(false);
            if (mRotateController != null) {
                // 电机校准
                mRotateController.resetMotorStep(firstBoot, new IMotorControlCallback.Stub() {
                    @Override
                    public void onSuccess(int latestSteps) throws RemoteException {
                        mLatestStep = latestSteps;
                        if (fromCloud) {
                            String rotateStr = getRotateStr(DEFAULT_ANGLE_VALUE, DEFAULT_ANGLE_VALUE);
                            CommonApiUtils.setSpfConfig(mContext, SPF_KEY_ROTATE, rotateStr);
                            if (mRotateCallback != null) {
                                mRotateCallback.onRotateAngle(rotateStr);
                            }
                            mAngle = DEFAULT_ANGLE_VALUE;
                            mIsInReset = false;
                            Log.d(TAG, "=====resetMotorStep===== completed");
                        } else {
                            // 恢复上次转动位置
                            String rotateStr = CommonApiUtils.getSpfConfig(mContext, SPF_KEY_ROTATE);
                            Log.d(TAG, "=====resetMotorStep===== rotateStr:" + rotateStr);
                            if (!TextUtils.isEmpty(rotateStr)) {
                                RotateEntry rotateEntry = new Gson().fromJson(rotateStr, RotateEntry.class);
                                mAngle = rotateEntry.getAngle();
                                if (rotateEntry.getAngle() > DEFAULT_ANGLE_VALUE) {
                                    rotateRight(mStepPerCycle * (rotateEntry.getAngle() - DEFAULT_ANGLE_VALUE), mIsInReset);
                                } else if (rotateEntry.getAngle() < DEFAULT_ANGLE_VALUE) {
                                    rotateLeft(mStepPerCycle * (DEFAULT_ANGLE_VALUE - rotateEntry.getAngle()), mIsInReset);
                                } else {
                                    mIsInReset = false;
                                    Log.d(TAG, "=====resetMotorStep===== completed");
                                }
                            } else {
                                mAngle = DEFAULT_ANGLE_VALUE;
                                mIsInReset = false;
                                Log.d(TAG, "=====resetMotorStep===== completed");
                            }
                        }
                        Log.i(TAG, "resetMotorStep onSuccess --> data:" + latestSteps);
                    }

                    @Override
                    public void onFail(int errNo, String errMsg) throws RemoteException {
                        Log.i(TAG, "resetMotorStep onFail --> errNo:" + errNo + " -->errMsg:" + errMsg);
                    }
                });
            }
        } catch (RemoteException e) {
            throw new RuntimeException(e);
        }
    }

    public void rotateRight(int step, boolean isInReset) {
        try {
            if (mRotateController != null) {
                mRotateController.rotateCamera(step, Direction.RIGHT, new IStepCallback.Stub() {
                    @Override
                    public void stepProgress(int latestSteps, int remainingCapacity) throws RemoteException {
//                        Log.v(TAG, "rotateRight --> latestSteps:" + latestSteps + " --> remainingCapacity:" + remainingCapacity);
                        if (isInReset) {
//                            Log.d(TAG, "rotateRight --> latestSteps:" + latestSteps + " --> step:" + step + " -> mLatestStep:" + mLatestStep);
                            if (Math.abs(latestSteps - mLatestStep) == step) {
                                mLatestStep = latestSteps;
                                mIsInReset = false;
                                Log.d(TAG, "=====resetMotorStep===== completed");
                            }
                            return;
                        }
                        long steps = latestSteps - mLatestStep;
                        accumulatedSteps += steps;
                        if (accumulatedSteps >= mStepPerCycle) {
                            if (mAngle < 100) {
                                mAngle += accumulatedSteps / mStepPerCycle;
                            } else {
                                mAngle = 100;
                            }
                            accumulatedSteps %= mStepPerCycle;
                            if (mRotateCallback != null) {
                                mRotateCallback.onRotateAngle(getRotateStr(mAngle, mElevation));
                            }
                            Log.i("TAG", "rotateRight --> mAngle:" + mAngle);
                        }
                        mLatestStep = latestSteps;
//                        Log.i("TAG", "steps:" + steps + " accumulatedSteps:" + accumulatedSteps + " mAngle:" + mAngle);
                    }
                });
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void rotateLeft(int step, boolean isInReset) {
        try {
            if (mRotateController != null) {
                mRotateController.rotateCamera(step, Direction.LEFT, new IStepCallback.Stub() {
                    @Override
                    public void stepProgress(int latestSteps, int remainingCapacity) throws RemoteException {
//                        Log.v(TAG, "rotateLeft --> latestSteps:" + latestSteps + " --> remainingCapacity:" + remainingCapacity);
                        if (isInReset) {
//                            Log.d(TAG, "rotateRight --> latestSteps:" + latestSteps + " --> step:" + step + " -> mLatestStep:" + mLatestStep);
                            if (Math.abs(mLatestStep - latestSteps) == step) {
                                mLatestStep = latestSteps;
                                mIsInReset = false;
                                Log.d(TAG, "=====resetMotorStep===== completed");
                            }
                            return;
                        }
                        long steps = mLatestStep - latestSteps; // 注意这里的方向
                        accumulatedSteps += steps;
                        if (accumulatedSteps >= mStepPerCycle) {
                            if (mAngle > 0) {
                                mAngle -= accumulatedSteps / mStepPerCycle;
                            } else {
                                mAngle = 0;
                            }
                            accumulatedSteps %= mStepPerCycle;
                            if (mRotateCallback != null) {
                                mRotateCallback.onRotateAngle(getRotateStr(mAngle, mElevation));
                            }
                            Log.i("TAG", "rotateLeft --> mAngle:" + mAngle);
                        }
                        mLatestStep = latestSteps;
//                        Log.i("TAG", "steps:" + steps + " accumulatedSteps:" + accumulatedSteps + " mAngle:" + mAngle);
                    }
                });
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private Runnable mTimeoutRunnable = new Runnable() {

        @Override
        public void run() {
            clearCommandQueue(true);
        }
    };

    public void missRotateData(String params) {
        try {
            if (mIsInReset) {
                Log.d(TAG, "Rotate is in resetting");
                return;
            }
            mHandler.removeCallbacks(mTimeoutRunnable);
            mHandler.postDelayed(mTimeoutRunnable, TIMEOUT_DELAY);
            JSONObject qualityJson = new JSONObject(params);
            int operation = qualityJson.getInt("operation");
            if (ROTATE_LEFT == operation) {
                rotateLeft(mStepPerCycle * 2, false);
            } else if (ROTATE_RIGHT == operation) {
                rotateRight(mStepPerCycle * 2, false);
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    // 背向休眠
    public void backToSleep(boolean isSleep) {
        mIsInReset = true;
        mHandler.postDelayed(() -> {
            // 获取当前电机位置
            String rotateStr = CommonApiUtils.getSpfConfig(mContext, SPF_KEY_ROTATE);
            Log.d(TAG, "=====backToSleep===== rotateStr:" + rotateStr);
            // 开启休眠开关，向左转到底休眠。关闭休眠开关，向右转动恢复
            if (isSleep) {
                if (!TextUtils.isEmpty(rotateStr)) {
                    RotateEntry rotateEntry = new Gson().fromJson(rotateStr, RotateEntry.class);
                    if (rotateEntry.getAngle() != 0) {
                        rotateLeft(mStepPerCycle * rotateEntry.getAngle(), mIsInReset);
                    } else {
                        mIsInReset = false;
                    }
                } else {
                    rotateLeft(mStepPerCycle * (DEFAULT_ANGLE_VALUE), mIsInReset);
                }
            } else {
                if (!TextUtils.isEmpty(rotateStr)) {
                    RotateEntry rotateEntry = new Gson().fromJson(rotateStr, RotateEntry.class);
                    rotateRight(mStepPerCycle * rotateEntry.getAngle(), mIsInReset);
                } else {
                    rotateRight(mStepPerCycle * (DEFAULT_ANGLE_VALUE), mIsInReset);
                }
            }
        }, TIMEOUT_BACK_TO_SLEEP_DELAY);
    }

    private String getRotateStr(int angle, int elevation) {
        RotateEntry entry = new RotateEntry(angle, elevation);
        String rotateStr = new Gson().toJson(entry);
        return rotateStr;
    }

    public void setRotateCallback(RotateCallback rotateCallback) {
        mRotateCallback = rotateCallback;
    }

    public interface RotateCallback {
        void onRotateAngle(String rotateStr);
    }
}
